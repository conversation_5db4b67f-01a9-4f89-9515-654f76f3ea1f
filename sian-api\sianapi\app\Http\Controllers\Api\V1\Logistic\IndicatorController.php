<?php

namespace App\Http\Controllers\Api\V1\Logistic;

use App\Models\Presentation;
use App\Models\Product;
use App\Models\ProductLink;
use App\Models\Recipe;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use App\Models\Fake\Currency;
use App\Models\Procedures\SpGetProductPresentations;
use App\Http\Controllers\Controller;
use PhpOffice\PhpSpreadsheet\IOFactory;


class IndicatorController extends Controller
{

    private $recipeCache = [];
    private $presentationCache = [];
    private $productLinkCache = [];
    private $productCache = [];

    public const SUPPLY_RECIPE = 'supplyRecipe';
    public const DERIVED_PRODUCT = 'derivedProduct';
    public const RAW_MATERIAL = 'rawMaterial';
    public const SUPPLY = 'supply';


    public function getReposition(Request $request)
    {
        // Aumentar timeout dinámicamente basado en días de reposición
        $daysOfReposition = intval($request->input('daysOfReposition', 7));
        $timeoutSeconds = 300; // Base: 5 minutos

        if ($daysOfReposition > 7) {
            // Para rangos largos: 10 minutos + 1 minuto por cada día adicional
            $timeoutSeconds = 600 + (($daysOfReposition - 7) * 60);
            Log::info("Increased timeout to {$timeoutSeconds} seconds for {$daysOfReposition} days of reposition");
        }

        set_time_limit($timeoutSeconds);
        ini_set('max_execution_time', $timeoutSeconds);

        try {
            $validate = Validator::make($request->all(), [
                "page" => "required|integer",
                "pageSize" => "required|integer",
                'store' => 'required|integer',
                'daysOfReposition' => 'required|integer',
                'daysMinStock' => 'required|integer',
                'daysToOrder' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_providerIds = "";
            $s_divisionIds = "";
            $s_lineIds = "";
            $s_sublineIds = "";
            $s_productIds = "";
            $s_sort = "";
            $s_order = "";
            $s_break_scale = "";
            $s_rotation_scale = "";
            $s_product_type = $request->input('mode', 'Merc');

            if ($request->has('provider')) {
                $s_providerIds = $request->input('provider');
            }
            if ($request->has('division')) {
                $s_divisionIds = $request->input('division');
            }
            if ($request->has('line')) {
                $s_lineIds = $request->input('line');
            }
            if ($request->has('subline')) {
                $s_sublineIds = $request->input('subline');
            }
            if ($request->has(key: 'product')) {
                $s_productIds = $request->input('product');
            }

            if ($request->has('sort')) {
                $s_sort = $request->input('sort');
            }
            if ($request->has('order')) {
                $s_order = $request->input('order');
            }

            if ($request->has('breakScale')) {
                $break_scale_array = explode(',', $request->input('breakScale'));
                $s_break_scale = "'" . implode("','", $break_scale_array) . "'";
            }

            if ($request->has('rotationScale')) {
                $rotation_scale_array = explode(',', $request->input('rotationScale'));
                $s_rotation_scale = "'" . implode("','", $rotation_scale_array) . "'";
            }

            if ($s_product_type !== 'Food') {

                $a_params = [
                    ':xprovider_ids' => $s_providerIds,
                    ':xdivision_ids' => $s_divisionIds,
                    ':xline_ids' => $s_lineIds,
                    ':xsubline_ids' => $s_sublineIds,
                    ':xproduct_ids' => $s_productIds,
                    ':xsort' => $s_sort,
                    ':xorder' => $s_order,
                    ':xlimit' => 0,
                    ':xoffset' => 0,
                    ':xrotation_scale' => $s_rotation_scale,
                    ':xbreak_scale' => $s_break_scale,
                    ':xproduct_type' => $s_product_type
                ];


                // Log de llamada al SP
                Log::info("call sp_get_reposition_products(" .
                    $a_params[':xprovider_ids'] . ", " .
                    $a_params[':xdivision_ids'] . ", " .
                    $a_params[':xline_ids'] . ", " .
                    $a_params[':xsubline_ids'] . ", " .
                    $a_params[':xproduct_ids'] . ", " .
                    $a_params[':xrotation_scale'] . ", " .
                    $a_params[':xbreak_scale'] . ", " .
                    $a_params[':xsort'] . ", " .
                    $a_params[':xorder'] . ", " .
                    $a_params[':xlimit'] . ", " .
                    $a_params[':xoffset'] . ", " .
                    $a_params[':xproduct_type'] . ")");

                $a_total_result = DB::select(
                    "call sp_get_reposition_products (:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xrotation_scale, :xbreak_scale, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                    $a_params
                );

                $i_pageSize = intval($request->input('pageSize'));
                $i_page = intval($request->input('page'));
                $i_offset = ($i_page - 1) * $i_pageSize;
                $i_totalItems = count($a_total_result);

                $a_params[':xlimit'] = $i_pageSize;
                $a_params[':xoffset'] = $i_offset;

                // Log de llamada al SP (segunda llamada con paginación)
                Log::info("call sp_get_reposition_products(" .
                    $a_params[':xprovider_ids'] . ", " .
                    $a_params[':xdivision_ids'] . ", " .
                    $a_params[':xline_ids'] . ", " .
                    $a_params[':xsubline_ids'] . ", " .
                    $a_params[':xproduct_ids'] . ", " .
                    $a_params[':xrotation_scale'] . ", " .
                    $a_params[':xbreak_scale'] . ", " .
                    $a_params[':xsort'] . ", " .
                    $a_params[':xorder'] . ", " .
                    $a_params[':xlimit'] . ", " .
                    $a_params[':xoffset'] . ", " .
                    $a_params[':xproduct_type'] . ")");

                $a_result = DB::select(
                    "call sp_get_reposition_products(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xrotation_scale, :xbreak_scale, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                    $a_params
                );

                $ids = [];
                foreach ($a_result as $o_row) {
                    $ids[] = $o_row->product_id;
                }

                $a_productPresentations = SpGetProductPresentations::getAssociative(
                    SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                    $ids,
                    Currency::PEN,
                    0
                );



                if ($s_product_type === 'Merc2') {
                    $a_analisys_params = [
                        ':xstore_id' => $request->input('store'),
                        ':xdays_min_stock' => $request->input('daysMinStock'),
                        ':xdays_reposition' => $request->input('daysOfReposition'),
                        ':xprovider_ids' => "",
                        ':xdivision_ids' => "",
                        ':xline_ids' => "",
                        ':xsubline_ids' => "",
                        ':xproduct_ids' => "",
                        ':xscale_rotation' => "",
                        ':xexpiration_alert' => "",
                        ':xobsolete_alert' => "",
                        ':xestimated_order' => "",
                        ':xdays_to_order' => intval($request->input('daysToOrder', 1)),
                        ':xsort' => "",
                        ':xorder' => "",
                        ':xlimit' => 0,
                        ':xoffset' => 0,
                        ':xproduct_type' => "Merc2"
                    ];

                    // Log de llamada al SP
                    Log::info("call sp_get_reposition_analysis(" .
                        $a_analisys_params[':xstore_id'] . ", " .
                        $a_analisys_params[':xdays_min_stock'] . ", " .
                        $a_analisys_params[':xdays_reposition'] . ", " .
                        $a_analisys_params[':xprovider_ids'] . ", " .
                        $a_analisys_params[':xdivision_ids'] . ", " .
                        $a_analisys_params[':xline_ids'] . ", " .
                        $a_analisys_params[':xsubline_ids'] . ", " .
                        $a_analisys_params[':xproduct_ids'] . ", " .
                        $a_analisys_params[':xscale_rotation'] . ", " .
                        $a_analisys_params[':xexpiration_alert'] . ", " .
                        $a_analisys_params[':xobsolete_alert'] . ", " .
                        $a_analisys_params[':xestimated_order'] . ", " .
                        $a_analisys_params[':xdays_to_order'] . ", " .
                        $a_analisys_params[':xsort'] . ", " .
                        $a_analisys_params[':xorder'] . ", " .
                        $a_analisys_params[':xlimit'] . ", " .
                        $a_analisys_params[':xoffset'] . ", " .
                        $a_analisys_params[':xproduct_type'] . ")");

                    $a_analisys = DB::select(
                        "call sp_get_reposition_analysis (:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                        $a_analisys_params
                    );

                    $grouped_analisys = [];

                    foreach ($a_analisys as $item) {
                        $productId = $item->product_id ?? null;
                        if (!$productId)
                            continue;

                        $grouped_analisys[$productId][] = $item;
                    }
                }

                foreach ($a_result as &$o_row) {
                    $o_row->product_id = intval($o_row->product_id);
                    $o_row->presentations = $a_productPresentations[$o_row->product_id] ?? [];

                    if ($s_product_type === 'Merc2') {
                        if (!isset($o_row->unit_quantity_order)) {
                            $o_row->unit_quantity_order = 0;
                        }

                        $analisysRows = $grouped_analisys[$o_row->product_id] ?? [];

                        foreach ($analisysRows as $analysis) {
                            $o_row->unit_quantity_order += floatval($analysis->unit_quantity_order ?? 0);
                        }

                        $o_row->analisys = $analisysRows;
                    }
                }

                $a_response = $a_response = [
                    'success' => false,
                    'data' => []
                ];

                if ($i_totalItems > 0) {

                    $a_response = [
                        'success' => true,
                        'pagination' => [
                            'page' => $i_page,
                            'pageSize' => $i_pageSize,
                            'totalRecords' => $i_totalItems,
                            'totalPages' => ceil($i_totalItems / $i_pageSize)
                        ],
                        'data' => $a_result
                    ];
                }
                return response()->json($a_response);
            } else {
                $a_supplys = $this->getSupplyData($request);

                return response()->json([
                    'success' => true,
                    'data' => $a_supplys,
                ]);
            }
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getTraceAsString());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getTraceAsString(),
            ], 500);
        }
    }

    public function getRepositionByProduct(Request $request)
    {
        // Aumentar timeout dinámicamente basado en días de reposición
        $daysOfReposition = intval($request->input('daysOfReposition', 7));
        $timeoutSeconds = 300; // Base: 5 minutos

        if ($daysOfReposition > 7) {
            // Para rangos largos: 10 minutos + 1 minuto por cada día adicional
            $timeoutSeconds = 600 + (($daysOfReposition - 7) * 60);
            Log::info("Increased timeout to {$timeoutSeconds} seconds for {$daysOfReposition} days of reposition in getRepositionByProduct");
        }

        set_time_limit($timeoutSeconds);
        ini_set('max_execution_time', $timeoutSeconds);

        try {
            $validate = Validator::make($request->all(), [
                "page" => "required|integer",
                "pageSize" => "required|integer",
                'store' => 'required|integer',
                'daysOfReposition' => 'required|integer',
                'daysMinStock' => 'required|integer',
                'daysToOrder' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_providerIds = "";
            $s_divisionIds = "";
            $s_lineIds = "";
            $s_sublineIds = "";
            $s_scaleRotation = "";
            $s_expirationAlert = "2";
            $s_obsoleteAlert = "2";
            $s_estimatedOrder = "";
            $i_daysToOrder = 1;
            $s_sort = "";
            $s_order = "";
            $s_productIds = "";

            if ($request->has('provider')) {
                $s_providerIds = $request->input('provider');
            }
            if ($request->has('division')) {
                $s_divisionIds = $request->input('division');
            }
            if ($request->has('line')) {
                $s_lineIds = $request->input('line');
            }
            if ($request->has('subline')) {
                $s_sublineIds = $request->input('subline');
            }
            if ($request->has(key: 'product')) {
                $s_productIds = $request->input('product');
            }
            if ($request->has('scaleRotation')) {
                $s_scaleRotation = $request->input('scaleRotation');
            }
            if ($request->has('expirationAlert')) {
                $s_expirationAlert = $request->input('expirationAlert');
            }
            if ($request->has('obsoleteAlert')) {
                $s_obsoleteAlert = $request->input('obsoleteAlert');
            }
            if ($request->has('estimatedOrder')) {
                $s_estimatedOrder = $request->input('estimatedOrder');
            }
            if ($request->has('daysToOrder')) {
                $i_daysToOrder = intval($request->input('daysToOrder'));
                if ($i_daysToOrder <= 0) {
                    $i_daysToOrder = 1;
                }
            }
            if ($request->has('sort')) {
                $s_sort = $request->input('sort');
            }
            if ($request->has('order')) {
                $s_order = $request->input('order');
            }


            $a_params = [
                ':xstore_id' => $request->input('store'),
                ':xdays_min_stock' => $request->input('daysMinStock'),
                ':xdays_reposition' => $request->input('daysOfReposition'),
                ':xprovider_ids' => $s_providerIds,
                ':xdivision_ids' => $s_divisionIds,
                ':xline_ids' => $s_lineIds,
                ':xsubline_ids' => $s_sublineIds,
                ':xproduct_ids' => $s_productIds,
                ':xscale_rotation' => $s_scaleRotation,
                ':xexpiration_alert' => $s_expirationAlert,
                ':xobsolete_alert' => $s_obsoleteAlert,
                ':xestimated_order' => $s_estimatedOrder,
                ':xdays_to_order' => $i_daysToOrder,
                ':xsort' => $s_sort,
                ':xorder' => $s_order,
                ':xlimit' => 0,
                ':xoffset' => 0,
                ':xproduct_type' => $request->input('mode', 'Merc')
            ];


            // Log de llamada al SP
            Log::info("call sp_get_reposition_analysis(" .
                $a_params[':xstore_id'] . ", " .
                $a_params[':xdays_min_stock'] . ", " .
                $a_params[':xdays_reposition'] . ", " .
                $a_params[':xprovider_ids'] . ", " .
                $a_params[':xdivision_ids'] . ", " .
                $a_params[':xline_ids'] . ", " .
                $a_params[':xsubline_ids'] . ", " .
                $a_params[':xproduct_ids'] . ", " .
                $a_params[':xscale_rotation'] . ", " .
                $a_params[':xexpiration_alert'] . ", " .
                $a_params[':xobsolete_alert'] . ", " .
                $a_params[':xestimated_order'] . ", " .
                $a_params[':xdays_to_order'] . ", " .
                $a_params[':xsort'] . ", " .
                $a_params[':xorder'] . ", " .
                $a_params[':xlimit'] . ", " .
                $a_params[':xoffset'] . ", " .
                $a_params[':xproduct_type'] . ")");

            $a_total_result = DB::select(
                "call sp_get_reposition_analysis (:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                $a_params
            );

            $i_pageSize = intval($request->input('pageSize'));
            $i_page = intval($request->input('page'));
            $i_offset = ($i_page - 1) * $i_pageSize;
            $i_totalItems = count($a_total_result);

            $a_params[':xlimit'] = $i_pageSize;
            $a_params[':xoffset'] = $i_offset;

            // Log de llamada al SP (segunda llamada con paginación)
            Log::info("call sp_get_reposition_analysis(" .
                $a_params[':xstore_id'] . ", " .
                $a_params[':xdays_min_stock'] . ", " .
                $a_params[':xdays_reposition'] . ", " .
                $a_params[':xprovider_ids'] . ", " .
                $a_params[':xdivision_ids'] . ", " .
                $a_params[':xline_ids'] . ", " .
                $a_params[':xsubline_ids'] . ", " .
                $a_params[':xproduct_ids'] . ", " .
                $a_params[':xscale_rotation'] . ", " .
                $a_params[':xexpiration_alert'] . ", " .
                $a_params[':xobsolete_alert'] . ", " .
                $a_params[':xestimated_order'] . ", " .
                $a_params[':xdays_to_order'] . ", " .
                $a_params[':xsort'] . ", " .
                $a_params[':xorder'] . ", " .
                $a_params[':xlimit'] . ", " .
                $a_params[':xoffset'] . ", " .
                $a_params[':xproduct_type'] . ")");

            $a_result = DB::select(
                "call sp_get_reposition_analysis (:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
                $a_params
            );

            $a_response = $a_response = [
                'success' => true,
                'data' => $a_result,
            ];

            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getRotation(Request $request)
    {
        try {
            $validate = Validator::make($request->all(), [
                'store' => 'required|integer',
                'product' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $a_params = [
                ':xstore_id' => intval($request->input('store')),
                ':xproduct_id' => intval($request->input('product')),
                //':xlimit' => 0,
                //':xoffset' => 0,
            ];

            // Log de llamada al SP
            Log::info("call sp_get_current_rotation(" .
                $a_params[':xstore_id'] . ", " .
                $a_params[':xproduct_id'] . ")");

            $a_result = DB::select(
                "call sp_get_current_rotation (:xstore_id, :xproduct_id)",
                $a_params
            );

            $a_response = $a_response = [
                'success' => false,
                'data' => []
            ];

            if (count($a_result) > 0) {
                $a_response = [
                    'success' => true,
                    'data' => [
                        'values' => $a_result
                    ]
                ];
            }
            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function exportOcExcel(Request $request)
    {
        try {
            $data = $request->input('data');

            $templatePath = storage_path('app/templates/RepositionOC.xlsx');
            $spreadsheet = IOFactory::load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            $rowStart = 2;
            foreach ($data as $rowIndex => $rowData) {
                $columnStart = 'A';
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_id']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['provider']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['equivalence']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['measure_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['6']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['8']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['9']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['10'] ?? 0);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['total_quantity']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['unit_price']);
                $columnStart++;

                $rowStart++;
            }

            // Crear el escritor de Excel y definir el nombre del archivo
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $dateTime = now()->format('Y-m-d_H-i-s');
            $filename = "Reposicion_{$dateTime}.xlsx";

            // Retornar el archivo para su descarga
            return response()->stream(
                function () use ($writer) {
                    $writer->save('php://output');
                },
                200,
                [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Content-FileName' => $filename,
                ]
            );
        } catch (\Exception $ex) {
            Log::error('Error exporting to Excel: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error exporting to Excel: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function exportOtaExcel(Request $request)
    {
        try {
            $data = $request->input('data');

            $templatePath = storage_path('app/templates/RepositionOTA.xlsx');
            $spreadsheet = IOFactory::load($templatePath);
            $sheet = $spreadsheet->getActiveSheet();

            $rowStart = 2;
            foreach ($data as $rowIndex => $rowData) {
                $columnStart = 'A';
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_id']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['product_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['equivalence']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['measure_name']);
                $columnStart++;
                $sheet->setCellValue("{$columnStart}{$rowStart}", $rowData['quantity']);
                $columnStart++;
                $rowStart++;
            }

            // Crear el escritor de Excel y definir el nombre del archivo
            $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
            $dateTime = now()->format('Y-m-d_H-i-s');
            $filename = "Reposicion_{$dateTime}.xlsx";

            // Retornar el archivo para su descarga
            return response()->stream(
                function () use ($writer) {
                    $writer->save('php://output');
                },
                200,
                [
                    'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'Content-Disposition' => 'attachment; filename="' . $filename . '"',
                    'Content-FileName' => $filename,
                ]
            );
        } catch (\Exception $ex) {
            Log::error('Error exporting to Excel: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error exporting to Excel: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getPlainSupplys(
        $product_id,
        $quantity,
        $store_id,
        $main_recipe,
        $main_recipe_quantity,
        array $excludedTypes = [],
        $onlyFromDerived = false,
        $inheritedFromDerived = false,
        &$derivedProductsCapture = []
    ) {
        $recipe = $this->getRecipe($product_id);
        $supplys = $this->getProductLinks($product_id);

        switch (true) {
            case ($recipe && $supplys->isNotEmpty()):
                $type = self::SUPPLY_RECIPE;
                break;
            case (!$recipe && $supplys->isNotEmpty()):
                $type = self::DERIVED_PRODUCT;
                break;
            case (!$recipe && $supplys->isEmpty()):
                $type = self::SUPPLY;
                break;
            default:
                $type = 'unknown';
        }


        if ($type === self::DERIVED_PRODUCT) {
            $derivedProductsCapture[$product_id] = ($derivedProductsCapture[$product_id] ?? 0) + $quantity;
        }

        if (in_array($type, $excludedTypes)) {
            return [];
        }

        $results = [];
        $factor = 1;

        if ($recipe) {
            $output_quantity = (float) $recipe->pres_quantity * (float) $recipe->equivalence;
            $factor = $output_quantity > 0 ? ($quantity / $output_quantity) : 1;
        } else {
            $presentation = $this->getDefaultPresentation($product_id);
            if ($presentation && intval($presentation->equivalence) > 0) {
                $factor = $presentation->equivalence / $quantity;
            }
        }

        if ($supplys->isNotEmpty()) {
            foreach ($supplys as $supply) {
                $requiredQty = $supply->quantity * $factor;

                $subResults = $this->getPlainSupplys(
                    $supply->product_id,
                    $requiredQty,
                    $store_id,
                    $main_recipe,
                    $main_recipe_quantity,
                    $excludedTypes,
                    $onlyFromDerived,
                    $onlyFromDerived && $type === self::DERIVED_PRODUCT,
                    $derivedProductsCapture
                );

                foreach ($subResults as $id => $sub) {
                    if (!isset($results[$id])) {
                        $results[$id] = $sub;
                    } else {
                        $results[$id]['unit_quantity_proyected'] += $sub['unit_quantity_proyected'];

                        foreach ($sub['recipes'] as $rid => $recipeData) {
                            if (!isset($results[$id]['recipes'][$rid])) {
                                $results[$id]['recipes'][$rid] = $recipeData;
                            } else {
                                $results[$id]['recipes'][$rid]['quantity'] += $recipeData['quantity'];
                                $results[$id]['recipes'][$rid]['recipe_quantity'] += $recipeData['recipe_quantity'];
                            }
                        }

                        foreach ($sub['quantities_by_store'] as $sid => $qty) {
                            $results[$id]['quantities_by_store'][$sid] = ($results[$id]['quantities_by_store'][$sid] ?? 0) + $qty;
                        }
                    }
                }
            }
        } else {
            if ($onlyFromDerived && !$inheritedFromDerived) {
                return [];
            }

            $recipes = [];

            // Validar que $main_recipe no sea null antes de acceder a sus propiedades
            if ($main_recipe && !isset($recipes[$main_recipe->recipe_id])) {
                $recipes[$main_recipe->recipe_id] = [
                    'parent_product' => $this->getProduct($main_recipe->product_id)->product_name,
                    'recipe_quantity' => $main_recipe_quantity,
                    'quantity' => $quantity,
                    'recipe_id' => $main_recipe->recipe_id,
                    'product_id' => $main_recipe->product_id
                ];
            } elseif ($main_recipe && isset($recipes[$main_recipe->recipe_id])) {
                $recipes[$main_recipe->recipe_id]['recipe_quantity'] += $main_recipe_quantity;
                $recipes[$main_recipe->recipe_id]['quantity'] += $quantity;
            } elseif (!$main_recipe) {
                // Log cuando main_recipe es null para debugging
                Log::info("main_recipe is null for product_id: {$product_id}, quantity: {$quantity}, store_id: {$store_id}");
            }

            $results[$product_id] = [
                'product_id' => $product_id,
                'unit_quantity_proyected' => $quantity,
                'type' => $type,
                'recipes' => $recipes,
                'quantities_by_store' => [
                    $store_id => $quantity
                ]
            ];
        }

        return $results;
    }

    protected function getRecipe($product_id)
    {
        if (!array_key_exists($product_id, $this->recipeCache)) {
            $this->recipeCache[$product_id] = Recipe::where('product_id', $product_id)->first();
        }
        return $this->recipeCache[$product_id];
    }

    protected function getProduct($product_id)
    {
        if (!array_key_exists($product_id, $this->productCache)) {
            $this->productCache[$product_id] = Product::where('product_id', $product_id)->first();
        }
        return $this->productCache[$product_id];
    }

    protected function getProductLinks($product_id)
    {
        if (!array_key_exists($product_id, $this->productLinkCache)) {
            $this->productLinkCache[$product_id] = ProductLink::where('product_parent_id', $product_id)->get();
        }
        return $this->productLinkCache[$product_id];
    }

    protected function getDefaultPresentation($product_id)
    {
        if (!array_key_exists($product_id, $this->presentationCache)) {
            $this->presentationCache[$product_id] = Presentation::where('product_id', $product_id)
                ->where('default', 1)
                ->first();
        }
        return $this->presentationCache[$product_id];
    }

    public function getRepositionBySupply(Request $request)
    {
        // Aumentar timeout dinámicamente basado en días de reposición
        $daysOfReposition = intval($request->input('daysOfReposition', 7));
        $timeoutSeconds = 300; // Base: 5 minutos

        if ($daysOfReposition > 7) {
            // Para rangos largos: 10 minutos + 1 minuto por cada día adicional
            $timeoutSeconds = 600 + (($daysOfReposition - 7) * 60);
            Log::info("Increased timeout to {$timeoutSeconds} seconds for {$daysOfReposition} days of reposition in getRepositionBySupply");
        }

        set_time_limit($timeoutSeconds);
        ini_set('max_execution_time', $timeoutSeconds);

        try {
            $validate = Validator::make($request->all(), [
                'store' => 'required|integer',
                'daysMinStock' => 'required|integer',
                'daysOfReposition' => 'required|integer'
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_productIds = "";

            if ($request->has(key: 'product')) {
                $s_productIds = $request->input('product');
            }

            $a_params = [
                ':xstore_id' => $request->input('store'),
                ':xdays_min_stock' => $request->input('daysMinStock'),
                ':xdays_reposition' => $request->input('daysOfReposition'),
                ':xproduct_ids' => $s_productIds,
            ];

            // Log de llamada al SP
            Log::info("call sp_get_supply_reposition_analysis(" .
                $a_params[':xstore_id'] . ", " .
                $a_params[':xdays_min_stock'] . ", " .
                $a_params[':xdays_reposition'] . ", " .
                $a_params[':xproduct_ids'] . ")");

            $a_result = DB::select(
                "call sp_get_supply_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xproduct_ids)",
                $a_params
            );

            $a_response = $a_response = [
                'success' => true,
                'data' => $a_result,
            ];

            return response()->json($a_response);
        } catch (\Exception $ex) {
            Log::error('Error reposition: ' . $ex->getMessage());
            return response()->json([
                'success' => false,
                'error' => 'Error reposition: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getSupplyData(Request $request)
    {
        set_time_limit(300);
        ini_set('max_execution_time', 300);

        $s_providerIds = "";
        $s_divisionIds = "";
        $s_lineIds = "";
        $s_sublineIds = "";
        $s_productIds = "";
        $s_sort = "";
        $s_order = "";
        $s_break_scale = "";
        $s_rotation_scale = "";
        $s_product_type = 'Food';

        if ($request->has('provider')) {
            $s_providerIds = $request->input('provider');
        }
        if ($request->has('division')) {
            $s_divisionIds = $request->input('division');
        }
        if ($request->has('line')) {
            $s_lineIds = $request->input('line');
        }
        if ($request->has('subline')) {
            $s_sublineIds = $request->input('subline');
        }
        if ($request->has(key: 'product')) {
            $s_productIds = $request->input('product');
        }

        if ($request->has('sort')) {
            $s_sort = $request->input('sort');
        }
        if ($request->has('order')) {
            $s_order = $request->input('order');
        }

        if ($request->has('breakScale')) {
            $break_scale_array = explode(',', $request->input('breakScale'));
            $s_break_scale = "'" . implode("','", $break_scale_array) . "'";
        }

        if ($request->has('rotationScale')) {
            $rotation_scale_array = explode(',', $request->input('rotationScale'));
            $s_rotation_scale = "'" . implode("','", $rotation_scale_array) . "'";
        }

        $a_params = [
            ':xprovider_ids' => "",
            ':xdivision_ids' => "",
            ':xline_ids' => "",
            ':xsubline_ids' => "",
            ':xproduct_ids' => "",
            ':xrotation_scale' => "",
            ':xbreak_scale' => "",
            ':xproduct_type' => "Merc2"
        ];

        // Log de llamada al SP
        Log::info("call sp_get_reposition_products_simplified(" .
            $a_params[':xprovider_ids'] . ", " .
            $a_params[':xdivision_ids'] . ", " .
            $a_params[':xline_ids'] . ", " .
            $a_params[':xsubline_ids'] . ", " .
            $a_params[':xproduct_ids'] . ", " .
            $a_params[':xrotation_scale'] . ", " .
            $a_params[':xbreak_scale'] . ", " .
            $a_params[':xproduct_type'] . ")");

        $food_merchandise_ids = collect(DB::select(
            "call sp_get_reposition_products_simplified(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xrotation_scale, :xbreak_scale, :xproduct_type)",
            $a_params
        ))->pluck('product_id')->all();

        $s_food_merchandise_ids = implode(',', $food_merchandise_ids);

        $food_merchandise_params = [
            ':xstore_id' => intval($request->input('store', 0)),
            ':xdays_min_stock' => intval($request->input('daysMinStock', 7)),
            ':xdays_reposition' => intval($request->input('daysOfReposition', 3)),
            ':xprovider_ids' => "",
            ':xdivision_ids' => "",
            ':xline_ids' => "",
            ':xsubline_ids' => "",
            ':xproduct_ids' => $s_food_merchandise_ids,
            ':xscale_rotation' => "",
            ':xexpiration_alert' => "",
            ':xobsolete_alert' => "",
            ':xestimated_order' => "",
            ':xdays_to_order' => intval($request->input('daysToOrder', 1)),
            ':xsort' => "",
            ':xorder' => "",
            ':xlimit' => 0,
            ':xoffset' => 0,
            ':xproduct_type' => 'Merc2'
        ];

        // Log de llamada al SP
        Log::info("call sp_get_reposition_analysis(" .
            $food_merchandise_params[':xstore_id'] . ", " .
            $food_merchandise_params[':xdays_min_stock'] . ", " .
            $food_merchandise_params[':xdays_reposition'] . ", " .
            $food_merchandise_params[':xprovider_ids'] . ", " .
            $food_merchandise_params[':xdivision_ids'] . ", " .
            $food_merchandise_params[':xline_ids'] . ", " .
            $food_merchandise_params[':xsubline_ids'] . ", " .
            $food_merchandise_params[':xproduct_ids'] . ", " .
            $food_merchandise_params[':xscale_rotation'] . ", " .
            $food_merchandise_params[':xexpiration_alert'] . ", " .
            $food_merchandise_params[':xobsolete_alert'] . ", " .
            $food_merchandise_params[':xestimated_order'] . ", " .
            $food_merchandise_params[':xdays_to_order'] . ", " .
            $food_merchandise_params[':xsort'] . ", " .
            $food_merchandise_params[':xorder'] . ", " .
            $food_merchandise_params[':xlimit'] . ", " .
            $food_merchandise_params[':xoffset'] . ", " .
            $food_merchandise_params[':xproduct_type'] . ")");

        $food_merchandise_analisys = DB::select(
            "call sp_get_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids, :xscale_rotation, :xexpiration_alert, :xobsolete_alert, :xestimated_order, :xdays_to_order, :xsort, :xorder, :xlimit, :xoffset, :xproduct_type)",
            $food_merchandise_params
        );

        $grouped_food_merchandise = [];


        foreach ($food_merchandise_analisys as $item) {
            $productId = $item->product_id ?? null;
            if (!$productId)
                continue;

            if (!isset($grouped_food_merchandise[$productId])) {
                $grouped_food_merchandise[$productId] = [
                    'quantity_oc' => 0,
                    'stores' => [],
                ];
            }

            $grouped_food_merchandise[$productId]['quantity_oc'] += $item->unit_quantity_order;
            $grouped_food_merchandise[$productId]['stores'][] = $item;
        }

        $allSupplys = [];

        $only_derived = $request->input('foodMode', self::SUPPLY) == self::RAW_MATERIAL;
        $excluded_supplys = $only_derived ? [] : [self::DERIVED_PRODUCT];

        $all_derivedProductsCapture = [];

        foreach ($grouped_food_merchandise as $product_id => $product_data) {
            foreach ($product_data['stores'] as $store) {
                $store_quantity_oc = floatval($store->unit_quantity_order);
                $derivedProductsCapture = [];
                if ($store_quantity_oc > 0) {
                    $supplys = $this->getPlainSupplys(
                        $product_id,
                        $store_quantity_oc,
                        $store->store_id,
                        $this->getRecipe($product_id),
                        $store_quantity_oc,
                        $excluded_supplys,
                        $only_derived,
                        false,
                        $derivedProductsCapture
                    );

                    foreach ($supplys as $id => $supply) {
                        if (!isset($allSupplys[$id])) {
                            $allSupplys[$id] = $supply;
                        } else {
                            $allSupplys[$id]['unit_quantity_proyected'] += $supply['unit_quantity_proyected'];
                            foreach ($supply['recipes'] as $rid => $recipeData) {
                                if (!isset($allSupplys[$id]['recipes'][$rid])) {
                                    $allSupplys[$id]['recipes'][$rid] = $recipeData;
                                } else {
                                    $allSupplys[$id]['recipes'][$rid]['quantity'] += $recipeData['quantity'];
                                    $allSupplys[$id]['recipes'][$rid]['recipe_quantity'] += $recipeData['recipe_quantity'];
                                }
                            }

                            foreach ($supply['quantities_by_store'] as $sid => $qty) {
                                $allSupplys[$id]['quantities_by_store'][$sid] = ($allSupplys[$id]['quantities_by_store'][$sid] ?? 0) + $qty;
                            }
                        }
                    }

                    foreach ($derivedProductsCapture as $derivedId => $derivedQty) {
                        if (!isset($allSupplys[$derivedId])) {
                            $all_derivedProductsCapture[$derivedId] = [
                                'unit_quantity_proyected' => $derivedQty,
                                'quantities_by_store' => [$store->store_id => $derivedQty]
                            ];
                        } else {
                            $all_derivedProductsCapture[$derivedId]['unit_quantity_proyected'] += $derivedQty;
                            $all_derivedProductsCapture[$derivedId]['quantities_by_store'][$store->store_id] = ($allSupplys[$derivedId]['quantities_by_store'][$store->store_id] ?? 0) + $derivedQty;
                        }
                    }
                }
            }
        }

        $s_supplys_ids = implode(',', array_keys($allSupplys));

        $a_supply_params = [
            ':xprovider_ids' => $s_providerIds,
            ':xdivision_ids' => $s_divisionIds,
            ':xline_ids' => $s_lineIds,
            ':xsubline_ids' => $s_sublineIds,
            ':xproduct_ids' => $s_supplys_ids,
        ];

        // Log de llamada al SP
        Log::info("call sp_get_reposition_supply(" .
            $a_supply_params[':xprovider_ids'] . ", " .
            $a_supply_params[':xdivision_ids'] . ", " .
            $a_supply_params[':xline_ids'] . ", " .
            $a_supply_params[':xsubline_ids'] . ", " .
            $a_supply_params[':xproduct_ids'] . ")");

        $a_supplys = DB::select(
            "call sp_get_reposition_supply(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids)",
            $a_supply_params
        );

        $a_supply_analysis_params = [
            ':xstore_id' => 0,
            ':xdays_min_stock' => $request->input('daysMinStock'),
            ':xdays_reposition' => $request->input('daysOfReposition'),
            ':xproduct_ids' => $s_supplys_ids
        ];

        // Log de llamada al SP
        Log::info("call sp_get_supply_reposition_analysis(" .
            $a_supply_analysis_params[':xstore_id'] . ", " .
            $a_supply_analysis_params[':xdays_min_stock'] . ", " .
            $a_supply_analysis_params[':xdays_reposition'] . ", " .
            $a_supply_analysis_params[':xproduct_ids'] . ")");

        $a_supplys_analisys = DB::select(
            "call sp_get_supply_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xproduct_ids)",
            $a_supply_analysis_params
        );

        $grouped_supply_analisys = [];

        foreach ($a_supplys_analisys as $item) {
            $productId = $item->product_id ?? null;
            if (!$productId)
                continue;

            $grouped_supply_analisys[$productId][] = $item;
        }

        $a_productPresentations = SpGetProductPresentations::getAssociative(
            SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
            $s_supplys_ids,
            Currency::PEN,
            0
        );

        $finalSupplys = [];

        foreach ($a_supplys as $o_row) {
            $productId = intval($o_row->product_id);

            if (!isset($allSupplys[$productId]))
                continue;

            $o_row->presentations = $a_productPresentations[$productId] ?? [];
            $o_row->presentation = $a_productPresentations[$productId][Presentation::UNIT_EQUIVALENCE];

            $analisysRows = $grouped_supply_analisys[$productId] ?? [];

            foreach ($analisysRows as &$analysis) {
                $storeId = $analysis->store_id;
                $stockQty = $allSupplys[$productId]['quantities_by_store'][$storeId] ?? 0;

                $analysis->unit_quantity_proyected = $stockQty;
                $analysis->unit_quantity_order = $stockQty - $analysis->purchase_stock;
            }

            $mergedRow = array_merge(
                (array) $o_row,
                $allSupplys[$productId],
                ['analisys' => $analisysRows],
                ['unit_quantity_order' => $allSupplys[$productId]['unit_quantity_proyected'] - $o_row->purchase_stock]
            );

            $finalSupplys[] = $mergedRow;
        }

        if ($only_derived) {
            // Flujo específico para RAW_MATERIAL - completamente separado
            $finalSupplys = $this->calculateRawMaterialProjectionsFlow($grouped_food_merchandise, $request);
        }

        return $finalSupplys;
    }


    private function addDerivedProductsToSupplies($supplies, $request, $all_derivedProductsCapture)
    {
        $allProductIds = [];
        foreach ($supplies as $supply) {
            $derivedProducts = $this->getDerivedProductsRecursive($supply['product_id'], $request, $all_derivedProductsCapture);
            if (!empty($derivedProducts)) {
                $allProductIds[] = $supply['product_id'];
                $this->collectAllDerivedProductIds($derivedProducts, $allProductIds);
            }
        }

        $wastePercentages = [];
        if (!empty($allProductIds)) {
            $uniqueProductIds = array_unique($allProductIds);
            $wastePercentages = $this->getWastePercentages($uniqueProductIds);
        }

        foreach ($supplies as &$supply) {
            $derivedProducts = $this->getDerivedProductsRecursive($supply['product_id'], $request, $all_derivedProductsCapture);
            if (!empty($derivedProducts)) {
                $this->addWasteInfoToProducts($derivedProducts, $wastePercentages);



                $supply['derivedProducts'] = $derivedProducts;

                if (isset($wastePercentages[$supply['product_id']])) {
                    $supply['waste_info'] = $wastePercentages[$supply['product_id']];
                }
            }
        }
        return $supplies;
    }

    private function collectAllDerivedProductIds($derivedProducts, &$allProductIds)
    {
        foreach ($derivedProducts as $derived) {
            $allProductIds[] = $derived['product_id'];

            if (!empty($derived['derivedProducts'])) {
                $this->collectAllDerivedProductIds($derived['derivedProducts'], $allProductIds);
            }
        }
    }

    private function addWasteInfoToProducts(&$products, $wastePercentages)
    {
        foreach ($products as &$product) {
            if (isset($wastePercentages[$product['product_id']])) {
                $product['waste_info'] = $wastePercentages[$product['product_id']];
            }

            if (!empty($product['derivedProducts'])) {
                $this->addWasteInfoToProducts($product['derivedProducts'], $wastePercentages);
            }
        }
    }



    /**
     * Calcula la proyección de un producto derivado final basado en las recetas de platos que lo usan
     */
    private function calculateFinalDerivedProductProjection($derivedProductId, $grouped_food_merchandise)
    {
        $totalProjection = 0;

        foreach ($grouped_food_merchandise as $dishId => $dishData) {
            // Obtener la receta del plato
            $recipe = $this->getRecipe($dishId);
            if (!$recipe) continue;

            // Calcular la proyección total del plato
            $dishProjectedSales = 0;
            foreach ($dishData['stores'] as $store) {
                $dishProjectedSales += floatval($store->unit_quantity_order);
            }

            if ($dishProjectedSales <= 0) continue;

            // Verificar si este producto derivado está en la receta del plato
            $ingredients = $this->getProductLinks($dishId);
            foreach ($ingredients as $ingredient) {
                if ($ingredient->product_id == $derivedProductId) {
                    // Calcular cuánto de este producto derivado se necesita para la proyección del plato
                    $recipeQuantity = $ingredient->quantity;
                    $recipeEquivalence = $ingredient->equivalence;
                    $requiredQuantity = ($recipeEquivalence * $recipeQuantity) * $dishProjectedSales;
                    $totalProjection += $requiredQuantity;
                    break;
                }
            }
        }

        return $totalProjection;
    }

    /**
     * Calcula la proyección de un producto derivado final con cantidades por tienda
     */
    private function calculateFinalDerivedProductProjectionWithStores($derivedProductId, $grouped_food_merchandise)
    {
        $totalProjection = 0;
        $quantitiesByStore = [];

        foreach ($grouped_food_merchandise as $dishId => $dishData) {
            // Obtener la receta del plato
            $recipe = $this->getRecipe($dishId);
            if (!$recipe) continue;

            // Verificar si este producto derivado está en la receta del plato
            $ingredients = $this->getProductLinks($dishId);
            $recipeQuantity = 0;
            $recipeEquivalence = 0;

            foreach ($ingredients as $ingredient) {
                if ($ingredient->product_id == $derivedProductId) {
                    $recipeQuantity = $ingredient->quantity;
                    $recipeEquivalence = $ingredient->equivalence;
                    break;
                }
            }

            if ($recipeQuantity <= 0) continue;

            // Calcular proyección por cada tienda
            foreach ($dishData['stores'] as $store) {
                $storeProjectedSales = floatval($store->unit_quantity_order);
                if ($storeProjectedSales <= 0) continue;

                $requiredQuantity = ($recipeEquivalence * $recipeQuantity) * $storeProjectedSales;
                $storeId = $store->store_id;

                if (!isset($quantitiesByStore[$storeId])) {
                    $quantitiesByStore[$storeId] = 0;
                }
                $quantitiesByStore[$storeId] += $requiredQuantity;
                $totalProjection += $requiredQuantity;
            }
        }

        return [
            'total' => $totalProjection,
            'quantities_by_store' => $quantitiesByStore
        ];
    }

    /**
     * Calcula la proyección de la materia prima basada en la suma de sus productos derivados
     * Retorna tanto la suma original como la multiplicada por el factor de merma
     */
    private function calculateRawMaterialProjectionFromDerived($derivedProducts, $wasteInfo)
    {
        $totalDerivedProjection = 0;

        // Sumar todas las proyecciones de productos derivados
        foreach ($derivedProducts as $derived) {
            $derivedProjection = $derived['unit_quantity_proyected'] ?? 0;
            $totalDerivedProjection += $derivedProjection;
        }

        // Aplicar el factor de merma si existe
        $multiplierToTotal = 1; // Factor por defecto
        if ($wasteInfo) {
            // Manejar tanto objetos como arrays
            if (is_object($wasteInfo) && isset($wasteInfo->multiplier_to_total)) {
                $multiplierToTotal = floatval($wasteInfo->multiplier_to_total);
            } elseif (is_array($wasteInfo) && isset($wasteInfo['multiplier_to_total'])) {
                $multiplierToTotal = floatval($wasteInfo['multiplier_to_total']);
            }
        }

        // Proyección de materia prima = suma de derivados * factor de merma
        $rawMaterialProjection = $totalDerivedProjection * $multiplierToTotal;

        // Retornar ambos valores
        return [
            'derived_sum' => $totalDerivedProjection,           // Suma sin factor de merma
            'final_projection' => $rawMaterialProjection,      // Con factor de merma aplicado
            'multiplier' => $multiplierToTotal                 // Factor aplicado
        ];
    }

    private function getWastePercentages($productIds)
    {
        if (empty($productIds)) {
            return [];
        }

        $productIdsString = implode(',', $productIds);

        // Log de llamada al SP
        Log::info("call sp_get_waste_percentage(" . $productIdsString . ")");

        $results = DB::select("CALL sp_get_waste_percentage(?)", [$productIdsString]);

        $wasteData = [];
        foreach ($results as $result) {
            $wasteData[$result->product_id] = [
                'product_id' => $result->product_id,
                'product_name' => $result->product_name,
                'normal_quantity' => floatval($result->normal_quantity),
                'waste_quantity' => floatval($result->waste_quantity),
                'total_quantity' => floatval($result->total_quantity),
                'waste_percentage_total' => floatval($result->waste_percentage_total),
                'multiplier_to_total' => floatval($result->multiplier_to_total)
            ];
        }

        return $wasteData;
    }

    /**
     * Formatea una cantidad dividiéndola entre la equivalencia por defecto
     * Si no es división exacta, muestra el resto en gramos
     */
    private function formatQuantityWithEquivalence($quantity, $productData)
    {
        // Valores por defecto
        $equivalenceDefault = 1;
        $measureDefault = 'gr';

        // Buscar en analisys si existe
        if (isset($productData['analisys']) && is_array($productData['analisys']) && !empty($productData['analisys'])) {
            $firstAnalysis = $productData['analisys'][0];

            if (isset($firstAnalysis->equivalence_default)) {
                $equivalenceDefault = floatval($firstAnalysis->equivalence_default);
            }
            if (isset($firstAnalysis->measure_default)) {
                $measureDefault = $firstAnalysis->measure_default;
            }
        }

        // Si la equivalencia es 1 o menor, mostrar solo en la unidad mínima (measure_name)
        if ($equivalenceDefault <= 1) {
            return [
                'formatted_text' => number_format($quantity, 2) . ' gr',
                'main_quantity' => $quantity,
                'main_unit' => 'gr',
                'remainder_quantity' => 0,
                'remainder_unit' => 'gr',
                'equivalence_default' => $equivalenceDefault,
                'measure_default' => $measureDefault,
                'total_quantity' => $quantity
            ];
        }

        // Calcular división
        $mainQuantity = floor($quantity / $equivalenceDefault);
        $remainder = $quantity % $equivalenceDefault;

        // Formatear texto
        $formattedText = '';
        if ($mainQuantity > 0) {
            $formattedText = number_format($mainQuantity, 0) . ' ' . $measureDefault;
        }

        if ($remainder > 0) {
            if ($mainQuantity > 0) {
                $formattedText .= ' + ';
            }
            $formattedText .= number_format($remainder, 2) . ' gr';
        }

        // Si no hay cantidad principal, mostrar solo gramos
        if ($mainQuantity == 0 && $remainder == 0) {
            $formattedText = '0 gr';
        }

        $result = [
            'formatted_text' => $formattedText,
            'main_quantity' => $mainQuantity,
            'main_unit' => $measureDefault,
            'remainder_quantity' => $remainder,
            'remainder_unit' => 'gr',
            'equivalence_default' => $equivalenceDefault,
            'measure_default' => $measureDefault,
            'total_quantity' => $quantity
        ];

        return $result;
    }

    private function getDerivedProductsRecursive($productId, $request, $all_derivedProductsCapture, $depth = 0, $maxDepth = 10)
    {
        if ($depth > $maxDepth) {
            return [];
        }

        $directDerived = $this->getDirectDerivedProducts($productId);

        if (empty($directDerived)) {
            return [];
        }

        $derivedProducts = [];

        $derivedProductIds = array_map(function ($derived) {
            return $derived->product_id;
        }, $directDerived);

        $allProductPresentations = [];
        if (!empty($derivedProductIds)) {
            $allProductPresentations = SpGetProductPresentations::getAssociative(
                SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
                $derivedProductIds,
                Currency::PEN,
                0
            );
        }

        foreach ($directDerived as $derived) {
            $derivedData = $this->getProductSupplyData($derived->product_id);
            if (!empty($derivedData)) {
                $productData = (array) $derivedData[0];
                $productData['unit_quantity_proyected'] = $all_derivedProductsCapture[$derived->product_id]['unit_quantity_proyected'] ?? 0;

                if (!empty($allProductPresentations[$derived->product_id])) {
                    $productData['presentations'] = $allProductPresentations[$derived->product_id];
                    $productData['presentation'] = $allProductPresentations[$derived->product_id][Presentation::UNIT_EQUIVALENCE] ?? null;
                }

                $analysisData = $this->getCustomAnalysisForDerivedProduct($derived->product_id);

                if (!empty($analysisData)) {
                    $productData['analisys'] = $analysisData;

                    // Calcular suma_netos_analisis directamente desde el análisis
                    $totalNetoFromAnalysis = 0;
                    foreach ($analysisData as $analysis) {
                        $storeStock = $analysis->purchase_stock ?? 0;
                        $storeProjection = $analysis->unit_quantity_proyected ?? 0;
                        $storeNeto = max(0, $storeProjection - $storeStock);
                        $totalNetoFromAnalysis += $storeNeto;
                    }
                    $productData['suma_netos_analisis'] = $totalNetoFromAnalysis;
                } else {
                    $productData['suma_netos_analisis'] = 0;
                }

                $nestedDerived = $this->getDerivedProductsRecursive($derived->product_id, $request, $depth + 1, $maxDepth);

                if (!empty($nestedDerived)) {
                    $productData['derivedProducts'] = $nestedDerived;
                }

                $derivedProducts[] = $productData;
            }
        }

        return $derivedProducts;
    }

    private function getDirectDerivedProducts($productId)
    {
        // Buscar productos derivados que se obtienen DE la materia prima ($productId)
        // Productos derivados: NO tienen receta Y son product_parent_id en product_link
        $query = "
            SELECT DP.product_id, DP.product_name
            FROM product DP
            JOIN product_link PL ON PL.product_parent_id = DP.product_id
            JOIN presentation DPUPRES ON DPUPRES.product_id = DP.product_id AND DPUPRES.equivalence = 1
            LEFT JOIN recipe RE ON DP.product_id = RE.product_id
            WHERE PL.product_id = ? AND RE.recipe_id IS NULL
        ";
        $result = DB::select($query, [$productId]);
        return $result;
    }

    private function calculateRawMaterialProjectionsFlow($grouped_food_merchandise, $request)
    {
        $allSupplys = [];

        foreach ($grouped_food_merchandise as $product_id => $product_data) {
            foreach ($product_data['stores'] as $store) {
                $store_quantity_oc = floatval($store->unit_quantity_order);
                $derivedProductsCapture = [];
                if ($store_quantity_oc > 0) {
                    $supplys = $this->getPlainSupplys(
                        $product_id,
                        $store_quantity_oc,
                        $store->store_id,
                        $this->getRecipe($product_id),
                        $store_quantity_oc,
                        [],
                        false,
                        false,
                        $derivedProductsCapture
                    );

                    foreach ($supplys as $id => $supply) {
                        if (!$this->isRealRawMaterial($id)) {
                            continue;
                        }

                        if (!isset($allSupplys[$id])) {
                            $allSupplys[$id] = $supply;
                        } else {
                            $allSupplys[$id]['unit_quantity_proyected'] += $supply['unit_quantity_proyected'];
                            foreach ($supply['recipes'] as $rid => $recipeData) {
                                if (!isset($allSupplys[$id]['recipes'][$rid])) {
                                    $allSupplys[$id]['recipes'][$rid] = $recipeData;
                                } else {
                                    $allSupplys[$id]['recipes'][$rid]['quantity'] += $recipeData['quantity'];
                                    $allSupplys[$id]['recipes'][$rid]['recipe_quantity'] += $recipeData['recipe_quantity'];
                                }
                            }
                            foreach ($supply['quantities_by_store'] as $sid => $qty) {
                                $allSupplys[$id]['quantities_by_store'][$sid] = ($allSupplys[$id]['quantities_by_store'][$sid] ?? 0) + $qty;
                            }
                        }
                    }
                }
            }
        }

        $finalSupplys = $this->getSupplyAnalysisWithOriginalStructure($allSupplys, $request);

        foreach ($finalSupplys as &$supply) {
            $derivedProducts = $this->getDerivedProductsRecursive($supply['product_id'], $request, []);

            if (!empty($derivedProducts)) {
                $this->calculateDerivedProductProjections($derivedProducts, $grouped_food_merchandise);

                $allProductIds = [$supply['product_id']];
                $this->collectAllDerivedProductIds($derivedProducts, $allProductIds);
                $wastePercentages = $this->getWastePercentages(array_unique($allProductIds));

                $this->addWasteInfoToProducts($derivedProducts, $wastePercentages);

                $supply['derivedProducts'] = $derivedProducts;

                if (isset($wastePercentages[$supply['product_id']])) {
                    $supply['waste_info'] = $wastePercentages[$supply['product_id']];
                }

                $wasteInfoForProduct = $wastePercentages[$supply['product_id']] ?? null;

                $projectionData = $this->calculateRawMaterialProjectionFromDerived($derivedProducts, $wasteInfoForProduct);

                $supply['unit_quantity_proyected'] = $projectionData['final_projection']; // Con factor de merma

                // Ahora calcular campos to_transform y neto para productos derivados usando la proyección de la materia prima
                $this->calculateDerivedProductTransformFields($derivedProducts, $supply['unit_quantity_proyected']);

                // Calcular neto para la materia prima basado en netos de productos derivados
                $this->calculateRawMaterialNetoField($supply, $derivedProducts, $projectionData['multiplier']);

                $supply['derived_products_sum'] = $projectionData['derived_sum']; // Sin factor de merma
                $supply['waste_multiplier'] = $projectionData['multiplier']; // Factor aplicado

                $supply['formatted_projection'] = $this->formatQuantityWithEquivalence(
                    $projectionData['final_projection'],
                    $supply
                );
                $supply['formatted_derived_sum'] = $this->formatQuantityWithEquivalence(
                    $projectionData['derived_sum'],
                    $supply
                );

                // Actualizar el análisis con la nueva proyección
                if (isset($supply['analisys']) && is_array($supply['analisys'])) {
                    foreach ($supply['analisys'] as &$analysis) {
                        // Buscar la cantidad específica por tienda en lugar de usar el total
                        $storeId = $analysis->store_id;
                        $storeSpecificQty = 0;

                        // Calcular la proyección específica para esta tienda basada en productos derivados
                        if (!empty($derivedProducts)) {
                            foreach ($derivedProducts as $derived) {
                                $derivedQtyForStore = $derived['quantities_by_store'][$storeId] ?? 0;
                                $storeSpecificQty += $derivedQtyForStore;
                            }
                            // Aplicar el factor de merma a la cantidad específica de la tienda
                            $storeSpecificQty *= $projectionData['multiplier'];
                        }

                        $analysis->unit_quantity_proyected = $storeSpecificQty;

                        // Para RAW_MATERIAL: REPONER/TRANSFORMAR = C.PROYECTADA - STOCK EN TIENDA
                        // (El stock del almacén principal se resta solo en el cálculo general)
                        $storeStock = $analysis->purchase_stock ?? 0; // Stock en esta tienda específica
                        $analysis->unit_quantity_order = max(0, $storeSpecificQty - $storeStock);

                        // Agregar campos específicos para cada tienda en RAW_MATERIAL
                        $analysis->to_transform = $analysis->unit_quantity_order; // Se formateará en frontend
                        $analysis->neto = $storeSpecificQty - $storeStock; // Valor sin transformación
                    }
                }

                // Calcular unit_quantity_order general para RAW_MATERIAL
                // Total proyectado - (suma de stocks en tiendas + stock almacén principal)
                $totalStoreStock = 0;
                if (isset($supply['analisys']) && is_array($supply['analisys'])) {
                    foreach ($supply['analisys'] as $analysis) {
                        $totalStoreStock += $analysis->purchase_stock ?? 0;
                    }
                }
                // Usar supplying_stock si purchase_stock es null (para materias primas)
                $mainWarehouseStock = $supply['purchase_stock'] ?? $supply['supplying_stock'] ?? 0; // Stock almacén principal



                $supply['unit_quantity_order'] = max(0, $projectionData['final_projection'] - ($totalStoreStock + $mainWarehouseStock));

                // Agregar campos específicos para RAW_MATERIAL
                $supply['to_transform'] = $supply['unit_quantity_order']; // Valor con transformación de unidades (se formateará en frontend)
                $supply['neto'] = $projectionData['final_projection'] - ($totalStoreStock + $mainWarehouseStock); // Valor sin transformación
            } else {
                // Si NO tiene productos derivados: mantener proyección original
                // Solo agregar waste_info si existe
                $wastePercentages = $this->getWastePercentages([$supply['product_id']]);
                if (isset($wastePercentages[$supply['product_id']])) {
                    $supply['waste_info'] = $wastePercentages[$supply['product_id']];
                }

                // Calcular suma_netos_analisis para productos sin derivados
                $totalNetoFromAnalysis = 0;
                if (isset($supply['analisys']) && is_array($supply['analisys'])) {
                    foreach ($supply['analisys'] as $analysis) {
                        $storeStock = $analysis->purchase_stock ?? 0;
                        $storeProjection = $analysis->unit_quantity_proyected ?? 0;
                        $storeNeto = max(0, $storeProjection - $storeStock);
                        $totalNetoFromAnalysis += $storeNeto;
                    }
                }
                $supply['suma_netos_analisis'] = $totalNetoFromAnalysis;

                // La proyección original se mantiene tal como viene del cálculo inicial
            }
        }

        return $finalSupplys;
    }

    /**
     * Obtiene el análisis de materias primas manteniendo la estructura original del SP
     */
    private function getSupplyAnalysisWithOriginalStructure($allSupplys, $request)
    {
        if (empty($allSupplys)) {
            return [];
        }

        // Usar la misma lógica del SP original (como en getSupplyData)
        $s_supplys_ids = implode(',', array_keys($allSupplys));

        $s_providerIds = $request->input('provider', '');
        $s_divisionIds = $request->input('division', '');
        $s_lineIds = $request->input('line', '');
        $s_sublineIds = $request->input('subline', '');

        // 1. Obtener información básica de supplies
        $a_supply_params = [
            ':xprovider_ids' => $s_providerIds,
            ':xdivision_ids' => $s_divisionIds,
            ':xline_ids' => $s_lineIds,
            ':xsubline_ids' => $s_sublineIds,
            ':xproduct_ids' => $s_supplys_ids,
        ];

        // Log de llamada al SP
        Log::info("call sp_get_reposition_supply(" .
            $a_supply_params[':xprovider_ids'] . ", " .
            $a_supply_params[':xdivision_ids'] . ", " .
            $a_supply_params[':xline_ids'] . ", " .
            $a_supply_params[':xsubline_ids'] . ", " .
            $a_supply_params[':xproduct_ids'] . ")");

        $result = DB::select(
            "call sp_get_reposition_supply(:xprovider_ids, :xdivision_ids, :xline_ids, :xsubline_ids, :xproduct_ids)",
            $a_supply_params
        );

        // 2. Obtener análisis de supplies
        $a_supply_analysis_params = [
            ':xstore_id' => $request->input('store', 0),
            ':xdays_min_stock' => $request->input('daysMinStock', 3),
            ':xdays_reposition' => $request->input('daysOfReposition', 7),
            ':xproduct_ids' => $s_supplys_ids
        ];

        // Log de llamada al SP
        Log::info("call sp_get_supply_reposition_analysis(" .
            $a_supply_analysis_params[':xstore_id'] . ", " .
            $a_supply_analysis_params[':xdays_min_stock'] . ", " .
            $a_supply_analysis_params[':xdays_reposition'] . ", " .
            $a_supply_analysis_params[':xproduct_ids'] . ")");

        $a_supplys_analisys = DB::select(
            "call sp_get_supply_reposition_analysis(:xstore_id, :xdays_min_stock, :xdays_reposition, :xproduct_ids)",
            $a_supply_analysis_params
        );

        // Obtener presentaciones
        $productIds = array_keys($allSupplys);
        $a_productPresentations = SpGetProductPresentations::getAssociative(
            SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
            $productIds,
            Currency::PEN,
            0
        );

        // Agrupar análisis por product_id
        $grouped_supply_analisys = [];
        foreach ($a_supplys_analisys as $row) {
            $productId = intval($row->product_id);
            $grouped_supply_analisys[$productId][] = $row;
        }

        $finalSupplys = [];
        foreach ($result as $o_row) {
            $productId = intval($o_row->product_id);

            if (!isset($allSupplys[$productId]))
                continue;

            $o_row->presentations = $a_productPresentations[$productId] ?? [];
            $o_row->presentation = $a_productPresentations[$productId][Presentation::UNIT_EQUIVALENCE] ?? null;

            $analisysRows = $grouped_supply_analisys[$productId] ?? [];

            foreach ($analisysRows as &$analysis) {
                $storeId = $analysis->store_id;
                $stockQty = $allSupplys[$productId]['quantities_by_store'][$storeId] ?? 0;

                $analysis->unit_quantity_proyected = $stockQty;
                $analysis->unit_quantity_order = $stockQty - $analysis->purchase_stock;
            }

            $mergedRow = array_merge(
                (array) $o_row,
                $allSupplys[$productId],
                ['analisys' => $analisysRows],
                ['unit_quantity_order' => $allSupplys[$productId]['unit_quantity_proyected'] - $o_row->purchase_stock]
            );

            $finalSupplys[] = $mergedRow;
        }

        return $finalSupplys;
    }

    /**
     * Determina el tipo de producto basado en si tiene receta y product_links
     * DERIVED_PRODUCT: NO tiene receta Y es product_parent_id en product_link
     * SUPPLY: NO tiene receta Y NO es product_parent_id en product_link
     * SUPPLY_RECIPE: SÍ tiene receta Y tiene ingredientes (product_links)
     */
    private function determineProductType($productId)
    {
        $recipe = $this->getRecipe($productId);
        $hasIngredients = $this->getProductLinks($productId)->isNotEmpty(); // Es padre de otros productos
        $isDerivedProduct = $this->isProductDerived($productId); // Es producto derivado

        switch (true) {
            case ($recipe && $hasIngredients):
                return self::SUPPLY_RECIPE; // Plato con receta e ingredientes
            case (!$recipe && $isDerivedProduct):
                return self::DERIVED_PRODUCT; // Producto derivado (sin receta, pero es parent en product_link)
            case (!$recipe && !$isDerivedProduct):
                return self::SUPPLY; // Materia prima pura
            default:
                return 'unknown';
        }
    }

    /**
     * Verifica si un producto es derivado (es product_parent_id en product_link pero no tiene receta)
     */
    private function isProductDerived($productId)
    {
        $query = "
            SELECT COUNT(*) as count
            FROM product_link PL
            LEFT JOIN recipe R ON R.product_id = ?
            WHERE PL.product_parent_id = ? AND R.recipe_id IS NULL
        ";
        $result = DB::select($query, [$productId, $productId]);
        return $result[0]->count > 0;
    }

    /**
     * Verifica si un producto es una materia prima real
     * Materia prima = Es HIJA de un producto derivado (aparece como product_id en product_link)
     * donde el product_parent_id es un producto derivado (sin receta)
     */
    private function isRealRawMaterial($productId)
    {
        $query = "
            SELECT COUNT(*) as count
            FROM product_link PL
            JOIN product P_PARENT ON P_PARENT.product_id = PL.product_parent_id
            LEFT JOIN recipe R_PARENT ON R_PARENT.product_id = P_PARENT.product_id
            WHERE PL.product_id = ? AND R_PARENT.recipe_id IS NULL
        ";
        $result = DB::select($query, [$productId]);
        return $result[0]->count > 0;
    }



    private function getProductSupplyData($productId)
    {
        // Log de llamada al SP
        Log::info("call sp_get_reposition_supply('', '', '', '', " . $productId . ")");

        $result = DB::select("CALL sp_get_reposition_supply(?, ?, ?, ?, ?)", [
            '',
            '',
            '',
            '',
            $productId
        ]);
        return $result;
    }

    /**
     * Calcula proyecciones para productos derivados recursivamente
     * Solo productos derivados finales se calculan basado en recetas de platos
     */
    private function calculateDerivedProductProjections(&$derivedProducts, $grouped_food_merchandise)
    {

        foreach ($derivedProducts as &$derived) {
            $derivedProductId = $derived['product_id'];

            if (!empty($derived['derivedProducts'])) {
                // Si tiene productos derivados, calcular recursivamente primero
                $this->calculateDerivedProductProjections($derived['derivedProducts'], $grouped_food_merchandise);

                // Calcular proyección basada en la suma de sus productos derivados
                $totalDerivedProjection = 0;
                $quantitiesByStore = [];

                foreach ($derived['derivedProducts'] as $subDerived) {
                    $subProjection = $subDerived['unit_quantity_proyected'] ?? 0;
                    $wasteInfo = $subDerived['waste_info'] ?? null;
                    $wasteFactor = $wasteInfo ? (1 + ($wasteInfo->waste_percentage / 100)) : 1;
                    $totalDerivedProjection += $subProjection * $wasteFactor;

                    // Sumar cantidades por tienda
                    if (isset($subDerived['quantities_by_store'])) {
                        foreach ($subDerived['quantities_by_store'] as $storeId => $qty) {
                            if (!isset($quantitiesByStore[$storeId])) {
                                $quantitiesByStore[$storeId] = 0;
                            }
                            $quantitiesByStore[$storeId] += $qty * $wasteFactor;
                        }
                    }
                }
                $derived['unit_quantity_proyected'] = $totalDerivedProjection;
                $derived['quantities_by_store'] = $quantitiesByStore;
            } else {
                // Es un producto derivado final - calcular basado en recetas de platos
                $projectionData = $this->calculateFinalDerivedProductProjectionWithStores($derivedProductId, $grouped_food_merchandise);
                $derived['unit_quantity_proyected'] = $projectionData['total'];
                $derived['quantities_by_store'] = $projectionData['quantities_by_store'];
            }

            // Formatear cantidad proyectada con equivalencia
            $derived['formatted_projection'] = $this->formatQuantityWithEquivalence(
                $derived['unit_quantity_proyected'],
                $derived
            );

            // Actualizar el análisis con la cantidad proyectada específica por tienda
            if (isset($derived['analisys']) && is_array($derived['analisys'])) {
                foreach ($derived['analisys'] as &$analysis) {
                    // Buscar la cantidad específica por tienda
                    $storeId = $analysis->store_id;
                    $storeSpecificQty = $derived['quantities_by_store'][$storeId] ?? 0;

                    $analysis->unit_quantity_proyected = $storeSpecificQty;
                    $analysis->unit_quantity_order = max(0, $storeSpecificQty - ($analysis->purchase_stock ?? 0));
                }
            }
        }
    }

    /**
     * Calcula los campos to_transform y neto para productos derivados usando su propia proyección
     */
    private function calculateDerivedProductTransformFields(&$derivedProducts, $rawMaterialProjection)
    {

        foreach ($derivedProducts as &$derived) {
            // Calcular netos por tienda y sumarlos
            $totalNetoFromAnalysis = 0;
            $totalStoreStock = 0;
            $mainWarehouseStock = $derived['supplying_stock'] ?? 0;

            // Calcular netos por tienda del producto derivado
            if (isset($derived['analisys']) && is_array($derived['analisys'])) {
                foreach ($derived['analisys'] as &$analysis) {
                    $storeStock = $analysis->purchase_stock ?? 0;
                    $totalStoreStock += $storeStock;

                    // Calcular campos por tienda usando proyección específica de la tienda
                    $storeProjection = $analysis->unit_quantity_proyected ?? 0;
                    $storeCalculatedValue = $storeProjection - $storeStock;
                    $analysis->to_transform = max(0, $storeCalculatedValue); // No negativos
                    $analysis->neto = max(0, $storeCalculatedValue); // No negativos

                    // Sumar al neto total del producto
                    $totalNetoFromAnalysis += $analysis->neto;
                }
            }

            // Agregar la suma de netos del análisis como campo visible
            $derived['suma_netos_analisis'] = $totalNetoFromAnalysis;

            // El neto del producto es la SUMA de los netos del análisis
            $derived['neto'] = $totalNetoFromAnalysis;

            // IMPORTANTE: El to_transform del producto derivado es neto - stock almacén principal
            // Solo lo que no se puede cubrir con stock almacén pasa a la materia prima
            $derived['to_transform'] = max(0, $totalNetoFromAnalysis - $mainWarehouseStock);

            // El neto_for_raw_material es lo que realmente pasa al cálculo de materia prima
            $derived['neto_for_raw_material'] = max(0, $totalNetoFromAnalysis - $mainWarehouseStock);



            // Procesar productos derivados anidados recursivamente
            if (!empty($derived['derivedProducts'])) {
                $this->calculateDerivedProductTransformFields($derived['derivedProducts'], $rawMaterialProjection);
            }
        }
    }

    /**
     * Calcula el campo neto para la materia prima basado en los netos de productos derivados
     */
    private function calculateRawMaterialNetoField(&$supply, $derivedProducts, $wasteMultiplier)
    {
        // Sumar todos los netos de productos derivados
        $totalDerivedNeto = 0;
        $this->sumDerivedProductNetos($derivedProducts, $totalDerivedNeto);

        // Multiplicar por factor de merma
        $netoWithWaste = $totalDerivedNeto * $wasteMultiplier;

        // Calcular stocks de la materia prima
        $totalStoreStock = 0;
        $mainWarehouseStock = $supply['purchase_stock'] ?? $supply['supplying_stock'] ?? 0;

        // Sumar stock de todas las tiendas de la materia prima
        if (isset($supply['analisys']) && is_array($supply['analisys'])) {
            foreach ($supply['analisys'] as &$analysis) {
                $storeStock = $analysis->purchase_stock ?? 0;
                $totalStoreStock += $storeStock;

                // Calcular neto por tienda para la materia prima
                $storeNeto = max(0, $netoWithWaste - $storeStock);
                $analysis->neto = $storeNeto;
            }
        }

        // Calcular neto general de la materia prima
        $rawMaterialNeto = max(0, $netoWithWaste - ($totalStoreStock + $mainWarehouseStock));
        $supply['neto'] = $rawMaterialNeto;
    }

    /**
     * Suma recursivamente los netos que van a materia prima de todos los productos derivados
     */
    private function sumDerivedProductNetos($derivedProducts, &$totalNeto)
    {
        foreach ($derivedProducts as $derived) {
            // Usar neto_for_raw_material que ya tiene descontado el stock de almacén principal
            $derivedNetoForRaw = $derived['neto_for_raw_material'] ?? 0;
            $totalNeto += $derivedNetoForRaw;

            // Procesar productos derivados anidados recursivamente
            if (!empty($derived['derivedProducts'])) {
                $this->sumDerivedProductNetos($derived['derivedProducts'], $totalNeto);
            }
        }
    }

    /**
     * Formatea las proyecciones de productos derivados y agrega información adicional
     */
    private function formatDerivedProductsProjections($derivedProductsProjections, $request)
    {
        if (empty($derivedProductsProjections)) {
            return [];
        }

        $productIds = array_keys($derivedProductsProjections);

        // Obtener presentaciones de productos
        $productPresentations = SpGetProductPresentations::getAssociative(
            SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
            $productIds,
            Currency::PEN,
            0
        );

        // Obtener análisis de productos derivados
        $analysisData = $this->getDerivedProductsAnalysis($productIds, $request);

        $finalResults = [];

        foreach ($derivedProductsProjections as $productId => $projection) {
            // Obtener información del producto
            $product = $this->getProduct($productId);

            $formattedProjection = [
                'product_id' => $productId,
                'product_name' => $product->product_name,
                'unit_quantity_proyected' => $projection['unit_quantity_proyected'],
                'type' => $projection['type'],
                'recipes' => $projection['recipes'],
                'quantities_by_store' => $projection['quantities_by_store'],
                'source_dishes' => $projection['source_dishes'],
                'presentations' => $productPresentations[$productId] ?? [],
                'presentation' => $productPresentations[$productId][Presentation::UNIT_EQUIVALENCE] ?? null,
                'analisys' => $analysisData[$productId] ?? [],
                'unit_quantity_order' => 0 // Se calculará después
            ];

            // Calcular unit_quantity_order basado en el análisis
            if (!empty($formattedProjection['analisys'])) {
                $totalStock = 0;
                foreach ($formattedProjection['analisys'] as &$analysis) {
                    $analysis->unit_quantity_proyected = $projection['unit_quantity_proyected'];
                    $analysis->unit_quantity_order = max(0, $projection['unit_quantity_proyected'] - ($analysis->purchase_stock ?? 0));
                    $totalStock += $analysis->purchase_stock ?? 0;
                }
                $formattedProjection['unit_quantity_order'] = max(0, $projection['unit_quantity_proyected'] - $totalStock);
            } else {
                $formattedProjection['unit_quantity_order'] = $projection['unit_quantity_proyected'];
            }

            $finalResults[] = $formattedProjection;
        }

        return $finalResults;
    }

    /**
     * Obtiene el análisis de productos derivados para múltiples productos
     */
    private function getDerivedProductsAnalysis($productIds, $request)
    {
        if (empty($productIds)) {
            return [];
        }

        $store = $request->input('store', 0);
        $productIdsString = implode(',', $productIds);

        $query = "
            SELECT
                W.warehouse_id,
                W.warehouse_name,
                ST.store_id,
                ST.store_name,
                P.product_id,
                P.product_name,
                PR.measure_name,
                PD.equivalence AS equivalence_default,
                PD.measure_name AS measure_default,
                PP.person_id AS provider_id,
                XP.identification_number AS provider_number,
                REPLACE(XP.person_name, ',', ' ') AS provider,
                ROUND(ME.lpcost_pen * (1 + GV.igv), 4) AS unit_price,
                COALESCE(K.unit_stock, 0) AS stock,
                0 AS to_enter,
                0 AS to_dispatch,
                COALESCE(K.unit_stock, 0) AS purchase_stock,
                0 AS unit_quantity_proyected,
                0 AS unit_quantity_order
            FROM product P
                JOIN presentation PR ON PR.product_id = P.product_id AND PR.equivalence = 1
                JOIN presentation PD ON PD.product_id = P.product_id AND PD.default = 1
                JOIN merchandise ME ON ME.product_id = P.product_id
                JOIN subline SL ON SL.subline_id = ME.subline_id
                JOIN line LI ON LI.line_id = SL.line_id
                JOIN division DI ON DI.division_id = LI.division_id
                JOIN store ST ON ST.division_id = DI.division_id
                JOIN warehouse W ON W.store_id = ST.store_id
                LEFT JOIN kardex K ON K.product_id = P.product_id AND K.warehouse_id = W.warehouse_id
                LEFT JOIN product_provider PP ON PP.product_id = P.product_id AND PP.main = 1
                LEFT JOIN person XP ON XP.person_id = PP.person_id
                JOIN global_var GV ON GV.global_var_id = 1
            WHERE P.product_id IN ($productIdsString)
                AND ST.status = 1
                " . ($store > 0 ? "AND ST.store_id = $store" : "") . "
            ORDER BY P.product_id, W.warehouse_id
        ";

        $result = DB::select($query);

        // Agrupar por product_id
        $groupedAnalysis = [];
        foreach ($result as $row) {
            $groupedAnalysis[$row->product_id][] = $row;
        }

        return $groupedAnalysis;
    }

    private function getProductAnalysisData($productId, $request)
    {
        $store = $request->input('store', 0);
        $daysMinStock = $request->input('daysMinStock', 3);
        $daysOfReposition = $request->input('daysOfReposition', 7);
        $daysToOrder = $request->input('daysToOrder', 0);
        $provider = $request->input('provider', '');
        $division = $request->input('division', '');
        $line = $request->input('line', '');
        $subline = $request->input('subline', '');
        $scaleRotation = $request->input('scaleRotation', '');
        $expirationAlert = $request->input('expirationAlert', '');
        $obsoleteAlert = $request->input('obsoleteAlert', '');
        $estimatedOrder = $request->input('estimatedOrder', '');
        $sort = $request->input('sort', '');
        $order = $request->input('order', '');

        // Log de llamada al SP
        Log::info("call sp_get_reposition_analysis(" .
            $store . ", " .
            $daysMinStock . ", " .
            $daysOfReposition . ", " .
            $provider . ", " .
            $division . ", " .
            $line . ", " .
            $subline . ", " .
            $productId . ", " .
            $scaleRotation . ", " .
            $expirationAlert . ", " .
            $obsoleteAlert . ", " .
            $estimatedOrder . ", " .
            $daysToOrder . ", " .
            $sort . ", " .
            $order . ", " .
            "0, 0, '')");

        $query = "CALL sp_get_reposition_analysis(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $result = DB::select($query, [
            $store,           // xstore_id
            $daysMinStock,    // xdays_min_stock
            $daysOfReposition, // xdays_reposition
            $provider,        // xprovider_ids
            $division,        // xdivision_ids
            $line,            // xline_ids
            $subline,         // xsubline_ids
            $productId,       // xproduct_ids
            $scaleRotation,   // xscale_rotation
            $expirationAlert, // xexpiration_alert
            $obsoleteAlert,   // xobsolete_alert
            $estimatedOrder,  // xestimated_order
            $daysToOrder,     // xdays_to_order
            $sort,            // xsort
            $order,           // xorder
            0,                // xlimit
            0,                // xoffset
            ''                // xproduct_type
        ]);

        return $result;
    }

    private function getCustomAnalysisForDerivedProduct($productId)
    {
        // Primero obtener las proyecciones por tienda para este producto derivado
        $request = request();

        $query = "
            SELECT
                W.warehouse_id,
                W.warehouse_name,
                ST.store_id,
                ST.store_name,
                P.product_id,
                P.product_name,
                PR.measure_name,
                PD.equivalence AS equivalence_default,
                PD.measure_name AS measure_default,
                PP.person_id AS provider_id,
                XP.identification_number AS provider_number,
                REPLACE(XP.person_name, ',', ' ') AS provider,
                ROUND(ME.lpcost_pen * (1 + GV.igv), 4) AS unit_price,
                COALESCE(K.unit_stock, 0) AS stock,
                0 AS to_enter,
                0 AS to_dispatch,
                COALESCE(K.unit_stock, 0) AS purchase_stock,
                0 AS unit_quantity_proyected,
                0 AS unit_quantity_order
            FROM product P
                JOIN presentation PR ON PR.product_id = P.product_id AND PR.equivalence = 1
                JOIN presentation PD ON PD.product_id = P.product_id AND PD.default = 1
                JOIN merchandise ME ON ME.product_id = P.product_id
                LEFT JOIN product_provider PP ON PP.product_id = P.product_id
                LEFT JOIN person XP ON XP.person_id = PP.person_id
                JOIN global_var GV ON GV.organization_id = 1
                LEFT JOIN merchandise_master MM ON MM.product_id = P.product_id
                LEFT JOIN kardex K ON K.kardex_id = MM.kardex_id
                LEFT JOIN warehouse W ON W.warehouse_id = MM.warehouse_id
                LEFT JOIN store ST ON ST.store_id = W.store_id
            WHERE P.product_id = ?
                AND W.warehouse_type = 'Suministros'
                AND W.status = 1
                AND W.warehouse_id != 50
                AND ST.store_id IN (6,8,9,10)
                AND W.warehouse_name LIKE '%Food%'
                AND W.warehouse_name NOT LIKE '%Principal%'
            ORDER BY W.warehouse_id
        ";



        $result = DB::select($query, [$productId]);

        // Obtener proyecciones reales por tienda para este producto derivado
        $projectionsByStore = [];
        try {
            // TODO: Implementar método getDerivedProductProjections o usar método alternativo
            // Temporalmente comentado para evitar error "Method does not exist"
            Log::info("getDerivedProductProjections method temporarily disabled for product_id: {$productId}");
            // $projections = $this->getDerivedProductProjections([$productId], $request);
            // if (isset($projections[$productId])) {
            //     $productProjection = $projections[$productId];
            //     if (isset($productProjection['quantities_by_store'])) {
            //         $projectionsByStore = $productProjection['quantities_by_store'];
            //     }
            // }
        } catch (\Exception) {
            // Error silencioso, continuar sin proyecciones por tienda
        }

        // Aplicar lógica de waste_info y derived_products_sum para productos derivados
        if (!empty($result)) {
            // Obtener waste_info para este producto (UNA SOLA VEZ)
            $wastePercentages = $this->getWastePercentages([$productId]);

            // Calcular datos derivados UNA SOLA VEZ por producto
            $derivedSum = 0;
            $multiplier = 1;
            $finalProjection = null;

            if (isset($wastePercentages[$productId])) {
                $wasteInfo = $wastePercentages[$productId];
                $multiplier = $wasteInfo->multiplier_to_total ?? 1;

                // Obtener derivedProducts UNA SOLA VEZ
                $derivedProducts = $this->getDerivedProductsRecursive($productId, request(), []);

                if (!empty($derivedProducts)) {
                    // Calcular la suma de proyecciones de productos derivados
                    foreach ($derivedProducts as $derived) {
                        $derivedProjection = $derived['unit_quantity_proyected'] ?? 0;
                        $derivedSum += $derivedProjection;
                    }

                    // Aplicar multiplicador de waste_info
                    $finalProjection = $derivedSum * $multiplier;


                }
            }

            // Aplicar los valores calculados a TODOS los items del análisis
            $resultArray = [];
            foreach ($result as $analysisItem) {
                $itemArray = (array) $analysisItem;
                $storeId = $itemArray['store_id'] ?? null;

                // Aplicar proyección específica por tienda si existe
                if ($storeId && isset($projectionsByStore[$storeId])) {
                    $storeProjection = $projectionsByStore[$storeId];
                    $itemArray['unit_quantity_proyected'] = $storeProjection;
                    $itemArray['unit_quantity_order'] = max(0, $storeProjection - ($itemArray['purchase_stock'] ?? 0));

                }

                // Agregar waste_info si existe
                if (isset($wastePercentages[$productId])) {
                    $itemArray['waste_info'] = $wastePercentages[$productId];
                    $itemArray['derived_products_sum'] = $derivedSum;
                    $itemArray['waste_multiplier'] = $multiplier;

                    // Solo actualizar proyección global si no se aplicó proyección por tienda
                    if ($finalProjection !== null && !isset($projectionsByStore[$storeId])) {
                        $itemArray['unit_quantity_proyected'] = $finalProjection;
                        // Recalcular cantidad a ordenar
                        $itemArray['unit_quantity_order'] = max(0, $finalProjection - ($itemArray['purchase_stock'] ?? 0));
                    }
                }

                // Convertir de vuelta a objeto y agregar al resultado
                $resultArray[] = (object) $itemArray;
            }

            // Reemplazar el resultado original
            $result = $resultArray;
        }

        return $result;
    }
}
