import React from 'react';
import { Box } from '@mui/material';
import { keyframes } from '@emotion/react';
import styled from '@emotion/styled';

const pulse = keyframes`
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
`;

const AnimatedImg = styled.svg`
    animation: ${pulse} 0.5s infinite;
`;

const Loader = ({ height = 80, width = 80 }) => (
    <AnimatedImg width={width} height={height} viewBox="0 0 438 405" xmlns="http://www.w3.org/2000/svg">
        <path style={{ fill: '#f60', stroke: '#fff', strokeWidth: 2 }} d="M304.151.349h132.79v84.85h-132.79z" />
        <path
            d="M437.84 236v-75.89s-2-32.3-29.15-52.91L261.91 13.06s-28.47-24-74.08-6.87l-72.37 45.69s41.16-31.61 91.92-9.62c0 0 20.92 7.56 55.56 30.92l102.2 65.63s22.63 10 21.95 27.14l.34 23.36s.34 14.78 11.32 22c0-.01 13.03 10.69 39.09 24.69zM125.41 186.91l58-36.42s33.95-19.24 71.68 0L395.32 240s64.3 29.38 35.49 118c0 0-14.4 42.78-69.45 46.9 0 0-26.75 2.06-50.92-18l-75.11-47.41s-52.13-38.14-109.92-11.34l52.82-32.47s41.15-29.89 90.54 4.13l70 45.86s20.06 19.59 41.15.52c0 0 8.75-4.64 7.21-26.28l.47-13.91a34.08 34.08 0 0 0-16.46-21.65L248.19 206s-64.48-48.35-122.78-19.09z"
            style={{ fill: '#ff6b00', stroke: '#fff', strokeWidth: 2 }}
        />
        <path
            d="M226.56 51s-58-37.45-109.41 0L42.39 98.44S5.35 117.68.55 158.91c0 0-5.15 50.16 16.12 73.18 0 0 21.6 27.83 52.47 35a55.82 55.82 0 0 0 34.29-5.84l78.2-50.16 16.8-11s12-11 28.13-6.87c0 0-47.33-33.33-104.61-4.81l-27.09 17.91s-19.21 14.09-35.67-2.06c0 0-9.26-4.12-7.89-22v-18.88s1.38-15.12 15.78-21.3L197.4 59.27A42.6 42.6 0 0 1 226.56 51zM228.74 334.82s-49.57-34.79-103.33-6.7l-31 21.9s-29.07 16-40.13-17v-23.19s1.29-16.24-18.52-27.06-33.57-19.84-33.57-19.84v69.57s2.69 38.4 32.27 58c0 0 24.43 21.65 70.47 10.31 0 0 12.35-3.09 30.87-17l63-40.2s18.97-12.4 29.94-8.79z"
            style={{ fill: '#3dae2b', stroke: '#fff', strokeWidth: 2 }}
        />
        <path
            d="M319.921 9.329h25.38v8.18h-15v20.49h11.59v8.18h-11.59v23.46h15.4v8.17h-25.79l.01-68.48zM353.201 9.329h11.27c1.28 0 2.69.06 4.23.16 1.557.112 3.101.373 4.61.78a18.51 18.51 0 0 1 4.45 1.87 12.998 12.998 0 0 1 3.81 3.41 17.013 17.013 0 0 1 2.67 5.43 27.266 27.266 0 0 1 1 7.92v1.72c0 5.213-.75 9.456-2.25 12.73a14.06 14.06 0 0 1-6.61 7.08l11 27.38h-11l-9.1-25.62h-3.89v25.62h-10.19V9.329zm11.75 35.37a15.357 15.357 0 0 0 4.61-.6 6.264 6.264 0 0 0 3-2 8.96 8.96 0 0 0 1.68-3.77 27.06 27.06 0 0 0 .53-5.79v-5.42a18.591 18.591 0 0 0-.49-4.57 7.095 7.095 0 0 0-1.62-3.16 6.8 6.8 0 0 0-3-1.85 15.76 15.76 0 0 0-4.73-.6h-1.56v27.76h1.58zM395.221 9.329h10.39c1.31 0 2.75.06 4.31.18a24.02 24.02 0 0 1 4.69.84 17.26 17.26 0 0 1 4.53 2 13.585 13.585 0 0 1 3.87 3.71 18.923 18.923 0 0 1 2.71 5.87 31.258 31.258 0 0 1 1 8.58v2.41a40.637 40.637 0 0 1-.94 9.4 24.422 24.422 0 0 1-2.51 6.58 14.957 14.957 0 0 1-3.62 4.27 16.135 16.135 0 0 1-4.3 2.42 19.065 19.065 0 0 1-4.49 1.08 36.064 36.064 0 0 1-4.27.26h-1v20.85h-10.39l.02-68.45zm10.91 40a12.028 12.028 0 0 0 4.73-.83 6.692 6.692 0 0 0 3.07-2.56 12.086 12.086 0 0 0 1.64-4.49c.364-2.181.531-4.39.5-6.6v-6.15a27.242 27.242 0 0 0-.44-5.28 8.409 8.409 0 0 0-1.58-3.66 6.686 6.686 0 0 0-3.07-2.15 14.235 14.235 0 0 0-4.85-.7h-.52v32.44l.52-.02z"
            style={{ fill: '#fff', stroke: '#fff', strokeWidth: 2 }}
        />
    </AnimatedImg>
);

export const LoaderBox = ({ disableMargin = null, height = 80, width = 80, customMargin = '5rem' }) => (
    <Box sx={{ display: 'flex', justifyContent: 'center', marginY: disableMargin ? '1rem' : customMargin }}>
        <Loader height={height} width={width} />
    </Box>
);

export const BlockLoader = ({
    loading,
    children,
    disableLoader,
    disableMargin = null,
    height = 80,
    width = 80,
    customMargin = null,
    loaderComponent = null
}) => {
    if (loading) {
        if (disableLoader) {
            return null;
        }
        if (loaderComponent) {
            return <>{loaderComponent}</>;
        }
        return (
            <Box sx={{ height: '100%' }}>
                <Box sx={{ flexGrow: 1 }} />
                <LoaderBox disableMargin={disableMargin} height={height} width={width} customMargin={customMargin} />
                <Box sx={{ flexGrow: 1 }} />
            </Box>
        );
    }
    return <>{children}</>;
};

export default Loader;
