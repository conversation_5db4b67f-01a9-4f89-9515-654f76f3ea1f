export const MIN_FLOAT_QUANTITY = 0.0001;
export const MIN_INT_QUANTITY = 1;

export function round(value, decimals = 2) {
    if (typeof value === 'number') {
        return parseFloat(value.toFixed(decimals));
    }

    if (typeof value === 'string') {
        return parseFloat(parseFloat(value).toFixed(decimals));
    }
    return null;
}

export function getPercentage(value, total, roundNumber = null) {
    if (total === 0) {
        return 0;
    }
    const result = (value / total) * 100;

    if (roundNumber) {
        return round(result, roundNumber);
    }
    return result;
}

export function displayNumber(input, maxDecimals = 2) {
    const number = parseFloat(input);

    if (Number.isNaN(number)) {
        return 'Invalid number';
    }
    return number.toLocaleString('en-US', { minimumFractionDigits: maxDecimals, maximumFractionDigits: maxDecimals });
}

export function convertToDecimal(number) {
    return (number / 100).toFixed(8);
}

export function formatNumberWithCommas(value, decimals = 2) {
    const numericValue = typeof value === 'number' ? value : parseFloat(value || 0);

    if (Number.isNaN(numericValue)) {
        return '0.00';
    }

    return numericValue.toLocaleString('en-US', {
        minimumFractionDigits: decimals,
        maximumFractionDigits: decimals
    });
}
