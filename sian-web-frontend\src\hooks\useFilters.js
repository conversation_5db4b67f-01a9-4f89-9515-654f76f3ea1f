import { useState, useEffect, useCallback } from 'react';

export default function useFilters(initialFilters = {}, setFilters, setFiltersInSlice = null) {
    const [dataFilters, setDataFilters] = useState(initialFilters);

    const setValue = useCallback((name, value) => {
        setDataFilters((currentState) => ({ ...currentState, [name]: value }));
    }, []);

    useEffect(() => {
        const updatedFilters = {};

        Object.keys(dataFilters).forEach((key) => {
            const value = dataFilters[key];

            if (typeof value === 'string' && value.trim() !== '') {
                updatedFilters[key] = value;
            } else if (typeof value === 'number') {
                updatedFilters[key] = value;
            } else if (Array.isArray(value) && value.length > 0) {
                updatedFilters[key] = value;
            } else if (typeof value === 'boolean' && value === true) {
                updatedFilters[key] = true;
            } else if (typeof value === 'object' && value !== null && Object.keys(value).length > 0) {
                updatedFilters[key] = value;
            }
        });

        setFilters(updatedFilters);

        if (setFiltersInSlice) {
            setFiltersInSlice(updatedFilters);
        }
    }, [dataFilters]);

    return [dataFilters, setValue];
}
