import React, { useState } from 'react';
import {
    <PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    TextField,
    Typography,
    Alert,
    CircularProgress,
    Grid,
    Divider
} from '@mui/material';
import { 
    sendDtoToSianPromise, 
    sendPaymentToSianPromise, 
    sendTransformationOrderToSianPromise 
} from 'services/sianService';

/**
 * Example component showing how to send DTOs to SIAN
 * This is a demonstration component that shows different ways to use the SIAN service
 */
const SianDtoExample = () => {
    const [loading, setLoading] = useState(false);
    const [response, setResponse] = useState(null);
    const [error, setError] = useState(null);
    
    // Form state for credentials
    const [credentials, setCredentials] = useState({
        username: '',
        password: '',
        sianDomain: ''
    });

    // Example DTO data
    const [dtoData, setDtoData] = useState({
        // Example payment data structure
        amount: 100.00,
        currency: 'USD',
        description: 'Test payment',
        reference: 'REF-001'
    });

    const handleCredentialChange = (field) => (event) => {
        setCredentials(prev => ({
            ...prev,
            [field]: event.target.value
        }));
    };

    const handleDtoDataChange = (field) => (event) => {
        setDtoData(prev => ({
            ...prev,
            [field]: event.target.value
        }));
    };

    const clearMessages = () => {
        setResponse(null);
        setError(null);
    };

    // Example 1: Send generic DTO to custom endpoint
    const handleSendGenericDto = async () => {
        setLoading(true);
        clearMessages();
        
        try {
            const result = await sendDtoToSianPromise(
                dtoData,
                'your-custom-endpoint', // Replace with actual endpoint
                credentials.username,
                credentials.password,
                credentials.sianDomain
            );
            
            setResponse(result);
        } catch (err) {
            setError(err.message || 'Error sending DTO to SIAN');
        } finally {
            setLoading(false);
        }
    };

    // Example 2: Send payment data
    const handleSendPayment = async () => {
        setLoading(true);
        clearMessages();
        
        try {
            const paymentData = {
                ...dtoData,
                type: 'payment',
                timestamp: new Date().toISOString()
            };
            
            const result = await sendPaymentToSianPromise(
                paymentData,
                credentials.username,
                credentials.password,
                credentials.sianDomain
            );
            
            setResponse(result);
        } catch (err) {
            setError(err.message || 'Error sending payment to SIAN');
        } finally {
            setLoading(false);
        }
    };

    // Example 3: Send transformation order
    const handleSendTransformationOrder = async () => {
        setLoading(true);
        clearMessages();
        
        try {
            const transformationData = {
                ...dtoData,
                type: 'transformation_order',
                items: [
                    { id: 1, quantity: 10, unit: 'kg' },
                    { id: 2, quantity: 5, unit: 'pcs' }
                ],
                timestamp: new Date().toISOString()
            };
            
            const result = await sendTransformationOrderToSianPromise(
                transformationData,
                credentials.username,
                credentials.password,
                credentials.sianDomain
            );
            
            setResponse(result);
        } catch (err) {
            setError(err.message || 'Error sending transformation order to SIAN');
        } finally {
            setLoading(false);
        }
    };

    return (
        <Box sx={{ p: 3 }}>
            <Typography variant="h4" gutterBottom>
                SIAN DTO Integration Example
            </Typography>
            
            <Grid container spacing={3}>
                {/* Credentials Section */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                SIAN Credentials
                            </Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <TextField
                                    label="Username"
                                    value={credentials.username}
                                    onChange={handleCredentialChange('username')}
                                    fullWidth
                                />
                                <TextField
                                    label="Password"
                                    type="password"
                                    value={credentials.password}
                                    onChange={handleCredentialChange('password')}
                                    fullWidth
                                />
                                <TextField
                                    label="SIAN Domain"
                                    value={credentials.sianDomain}
                                    onChange={handleCredentialChange('sianDomain')}
                                    placeholder="https://your-sian-domain.com"
                                    fullWidth
                                />
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* DTO Data Section */}
                <Grid item xs={12} md={6}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Example DTO Data
                            </Typography>
                            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                                <TextField
                                    label="Amount"
                                    type="number"
                                    value={dtoData.amount}
                                    onChange={handleDtoDataChange('amount')}
                                    fullWidth
                                />
                                <TextField
                                    label="Currency"
                                    value={dtoData.currency}
                                    onChange={handleDtoDataChange('currency')}
                                    fullWidth
                                />
                                <TextField
                                    label="Description"
                                    value={dtoData.description}
                                    onChange={handleDtoDataChange('description')}
                                    fullWidth
                                />
                                <TextField
                                    label="Reference"
                                    value={dtoData.reference}
                                    onChange={handleDtoDataChange('reference')}
                                    fullWidth
                                />
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Actions Section */}
                <Grid item xs={12}>
                    <Card>
                        <CardContent>
                            <Typography variant="h6" gutterBottom>
                                Send DTO to SIAN
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                                <Button
                                    variant="contained"
                                    onClick={handleSendGenericDto}
                                    disabled={loading}
                                    startIcon={loading ? <CircularProgress size={20} /> : null}
                                >
                                    Send Generic DTO
                                </Button>
                                <Button
                                    variant="contained"
                                    color="secondary"
                                    onClick={handleSendPayment}
                                    disabled={loading}
                                    startIcon={loading ? <CircularProgress size={20} /> : null}
                                >
                                    Send Payment
                                </Button>
                                <Button
                                    variant="contained"
                                    color="success"
                                    onClick={handleSendTransformationOrder}
                                    disabled={loading}
                                    startIcon={loading ? <CircularProgress size={20} /> : null}
                                >
                                    Send Transformation Order
                                </Button>
                            </Box>
                        </CardContent>
                    </Card>
                </Grid>

                {/* Response Section */}
                <Grid item xs={12}>
                    {error && (
                        <Alert severity="error" sx={{ mb: 2 }}>
                            {error}
                        </Alert>
                    )}
                    
                    {response && (
                        <Alert severity="success" sx={{ mb: 2 }}>
                            <Typography variant="h6">Response from SIAN:</Typography>
                            <pre style={{ marginTop: 8, fontSize: '0.875rem' }}>
                                {JSON.stringify(response, null, 2)}
                            </pre>
                        </Alert>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
};

export default SianDtoExample;
