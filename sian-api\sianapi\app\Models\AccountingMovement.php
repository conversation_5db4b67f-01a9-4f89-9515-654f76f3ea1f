<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountingMovement
 * 
 * @property int $movement_id
 * @property int|null $accounting_file_id
 * @property int|null $year
 * @property int|null $period
 * @property int|null $day
 * @property int|null $correlative
 * @property string $gloss
 * @property int $entry_count
 * @property string $detraction_proof
 * @property Carbon|null $detraction_date
 * @property bool $group_entries
 * @property bool $has_combination
 * @property bool $checked
 * 
 * @property AccountingDay|null $accounting_day
 * @property AccountingPeriod|null $accounting_period
 * @property Movement $movement
 * @property Collection|Entry[] $entries
 * @property Collection|MovementSetting[] $movement_settings
 *
 * @package App\Models
 */
class AccountingMovement extends Model
{
	protected $table = 'accounting_movement';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'accounting_file_id' => 'int',
		'year' => 'int',
		'period' => 'int',
		'day' => 'int',
		'correlative' => 'int',
		'entry_count' => 'int',
		'group_entries' => 'bool',
		'has_combination' => 'bool',
		'checked' => 'bool'
	];

	protected $dates = [
		'detraction_date'
	];

	protected $fillable = [
		'accounting_file_id',
		'year',
		'period',
		'day',
		'correlative',
		'gloss',
		'entry_count',
		'detraction_proof',
		'detraction_date',
		'group_entries',
		'has_combination',
		'checked'
	];

	public function accounting_day()
	{
		return $this->belongsTo(AccountingDay::class, 'accounting_file_id')
					->where('accounting_day.accounting_file_id', '=', 'accounting_movement.accounting_file_id')
					->where('accounting_day.year', '=', 'accounting_movement.year')
					->where('accounting_day.period', '=', 'accounting_movement.period')
					->where('accounting_day.day', '=', 'accounting_movement.day');
	}

	public function accounting_period()
	{
		return $this->belongsTo(AccountingPeriod::class, 'accounting_file_id')
					->where('accounting_period.accounting_file_id', '=', 'accounting_movement.accounting_file_id')
					->where('accounting_period.year', '=', 'accounting_movement.year')
					->where('accounting_period.period', '=', 'accounting_movement.period');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}

	public function entries()
	{
		return $this->hasMany(Entry::class, 'movement_id');
	}

	public function movement_settings()
	{
		return $this->hasMany(MovementSetting::class, 'movement_id');
	}
}
