import fireSwal from 'utils/swal';
import React, { useEffect, useState, useCallback } from 'react';
import useFilters from 'hooks/useFilters';
import AddIcon from '@mui/icons-material/Add';
import People from '@mui/icons-material/People';
import { useDispatch } from 'store';
import { ProviderFilter } from './ProviderFilter';
import CloseIcon from '@mui/icons-material/Close';
import CategoryIcon from '@mui/icons-material/Category';
import ToggleButton from 'ui-component/inputs/ToggleButton';
import { RadioGroupList } from 'ui-component/inputs/Radio';
import QueryStatsIcon from '@mui/icons-material/QueryStats';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import { getDifferenceInDays, parseDataToSearchString, parseStringDateToDate } from 'utils/dates';
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Box,
    Button,
    ButtonGroup,
    FormLabel,
    IconButton,
    MenuItem,
    TextField
} from '@mui/material';
import { updateFilters } from 'store/slices/reposition/reposition';
import { ClasificationFilter } from './ClasificationFilter';
import { FOOD_VALUE, MARKET_VALUE, RAW_MATERIAL, SUPPLY } from 'models/Reposition';

export const CLASIFICATION = 'clasification';
export const PROVIDER = 'provider';

const rotationItems = [
    {
        value: 'AR',
        label: 'ALTA ROTACIÓN'
    },
    {
        value: 'MR',
        label: 'MEDIA ROTACIÓN'
    },
    {
        value: 'BR',
        label: 'BAJA ROTACIÓN'
    },
    {
        value: 'NR',
        label: 'NO ROTACIÓN'
    }
];

const breakItems = [
    {
        value: 'SS',
        label: 'SOBRE STOCKEADO'
    },
    {
        value: 'OPT',
        label: 'OPTIMO'
    },
    {
        value: 'MOD',
        label: 'MODERADO'
    },
    {
        value: 'CRI',
        label: 'CRÍTICO'
    },
    {
        value: 'QB',
        label: 'QUEBRADO'
    }
];

export default function RepositionFilter({ setFilters, handleSearch, disabled = false, isServerSideSort, setServerSideSort }) {
    const dispatch = useDispatch();
    const updateFiltersCallback = useCallback((data) => {
        dispatch(updateFilters(data));
    }, [dispatch]);

    const [dataFilters, setValue] = useFilters(
        {
            mode: MARKET_VALUE,
            daysOfReposition: 7,
            foodMode: SUPPLY,
            daysMinStock: 3,
            provider: [],
            division: [],
            line: [],
            subline: [],
            expirationAlert: '',
            obsoleteAlert: '',
            dateToOrder: parseDataToSearchString(new Date()),
            estimatedOrder: '',
            filterType: CLASIFICATION,
            rotationScale: [],
            breakScale: []
        },
        setFilters,
        updateFiltersCallback
    );

    const [showFilters, setShowFilter] = useState(false);

    useEffect(() => () => dispatch(updateFilters({})), [dispatch]);

    return (
        <Box sx={{ display: 'flex', flexDirection: 'row', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 2, flexDirection: 'column' }}>
                <ToggleButton
                    items={[
                        { value: MARKET_VALUE, label: 'Market' },
                        { value: FOOD_VALUE, label: 'Food' }
                    ]}
                    label="DIVISION"
                    value={dataFilters.mode}
                    height="4rem"
                    setValue={(value) => setValue('mode', value)}
                />

                {dataFilters.mode === FOOD_VALUE ? (
                    <ToggleButton
                        items={[
                            { value: SUPPLY, label: 'Insumos' },
                            { value: RAW_MATERIAL, label: 'M.Primas' }
                        ]}
                        label="TIPO DE INSUMO"
                        value={dataFilters.foodMode}
                        height="4rem"
                        setValue={(value) => setValue('foodMode', value)}
                    />
                ) : null}

                {/* <ToggleButton
                    items={[
                        { value: 'global', label: 'Global' },
                        { value: 'local', label: 'Local' }
                    ]}
                    value={isServerSideSort}
                    label="ORDENAMIENTO"
                    height="4rem"
                    setValue={(value) => setServerSideSort(value)}
                /> */}
                <Box sx={{ flexGrow: 1 }} />
            </Box>
            <Box
                sx={{
                    paddingBottom: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    overflowX: 'auto',
                    width: '100%',
                    gap: 2,
                    py: 1
                }}
            >
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
                    <Box sx={{ display: 'flex', gap: 2, flexDirection: 'row', width: '100%' }}>
                        <TextField
                            className="select-field"
                            label="DÍAS DE REPOSICIÓN"
                            name="daysOfReposition"
                            onChange={({ target: { name, value } }) => setValue(name, value)}
                            value={dataFilters.daysOfReposition}
                            select
                            fullWidth
                        >
                            <MenuItem value={1}>1 Días</MenuItem>
                            <MenuItem value={2}>2 Días</MenuItem>
                            <MenuItem value={7}>7 Días</MenuItem>
                            <MenuItem value={14}>2 Semanas</MenuItem>
                            <MenuItem value={21}>3 Semanas</MenuItem>
                            <MenuItem value={28}>4 Semanas</MenuItem>
                            <MenuItem value={30}>1 Mes</MenuItem>
                            <MenuItem value={60}>2 Meses</MenuItem>
                            <MenuItem value={90}>3 Meses</MenuItem>
                        </TextField>

                        <TextField
                            className="select-field"
                            label="DÍAS PARA EL STOCK MÍNIMO"
                            name="daysMinStock"
                            onChange={({ target: { name, value } }) => setValue(name, value)}
                            value={dataFilters.daysMinStock}
                            select
                            fullWidth
                        >
                            <MenuItem value={1}>1 Días</MenuItem>
                            <MenuItem value={2}>2 Días</MenuItem>
                            <MenuItem value={3}>3 Días</MenuItem>
                            <MenuItem value={7}>7 Días</MenuItem>
                            <MenuItem value={14}>2 Semanas</MenuItem>
                            <MenuItem value={21}>3 Semanas</MenuItem>
                            <MenuItem value={28}>4 Semanas</MenuItem>
                            <MenuItem value={30}>1 Mes</MenuItem>
                            <MenuItem value={60}>2 Meses</MenuItem>
                            <MenuItem value={90}>3 Meses</MenuItem>
                        </TextField>
                        <TextField
                            className="select-field"
                            label="FECHA DE PEDIDO"
                            name="dateToOrder"
                            type="date"
                            onChange={({ target: { name, value } }) => {
                                const newDate = parseStringDateToDate(value);
                                const daysDiference = getDifferenceInDays(new Date(), newDate);

                                if (daysDiference >= 0) {
                                    setValue(name, value);
                                } else {
                                    fireSwal('No se pueden elegir fechas pasadas', 'warning', 'top', true);
                                    setValue(name, parseDataToSearchString(new Date()));
                                }
                            }}
                            value={dataFilters.dateToOrder}
                            fullWidth
                        />
                        <ButtonGroup aria-label="filter actions">
                            <Button variant="contained" onClick={() => setShowFilter(!showFilters)} color="info">
                                {showFilters ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                            </Button>
                            <Button
                                variant="contained"
                                color="success"
                                onClick={() => handleSearch()}
                                disabled={!dataFilters.daysOfReposition || !dataFilters.daysMinStock}
                            >
                                <QueryStatsIcon />
                            </Button>
                        </ButtonGroup>
                    </Box>
                    {showFilters && (
                        <>
                            <Box sx={{ display: 'flex', gap: 2, flexDirection: 'row', width: '100%' }}>
                                <RadioGroupList
                                    items={[
                                        { label: 'Clasificación', value: CLASIFICATION },
                                        { label: 'Proveedor', value: PROVIDER }
                                    ]}
                                    icons={[<CategoryIcon />, <People />]}
                                    value={dataFilters.filterType}
                                    onChange={(value) => setValue('filterType', value)}
                                />
                                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, width: '100%' }}>
                                    <Box sx={{ display: 'flex', gap: 2, width: '100%' }}>
                                        <ClasificationFilter
                                            setValue={setValue}
                                            dataFilters={dataFilters}
                                            disabled={dataFilters.filterType !== CLASIFICATION}
                                        />
                                    </Box>
                                    <Box>
                                        <ProviderFilter
                                            setValue={setValue}
                                            dataFilters={dataFilters}
                                            disabled={dataFilters.filterType !== PROVIDER}
                                        />
                                    </Box>
                                </Box>
                            </Box>
                            <Box sx={{ display: 'flex', width: '100%', gap: 2 }}>
                                <Box sx={{ width: '100%' }}>
                                    <Box sx={{ display: 'flex', gap: 2, width: '100%', alignItems: 'end' }}>
                                        <TextField
                                            className="select-field"
                                            label="INDICE DE QUIEBRE"
                                            name="breakScale"
                                            onChange={({ target: { name, value } }) => setValue(name, value)}
                                            value={dataFilters.breakScale}
                                            SelectProps={{
                                                multiple: true,
                                                renderValue: (selected) =>
                                                    selected
                                                        .map((value) => breakItems.find((item) => item.value === value)?.value || value)
                                                        .join(', ')
                                            }}
                                            select
                                            fullWidth
                                        >
                                            {breakItems.map(({ value, label }, index) => (
                                                <MenuItem value={value} key={`ITEM_SCALE_RATE_${index}`}>
                                                    {value} - {label}
                                                </MenuItem>
                                            ))}
                                        </TextField>
                                        <TextField
                                            className="select-field"
                                            label="INDICE DE ROTACIÓN"
                                            name="rotationScale"
                                            onChange={({ target: { name, value } }) => setValue(name, value)}
                                            value={dataFilters.rotationScale}
                                            SelectProps={{
                                                multiple: true,
                                                renderValue: (selected) =>
                                                    selected
                                                        .map((value) => rotationItems.find((item) => item.value === value)?.value || value)
                                                        .join(', ')
                                            }}
                                            select
                                            fullWidth
                                        >
                                            {rotationItems.map(({ value, label }, index) => (
                                                <MenuItem value={value} key={`ITEM_SCALE_RATE_${index}`}>
                                                    {value} - {label}
                                                </MenuItem>
                                            ))}
                                        </TextField>
                                    </Box>
                                </Box>
                            </Box>
                        </>
                    )}
                </Box>
            </Box>
        </Box>
    );
}
