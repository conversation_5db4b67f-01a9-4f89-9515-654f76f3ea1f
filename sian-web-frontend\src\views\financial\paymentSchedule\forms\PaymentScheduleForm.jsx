import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'store';
import {
    Typo<PERSON>,
    Box,
    TextField,
    MenuItem,
    TableRow,
    TableCell,
    TableFooter,
    InputAdornment,
    Tooltip,
    Button,
    Input,
    Dialog,
    Slide,
    DialogActions,
    DialogContent,
    AppBar,
    Toolbar,
    IconButton,
    Divider,
    Chip,
    useTheme,
    useMediaQuery
} from '@mui/material';
import CustomOptionsMUIDtb from 'assets/customization/mui-datatable/CustomOptionsMUIDtb';
import CloseIcon from '@mui/icons-material/Close';
import { getDatesOfNextNDaysOfWeek } from 'utils/dates';
import ErrorIcon from '@mui/icons-material/Error';
import CancelIcon from '@mui/icons-material/Cancel';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import PaidOutlinedIcon from '@mui/icons-material/PaidOutlined';
import AccountBalanceIcon from '@mui/icons-material/AccountBalance';
import ReportProblemIcon from '@mui/icons-material/ReportProblem';
import HelpIcon from '@mui/icons-material/Help';
import useModal from 'hooks/useModal';
import { getCashboxBalances, getCashboxesByUser, savePaymentSchedule } from 'store/slices/payment-schedule/paymentSchedule';
import { LoaderBox } from 'ui-component/loaders/loaders';
import Swal from 'sweetalert2';
import { getScheduleSetting } from 'store/slices/payment-schedule/paymentScheduleSetting';
import CompareBalances from '../others/CompareBalances';
import CurrencyExchangeIcon from '@mui/icons-material/CurrencyExchange';
import TodayIcon from '@mui/icons-material/Today';
import SIANLink from 'ui-component/SIAN/SIANLink';
import Grid, { stickyColumn } from 'ui-component/grid/Grid';
import { useNavigate } from 'react-router-dom';
import { paymentScheduleRoute } from 'routes/routes';
import { round } from 'utils/number';
import BooleanLabel from 'ui-component/label/BooleanLabel';
import DisplayCurrency, { currencyValues } from 'ui-component/display/DisplayCurrency';
import DisplayRetentionAlert from 'ui-component/display/DisplayRetentionAlert';

import SelectAccount, { SelectBoxAccount } from './SelectAccount';
import { FEE_PAY_ROUTE } from 'utils/transactionUtils';

const Transition = React.forwardRef((props, ref) => <Slide direction="up" ref={ref} {...props} />);

const todayDate = new Date();

const Footer = ({ formData, day, nWeeks, setAmountPen, setAmountUsd }) => {
    const dates = [new Date(), ...getDatesOfNextNDaysOfWeek(day, nWeeks)];

    let amountSoles = 0;
    let amountDolars = 0;

    useEffect(() => {
        setAmountPen(0);
        setAmountUsd(0);
    }, [day, nWeeks]);

    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down('md'));

    return (

        isMobile ? (
            <Box sx={{ p: 2, borderTop: '1px solid #ccc' }}>
                <Typography variant="subtitle1" fontWeight="bold">Total Soles S/</Typography>
                {dates.map((date, index) => {
                    const value = formData.reduce((acc, item) => {
                        if (item.currency === 'S/') {
                            const field = item.fields.find(f => f.date.getUTCDate() === date.getUTCDate());
                            return acc + (field?.amount || 0);
                        }
                        return acc;
                    }, 0);
                    amountSoles += value;
                    if (index === dates.length - 1) {
                        setAmountPen(amountSoles);
                    }
                    return (
                        <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                            <Typography variant="body2">{(index === 0 ?
                                `INMEDIATO ${date
                                    .toLocaleDateString('es-PE', {
                                        month: '2-digit',
                                        day: '2-digit'
                                    })
                                    .replace(/^./, (letra) => letra.toUpperCase())
                                    .replace(/,/, '')
                                    .toUpperCase()}`
                                :
                                date
                                    .toLocaleDateString('es-PE', {
                                        month: '2-digit',
                                        day: '2-digit',
                                        weekday: 'long'
                                    })
                                    .replace(/^./, (letra) => letra.toUpperCase())
                                    .replace(/,/, '')
                                    .toUpperCase()
                            )}</Typography>
                            <Typography variant="body2">
                                {value === 0 ? '-' : <DisplayCurrency currency="pen" value={value} />}
                            </Typography>
                        </Box>
                    );
                })}
                <Typography variant="body2" fontWeight="bold" sx={{ mt: 1 }}>
                    Total: <DisplayCurrency currency="pen" value={amountSoles} />
                </Typography>

                <Typography variant="subtitle1" fontWeight="bold" sx={{ mt: 2 }}>Total Dólares $</Typography>
                {dates.map((date, index) => {
                    const value = formData.reduce((acc, item) => {
                        if (item.currency === '$') {
                            const field = item.fields.find(f => f.date.getUTCDate() === date.getUTCDate());
                            return acc + (field?.amount || 0);
                        }
                        return acc;
                    }, 0);
                    amountDolars += value;

                    if (index === dates.length - 1) {
                        setAmountUsd(amountDolars);
                    }
                    return (
                        <Box key={index} sx={{ display: 'flex', justifyContent: 'space-between', py: 0.5 }}>
                            <Typography variant="body2">
                                {(index === 0 ?
                                    `INMEDIATO ${date
                                        .toLocaleDateString('es-PE', {
                                            month: '2-digit',
                                            day: '2-digit'
                                        })
                                        .replace(/^./, (letra) => letra.toUpperCase())
                                        .replace(/,/, '')
                                        .toUpperCase()}`
                                    :
                                    date
                                        .toLocaleDateString('es-PE', {
                                            month: '2-digit',
                                            day: '2-digit',
                                            weekday: 'long'
                                        })
                                        .replace(/^./, (letra) => letra.toUpperCase())
                                        .replace(/,/, '')
                                        .toUpperCase()
                                )}
                            </Typography>
                            <Typography variant="body2">
                                {value === 0 ? '-' : <DisplayCurrency currency="usd" value={value} />}
                            </Typography>
                        </Box>
                    );
                })}
                <Typography variant="body2" fontWeight="bold" sx={{ mt: 1 }}>
                    Total: <DisplayCurrency currency="usd" value={amountDolars} />
                </Typography>
            </Box>
        ) : (
            <TableFooter className="mui-data-tables-footer">
                <TableRow>
                    <TableCell colSpan={7} rowSpan={4}>
                        <Typography variant="h3" sx={{ display: 'inline-flex' }}>
                            Total
                        </Typography>
                    </TableCell>

                    <TableCell colSpan={2} rowSpan={1}>
                        <Typography variant="h4" sx={{ display: 'inline-flex' }}>
                            Soles S/
                        </Typography>
                    </TableCell>
                    {dates.map((date, index) => {
                        const value = formData.reduce((accumulator, item) => {
                            const currency = item.currency;
                            if (currency === 'S/') {
                                const field = item.fields.find((fieldData) => fieldData.date.getUTCDate() === date.getUTCDate());
                                return accumulator + (field ? field.amount : 0);
                            }

                            return accumulator;
                        }, 0);
                        amountSoles += value;
                        if (index === dates.length - 1) {
                            setAmountPen(amountSoles);
                        }
                        return (
                            <TableCell colSpan={1} rowSpan={1}>
                                {value === 0 ? '-' : <DisplayCurrency currency="pen" value={value} variant="h4" />}
                            </TableCell>
                        );
                    })}
                    <TableCell colSpan={1} rowSpan={1}>
                        <DisplayCurrency currency="pen" value={amountSoles} variant="h4" bold />
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell colSpan={2} rowSpan={1}>
                        <Typography variant="h4" sx={{ display: 'inline-flex' }}>
                            Dólares $
                        </Typography>
                    </TableCell>
                    {dates.map((date, index) => {
                        const value = formData.reduce((accumulator, item) => {
                            const currency = item.currency;
                            if (currency === '$') {
                                const field = item.fields.find((fieldData) => fieldData.date.getUTCDate() === date.getUTCDate());
                                return accumulator + (field ? field.amount : 0);
                            }
                            return accumulator;
                        }, 0);
                        amountDolars += value;

                        if (index === dates.length - 1) {
                            setAmountUsd(amountDolars);
                        }
                        return (
                            <TableCell colSpan={1} rowSpan={1}>
                                {value === 0 ? '-' : <DisplayCurrency currency="usd" value={value} variant="h4" />}
                            </TableCell>
                        );
                    })}
                    <TableCell colSpan={1} rowSpan={1}>
                        <DisplayCurrency currency="usd" value={amountDolars} variant="h4" bold />
                    </TableCell>
                </TableRow>
            </TableFooter>
        )
    );
};

const PaymentPeriod = ({ id, formData, setFormData, date, handleOpenModalCashbox }) => {
    const [disabled, setDisabled] = useState(false);
    const [error, setError] = useState(false);
    const [amountText, setAmountText] = useState('');
    const rowField = formData.find((data) => data.id === id);
    const dateField = rowField?.fields.find((data) => data.date.getUTCDate() === date.getUTCDate());

    function validate(row) {
        const remainingAmount = Number(Number(row.remainingAmount).toFixed(2));
        const currentAmountSum = Number(row.fields.reduce((sum, field) => sum + field.amount, 0).toFixed(2));
        setError(currentAmountSum > remainingAmount);
    }

    function getCurrency() {
        const currentFormData = [...formData];
        const indexDataForm = currentFormData.findIndex((data) => data.id === id);
        if (indexDataForm >= 0) {
            return currentFormData[indexDataForm].currency;
        }

        return 'Moneda no Especificada';
    }

    function autocompleteField() {
        const currentFormData = [...formData];
        const indexDataForm = currentFormData.findIndex((data) => data.id === id);

        if (indexDataForm >= 0) {
            const rowData = currentFormData[indexDataForm];

            const amountOnFields = rowData.fields.reduce((acc, element) => {
                if (element.date.getUTCDate() === date.getUTCDate()) {
                    return acc;
                }
                return acc + element.amount;
            }, 0);

            if (amountOnFields < rowData.remainingAmount) {
                setAmountText(Number((rowData.remainingAmount - amountOnFields).toFixed(2)));
            } else {
                setError(true);
            }
        }
    }

    useEffect(() => amountText !== '' && setDisabled(true), [amountText]);

    useEffect(() => {
        if (rowField && dateField?.amount > 0) {
            setDisabled(true);
            setAmountText(Number(dateField.amount.toFixed(2)));
        } else {
            setDisabled(false);
        }
    }, [dateField?.amount]);

    function setValueOnForm(amount) {
        const currentFormData = [...formData];
        const indexDataForm = currentFormData.findIndex((data) => data.id === id);
        const indexCellData = currentFormData[indexDataForm].fields.findIndex((data) => data.date.getUTCDate() === date.getUTCDate());

        if (indexCellData >= 0) {
            if (amount.trim() === '' || parseFloat(amount) <= 0) {
                currentFormData[indexDataForm].fields.splice(indexCellData, 1);
                validate(currentFormData[indexDataForm]);
            } else {
                const currentField = currentFormData[indexDataForm].fields[indexCellData];
                currentFormData[indexDataForm].fields[indexCellData] = {
                    ...currentField,
                    date,
                    amount: parseFloat(amount)
                };
                setAmountText(parseFloat(amount).toFixed(2));

                validate(currentFormData[indexDataForm]);
            }
        } else if (amount.trim() !== '' || parseFloat(amount) > 0) {
            const cashbox = currentFormData[indexDataForm].cashbox;
            if (cashbox) {
                currentFormData[indexDataForm].fields.push({
                    date,
                    amount: parseFloat(amount),
                    cashboxID: cashbox.cashboxID,
                    cashbox
                });
            } else {
                currentFormData[indexDataForm].fields.push({
                    date,
                    amount: parseFloat(amount)
                });
            }
            setAmountText(parseFloat(amount).toFixed(2));
            validate(currentFormData[indexDataForm]);
        }

        setFormData(currentFormData);
    }

    return (
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <div
                style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    minWidth: '8rem',
                    minHeight: '3rem',
                    width: '100%'
                }}
                onClick={() => {
                    setDisabled(true);
                    setError(false);
                }}
            >
                {disabled ? (
                    <Box sx={{ display: 'flex', gap: 1, flexDirection: 'column' }}>
                        <TextField
                            sx={{ fontFamily: `'Azeret Mono', monospace` }}
                            type="number"
                            error={error}
                            variant="standard"
                            onBlur={({ target: { value } }) => {
                                setValueOnForm(value);
                                if (value.trim() === '') {
                                    setDisabled(false);
                                }
                            }}
                            value={amountText}
                            onChange={({ target: { value } }) => setAmountText(value)}
                            InputProps={{
                                startAdornment: <InputAdornment position="start">{getCurrency()}</InputAdornment>,
                                inputProps: {
                                    style: { textAlign: 'right' },
                                    inputMode: 'decimal'
                                }
                            }}
                            autoFocus
                        />
                        {dateField ? (
                            <Typography color="textPrimary" noWrap>
                                {dateField.cashbox?.cashboxName ?? null}
                            </Typography>
                        ) : null}
                        {error ? (
                            <Tooltip title="La suma de los montos ha superado el Saldo" disableFocusListener>
                                <ErrorIcon color="error" />
                            </Tooltip>
                        ) : null}
                    </Box>
                ) : null}
            </div>

            <Tooltip title="Autocompletar Monto" disableFocusListener onClick={() => autocompleteField()}>
                <PaidOutlinedIcon color="success" />
            </Tooltip>
            {dateField ? (
                <Tooltip
                    title="Elegir Cuenta"
                    onClick={() =>
                        handleOpenModalCashbox({ selectedDate: date, documentSelected: id, cashboxSelected: dateField?.cashboxID ?? null })
                    }
                    disableFocusListener
                >
                    <AccountBalanceIcon color="info" />
                </Tooltip>
            ) : null}
        </Box>
    );
};

const validateForm = (data) => {
    const messages = [];

    data.forEach((element) => {
        if (element.scheduledAmount === 0) {
            const message = (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SIANLink route={element.route} id={element.movementId}>
                        {element.document}
                    </SIANLink>
                    no tiene montos en la programación
                </Box>
            );
            messages.push(message);
        }
        if (element.newRemainingAmount < 0) {
            const message = (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SIANLink route={element.route} id={element.movementId}>
                        {element.document}
                    </SIANLink>
                    Se ha superado al saldo por {element.currency} {round(element.newRemainingAmount * -1).toFixed(2)}
                </Box>
            );
            messages.push(message);
        }
        if (element.route === FEE_PAY_ROUTE || (element.isDetraction && element.isDetraction === 1)) {
            if (element.fields.length > 1) {
                const message = (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SIANLink route={element.route} id={element.movementId}>
                            {element.document}
                        </SIANLink>
                        es una {element.route === FEE_PAY_ROUTE ? 'OBLIGACIÓN FINANCIERA' : 'DETRACCIÓN'} solo debe tener una fecha de pago
                    </Box>
                );
                messages.push(message);
            }
            if (element.scheduledAmount < element.total) {
                const message = (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <SIANLink route={element.route} id={element.movementId}>
                            {element.document}
                        </SIANLink>
                        es una {element.route === FEE_PAY_ROUTE ? 'OBLIGACIÓN FINANCIERA' : 'DETRACCIÓN'} se debe pagar su totalidad
                    </Box>
                );
                messages.push(message);
            }
        }
    });

    return messages.length === 0 ? true : messages;
};

const ErrorDisplay = ({ open, handleClose, errors }) => (
    <Dialog
        className="lal-dialog"
        style={{ overflow: 'hidden' }}
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition}
        maxWidth="lg"
    >
        <DialogContent sx={{ px: '5rem' }}>
            <Box sx={{ paddingY: 2, display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                <ReportProblemIcon sx={{ fontSize: '10rem', marginY: 3 }} color="warning" />
                <Typography variant="h4">Se encontraron algunos errores con los datos ingresados</Typography>
            </Box>
            <Box sx={{ paddingY: 2, marginY: 2, display: 'flex', flexDirection: 'column', gap: 1 }}>
                {errors.map((error) => (
                    <Typography>{error}</Typography>
                ))}
            </Box>
        </DialogContent>

        <Divider />
        <DialogActions
            sx={{
                display: 'flex',
                justifyContent: 'center',
                marginX: '1rem',
                '& .MuiBox-root': {
                    display: 'flex',
                    gap: 2
                }
            }}
        >
            <Button variant="contained" color="error" onClick={handleClose} size="large">
                Cerrar
            </Button>
        </DialogActions>
    </Dialog>
);

const ConfirmSave = ({ data, open, handleClose, day, nWeeks, handleFormSubmit }) => {
    const dispatch = useDispatch();
    const { loading } = useSelector((state) => state.paymentSchedule);

    const [description, setDescription] = useState('');

    const dates = [new Date(), ...getDatesOfNextNDaysOfWeek(day, nWeeks)];
    let amountSoles = 0;
    let amountDolars = 0;

    const saveData = () => {
        dispatch(savePaymentSchedule(data, description)).then((response) => {
            if (response === true) {
                handleClose();
                handleFormSubmit();
                Swal.fire({
                    position: 'bottom',
                    toast: true,
                    timerProgressBar: true,
                    icon: 'success',
                    title: 'Se guardó la programación!',
                    showConfirmButton: false,
                    timer: 3000
                });
            } else {
                Swal.fire({
                    position: 'bottom',
                    toast: true,
                    timerProgressBar: true,
                    icon: 'error',
                    title: 'Hubo un error',
                    showConfirmButton: false,
                    timer: 3000
                });
            }
        });
    };

    return (
        <Dialog
            className="lal-dialog"
            style={{ overflow: 'hidden' }}
            open={open}
            onClose={handleClose}
            TransitionComponent={Transition}
            maxWidth="md"
        >
            {loading ? (
                <Box sx={{ paddingY: '6rem', paddingX: '8rem', display: 'flex', alignItems: 'center', flexDirection: 'column', gap: 2 }}>
                    <LoaderBox />
                    <Typography variant="h3">Guardando...</Typography>
                </Box>
            ) : (
                <>
                    <AppBar sx={{ position: 'relative' }}>
                        <Toolbar>
                            <Typography sx={{ ml: 0, flex: 1, color: '#ffffff' }} variant="h3" component="div">
                                Guardar Programación
                            </Typography>
                            <IconButton edge="end" color="inherit" onClick={handleClose} aria-label="close">
                                <CloseIcon />
                            </IconButton>
                        </Toolbar>
                    </AppBar>
                    <DialogContent sx={{ paddingY: 1, paddingX: '5rem' }}>
                        <Box sx={{ paddingY: 2, display: 'flex', alignItems: 'center', flexDirection: 'column' }}>
                            <HelpIcon sx={{ fontSize: '10rem', marginY: 3 }} color="primary" />
                            <Typography variant="h3">¿Desea guardar la programación de pago?</Typography>
                        </Box>
                        <Box sx={{ paddingY: 2, width: '100%' }}>
                            <Input
                                multiline
                                fullWidth
                                placeholder="Ingresa la descripción de la Programación de Pago"
                                onChange={({ target: { value } }) => setDescription(value)}
                            />
                        </Box>
                        <Box sx={{ paddingY: 2, marginY: 2 }}>
                            <TableFooter>
                                <TableRow>
                                    <TableCell colSpan={1} rowSpan={1}>
                                        <h3>Fechas</h3>
                                    </TableCell>
                                    {dates.map((date) => (
                                        <TableCell colSpan={1} rowSpan={1}>
                                            <div style={{ textAlign: 'center' }}>
                                                <h3>
                                                    {date
                                                        .toLocaleDateString('es-PE', {
                                                            month: '2-digit',
                                                            day: '2-digit',
                                                            weekday: 'long'
                                                        })
                                                        .replace(/^./, (letra) => letra.toUpperCase())
                                                        .replace(/,/, '')}
                                                </h3>
                                            </div>
                                        </TableCell>
                                    ))}
                                    <TableCell colSpan={1} rowSpan={1}>
                                        <div style={{ textAlign: 'center' }}>
                                            <h3>TOTAL</h3>
                                        </div>
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell colSpan={1} rowSpan={1}>
                                        <h3>Soles</h3>
                                    </TableCell>
                                    {dates.map((date) => {
                                        const value = data.reduce((accumulator, item) => {
                                            const currency = item.currency;
                                            if (currency === 'S/') {
                                                const field = item.fields.find(
                                                    (fieldData) => fieldData.date.getUTCDate() === date.getUTCDate()
                                                );
                                                return accumulator + (field ? field.amount : 0);
                                            }
                                            return accumulator;
                                        }, 0);
                                        amountSoles += value;
                                        return (
                                            <TableCell colSpan={1} rowSpan={1}>
                                                <DisplayCurrency currency="pen" value={value} />
                                            </TableCell>
                                        );
                                    })}
                                    <TableCell colSpan={1} rowSpan={1}>
                                        <DisplayCurrency currency="pen" value={amountSoles} bold />
                                    </TableCell>
                                </TableRow>
                                <TableRow>
                                    <TableCell colSpan={1} rowSpan={1}>
                                        <h3>Dólares</h3>
                                    </TableCell>
                                    {dates.map((date) => {
                                        const value = data.reduce((accumulator, item) => {
                                            const currency = item.currency;
                                            if (currency === '$') {
                                                const field = item.fields.find(
                                                    (fieldData) => fieldData.date.getUTCDate() === date.getUTCDate()
                                                );
                                                return accumulator + (field ? field.amount : 0);
                                            }
                                            return accumulator;
                                        }, 0);

                                        amountDolars += value;
                                        return (
                                            <TableCell colSpan={1} rowSpan={1}>
                                                <DisplayCurrency currency="usd" value={value} />
                                            </TableCell>
                                        );
                                    })}
                                    <TableCell colSpan={1} rowSpan={1}>
                                        <DisplayCurrency currency="usd" value={amountDolars} bold />
                                    </TableCell>
                                </TableRow>
                            </TableFooter>
                        </Box>
                    </DialogContent>
                    <Divider />
                    <DialogActions
                        sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            marginX: '1rem',
                            '& .MuiBox-root': {
                                display: 'flex',
                                gap: 2
                            }
                        }}
                    >
                        <Button variant="contained" color="primary" onClick={saveData} size="large">
                            Guardar
                        </Button>

                        <Button variant="contained" color="error" onClick={handleClose} size="large">
                            Cerrar
                        </Button>
                    </DialogActions>
                </>
            )}
        </Dialog>
    );
};

export default function PaymentScheduleForm({ selectedDocuments, setSelectedDocuments, handleFormSubmit }) {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { data: dataSetting, loading: loadingDay } = useSelector((state) => state.paymentScheduleSetting);
    const { cashboxBalances, cashboxes } = useSelector((state) => state.paymentSchedule);
    const [step, setStep] = useState(0);
    const [open, handleOpen, handleClose] = useModal();
    const [openConfirm, handleOpenConfirm, handleCloseConfirm] = useModal();
    const [formData, setFormData] = useState([]);
    const [errors, setError] = useState(null);

    const [amountPen, setAmountPen] = useState(0);
    const [amountUsd, setAmountUsd] = useState(0);

    const [dateSelected, setDateSelected] = useState(null);
    const [documentSelected, setDocumentSelected] = useState(null);
    const [cashboxSelected, setCashboxSelected] = useState(null);
    const [isOpenModalCashbox, openModalCashbox, closeModalCashbox] = useModal();

    const handleOpenModalCashbox = ({ selectedDate = null, documentSelected = null, cashboxSelected = null }) => {
        setDateSelected(selectedDate || null);
        setDocumentSelected(documentSelected || null);
        setCashboxSelected(cashboxSelected || null);
        openModalCashbox();
    };

    const deleteDocument = (id) => {
        setFormData((prevFormData) => {
            const indexToRemove = prevFormData.findIndex((row) => row.id === id);

            if (indexToRemove !== -1) {
                const updatedFormData = [...prevFormData];
                updatedFormData.splice(indexToRemove, 1);
                return updatedFormData;
            }

            return prevFormData;
        });

        setSelectedDocuments((prevSelectedDocuments) => prevSelectedDocuments.filter((document) => document.pk !== id));
    };

    const RenderRetentionDetractionStatus = ({ documentData }) => {
        const renderChip = (label, color) => <Chip label={label} color={color} size="small" sx={{ borderRadius: 0, fontSize: '0.8rem' }} />;

        if (documentData?.isDetraction) {
            return renderChip('DET', 'primary');
        }

        if (documentData?.is_retention_affected) {
            return renderChip('RET', 'success');
        }

        return null;
    };

    const deleteAll = () => {
        setFormData([]);
        setSelectedDocuments([]);
    };

    const autocompleteAmountColumns = (date) => {
        selectedDocuments.forEach(({ pk }) => {
            const currentFormData = [...formData];
            const indexDataForm = currentFormData.findIndex((data) => data.id === pk);

            if (indexDataForm >= 0) {
                const indexCellData = currentFormData[indexDataForm].fields.findIndex(
                    (data) => data.date.getUTCDate() === date.getUTCDate()
                );
                const rowData = currentFormData[indexDataForm];

                const amountOnFields = rowData.fields.reduce((acc, element) => {
                    if (element.date.getUTCDate() === date.getUTCDate()) {
                        return acc;
                    }
                    return acc + element.amount;
                }, 0);

                const amount = rowData.remainingAmount - amountOnFields;

                if (indexCellData >= 0) {
                    const currendField = currentFormData[indexDataForm].fields[indexCellData];
                    currentFormData[indexDataForm].fields[indexCellData] = {
                        ...currendField,
                        date,
                        amount: parseFloat(amount)
                    };
                } else if (parseFloat(amount) > 0) {
                    const cashbox = currentFormData[indexDataForm].cashbox;
                    if (cashbox) {
                        currentFormData[indexDataForm].fields.push({
                            date,
                            amount: parseFloat(amount),
                            cashboxID: cashbox.cashboxID,
                            cashbox
                        });
                    } else {
                        currentFormData[indexDataForm].fields.push({
                            date,
                            amount: parseFloat(amount)
                        });
                    }
                }

                setFormData(currentFormData);
            }
        });
    };

    const autocompleteCashBoxColumns = (date, cashbox, inputSelected) => {
        if (inputSelected) {
            const currentFormData = [...formData];
            selectedDocuments.forEach(({ pk }) => {
                const indexDataForm = currentFormData.findIndex((data) => data.id === pk);

                if (indexDataForm >= 0) {
                    const indexCellData = currentFormData[indexDataForm].fields.findIndex(
                        (data) => data.date.getUTCDate() === date.getUTCDate()
                    );
                    const isDetraction = selectedDocuments[indexDataForm].isDetraction || 0;
                    const currency = currencyValues[currentFormData[indexDataForm].currency];
                    if (indexCellData >= 0) {
                        if (isDetraction === 1) {
                            if (inputSelected === 'det' && cashbox.isDetraction !== 1) {
                                const currendField = currentFormData[indexDataForm].fields[indexCellData];
                                currentFormData[indexDataForm].fields[indexCellData] = {
                                    ...currendField,
                                    cashboxID: cashbox.cashboxID,
                                    cashbox
                                };
                            }
                        } else if (currency === cashbox.currency && inputSelected !== 'det') {
                            const currendField = currentFormData[indexDataForm].fields[indexCellData];
                            currentFormData[indexDataForm].fields[indexCellData] = {
                                ...currendField,
                                cashboxID: cashbox.cashboxID,
                                cashbox
                            };
                        }
                    }
                }
            });
            setFormData(currentFormData);
        }
    };

    const autocompleteCashBoxRow = (pk, cashboxID) => {
        const currentFormData = [...formData];
        const indexDataForm = currentFormData.findIndex((data) => data.id === pk);
        const cashbox = cashboxes.find((cb) => cb.cashboxID === cashboxID);
        currentFormData[indexDataForm] = { ...currentFormData[indexDataForm], cashbox };

        currentFormData[indexDataForm].fields.forEach((field, indexField) => {
            currentFormData[indexDataForm].fields[indexField] = {
                ...field,
                cashboxID,
                cashbox
            };
        });

        setFormData(currentFormData);
    };

    const setCashboxField = (cashbox) => {
        const currentFormData = [...formData];
        const indexDataForm = currentFormData.findIndex((data) => data.id === documentSelected);
        const indexCellData = currentFormData[indexDataForm].fields.findIndex(
            (data) => data.date.getUTCDate() === dateSelected.getUTCDate()
        );

        if (indexCellData >= 0) {
            const currentField = currentFormData[indexDataForm].fields[indexCellData];
            if (cashbox) {
                currentFormData[indexDataForm].fields[indexCellData] = {
                    ...currentField,
                    cashboxID: cashbox.cashboxID,
                    cashbox
                };
            } else {
                delete currentFormData[indexDataForm].fields[indexCellData].cashbox;
                delete currentFormData[indexDataForm].fields[indexCellData].cashboxID;
            }
        }

        setFormData(currentFormData);
    };

    const initialColumns = [
        {
            label: 'Id',
            name: 'pkView',
            options: {
                filter: false,
                display: false
            }
        },
        {
            label: 'Eliminar',
            name: 'pk',
            options: {
                filter: false,
                display: true,
                customBodyRender: (value) => (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }} onClick={() => deleteDocument(value)}>
                        <CancelIcon color="error" fontSize="large" />
                    </Box>
                ),
                customHeadRender: () => (
                    <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }} onClick={() => deleteAll()}>
                        <Tooltip title="Quitar Todos" placement="right" disableFocusListener>
                            <CancelIcon color="error" fontSize="large" />
                        </Tooltip>
                    </Box>
                )
            }
        },
        {
            label: 'Documento',
            name: 'document',
            options: {
                filter: true,
                display: true,
                ...stickyColumn,
                customBodyRender: (value, { rowIndex }) => (
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <SIANLink id={selectedDocuments[rowIndex]?.movementId} route={selectedDocuments[rowIndex]?.route}>
                            {value}
                        </SIANLink>
                        <RenderRetentionDetractionStatus documentData={selectedDocuments[rowIndex]} />
                    </Box>
                )
            }
        },
        {
            label: 'Prov?',
            name: 'isProvisioned',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) => <BooleanLabel value={value === 1} />
            }
        },
        {
            label: 'T.Documento',
            name: 'documentType',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'Detalle',
            name: 'detail',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'RUC/DNI',
            name: 'identificationNumber',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'Socio de Negocio',
            name: 'auxPersonName',
            options: {
                filter: true,
                customBodyRender: (value) => {
                    const maxLength = 28;
                    const truncatedValue = value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;
                    return <Box sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{truncatedValue}</Box>;
                }
            }
        },
        {
            label: 'Solicitante',
            name: 'personName',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) => {
                    const maxLength = 25;
                    const truncatedValue = value.length > maxLength ? `${value.substring(0, maxLength)}...` : value;
                    return <Box sx={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>{truncatedValue}</Box>;
                }
            }
        },
        {
            label: 'Método de Pago',
            name: 'paymentMethod',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'Cuenta de Salida',
            name: 'pk',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value, tableMeta) => (
                    <SelectBoxAccount
                        documents={selectedDocuments}
                        setDocuments={(cashbox) => autocompleteCashBoxRow(value, cashbox)}
                        boxAccounts={cashboxes}
                        documentPk={value}
                    />
                )
            }
        },
        {
            label: 'F.Emisión',
            name: 'emissionDate',
            options: {
                filter: true,
                display: false,
                customBodyRender: (value) =>
                    `${value.toLocaleDateString('es-PE', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    })}`
            }
        },
        {
            label: 'F.Venc.',
            name: 'expirationDate',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) =>
                    `${value.toLocaleDateString('es-PE', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit'
                    })}`
            }
        },
        {
            label: 'Vence en',
            name: 'daysRemaining',
            options: {
                filter: false,
                display: false,
                customBodyRender: (value) => (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 1 }}>
                        <Typography>{value}</Typography>
                        {value < 1 ? <ErrorIcon color="error" /> : value === 0 && <ErrorIcon color="warning" />}
                    </Box>
                )
            }
        },
        {
            label: 'Mon',
            name: 'currency',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'Total',
            name: 'total',
            options: {
                filter: false,
                display: true,
                customBodyRender: (value, { rowData }) => {
                    const pk = rowData[0];
                    const index = selectedDocuments.findIndex((row) => row.pk === pk);
                    if (index >= 0) {
                        return <DisplayCurrency value={value} currency={selectedDocuments[index].currency} />;
                    }
                    return null;
                }
            }
        },
        {
            label: 'Saldo',
            name: 'balance',
            options: {
                filter: false,
                display: true,
                customBodyRender: (value, { rowData }) => {
                    const pk = rowData[0];
                    const index = selectedDocuments.findIndex((row) => row.pk === pk);
                    if (index >= 0) {
                        return <DisplayCurrency value={value} currency={selectedDocuments[index].currency} />;
                    }
                    return null;
                }
            }
        },
        {
            label: `INMEDIATO ${todayDate
                .toLocaleDateString('es-PE', {
                    month: '2-digit',
                    day: '2-digit'
                })
                .replace(/^./, (letra) => letra.toUpperCase())
                .replace(/,/, '')
                .toUpperCase()}`,
            name: 'pk',
            options: {
                filter: false,
                display: true,
                minWidth: 300,
                customBodyRender: (value) => (
                    <PaymentPeriod
                        id={value}
                        formData={formData}
                        setFormData={setFormData}
                        date={new Date()}
                        handleOpenModalCashbox={handleOpenModalCashbox}
                    />
                ),
                customHeadRender: () => (
                    <th>
                        <Box sx={{ display: 'flex', width: '100%', gap: 1, px: 2, justifyContent: 'space-between' }}>
                            <Typography>
                                {`INMEDIATO ${todayDate
                                    .toLocaleDateString('es-PE', {
                                        month: '2-digit',
                                        day: '2-digit'
                                    })
                                    .replace(/^./, (letra) => letra.toUpperCase())
                                    .replace(/,/, '')
                                    .toUpperCase()}`}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                                <Tooltip title="Autocompletar Monto" onClick={() => autocompleteAmountColumns(todayDate)} disableFocusListener>
                                    <PaidOutlinedIcon color="success" />
                                </Tooltip>
                                <Tooltip
                                    title="Elegir Cuenta"
                                    disableFocusListener
                                    onClick={() => handleOpenModalCashbox({ selectedDate: todayDate })}
                                >
                                    <AccountBalanceIcon color="info" />
                                </Tooltip>
                            </Box>
                        </Box>
                    </th>
                )
            }
        }
    ];

    const [finalData, setFinalData] = useState([]);
    const [columns, setColumns] = useState(initialColumns);
    const [nWeeks, setNWeeks] = useState('');
    const [day, setDay] = useState(null);

    const submit = () => {
        if (selectedDocuments.length > 0) {
            const finalData = selectedDocuments.map((document, index) => {
                const scheduledAmount = formData[index].fields.reduce((acc, item) => acc + item.amount, 0);
                const newRemainingAmount = document.balance - scheduledAmount;

                return {
                    ...document,
                    fields: formData[index].fields,
                    scheduledAmount,
                    newRemainingAmount
                };
            });

            const validationResult = validateForm(finalData);

            if (validationResult !== true) {
                setError(validationResult);
                setFinalData(null);
            } else {
                setError(null);
                setFinalData(finalData);
                handleOpenConfirm();
            }
        } else {
            setError(['No hay documentos que programar']);
            setFinalData(null);
        }
    };

    const options = {
        search: true,
        download: false,
        print: true,
        sort: false,
        viewColumns: true,
        filter: true,
        filterType: 'multiselect',
        confirmFilters: false,
        responsive: 'vertical',
        fixedHeader: true,
        fixedSelectColumn: true,
        textLabels: CustomOptionsMUIDtb.textLabels,
        jumpToPage: true,
        resizableColumns: false,
        draggableColumns: {
            enabled: true
        },
        selectableRows: 'none',
        selectableRowsOnClick: false,
        pagination: false,
        rowHover: true,
        customTableBodyFooterRender: () => (
            <Footer
                formData={formData}
                day={day}
                nWeeks={nWeeks}
                balances={cashboxBalances}
                setAmountPen={setAmountPen}
                setAmountUsd={setAmountUsd}
            />
        )
    };

    const getDatesOffPeriods = (day, nWeeks, formData, setFormData) => {
        const dates = getDatesOfNextNDaysOfWeek(day, nWeeks);

        const newColumns = dates.map((date) => ({
            label: date
                .toLocaleDateString('es-PE', {
                    month: '2-digit',
                    day: '2-digit',
                    weekday: 'long'
                })
                .replace(/^./, (letra) => letra.toUpperCase())
                .replace(/,/, '')
                .toUpperCase(),
            name: 'pk',
            options: {
                filter: false,
                display: true,
                minWidth: 300,
                customBodyRender: (value) => (
                    <PaymentPeriod
                        id={value}
                        formData={formData}
                        setFormData={setFormData}
                        date={date}
                        handleOpenModalCashbox={handleOpenModalCashbox}
                    />
                ),
                customHeadRender: () => (
                    <th>
                        <Box sx={{ display: 'flex', width: '100%', gap: 1, px: 2, justifyContent: 'space-between' }}>
                            <Typography>
                                {`${date
                                    .toLocaleDateString('es-PE', {
                                        month: '2-digit',
                                        day: '2-digit',
                                        weekday: 'long'
                                    })
                                    .replace(/^./, (letra) => letra.toUpperCase())
                                    .replace(/,/, '')
                                    .toUpperCase()}`}
                            </Typography>
                            <Box sx={{ display: 'flex', gap: 1 }}>
                                <Tooltip title="Autocompletar Monto" onClick={() => autocompleteAmountColumns(date)} disableFocusListener>
                                    <PaidOutlinedIcon color="success" />
                                </Tooltip>
                                <Tooltip
                                    title="Elegir Cuenta"
                                    onClick={() => handleOpenModalCashbox({ selectedDate: date })}
                                    disableFocusListener
                                >
                                    <AccountBalanceIcon color="info" />
                                </Tooltip>
                            </Box>
                        </Box>
                    </th>
                )
            }
        }));
        return [...initialColumns, ...newColumns];
    };

    useEffect(() => {
        if (dataSetting) {
            setDay(dataSetting.day);
            setNWeeks(dataSetting.default_time);
        } else {
            dispatch(getScheduleSetting());
        }
    }, [dataSetting]);

    useEffect(() => {
        dispatch(getScheduleSetting());
        dispatch(getCashboxBalances());
        dispatch(getCashboxesByUser());
    }, []);

    useEffect(() => handleOpen(), [errors]);

    useEffect(() => {
        const updatedFormData = selectedDocuments.map((item) => {
            const existingItem = formData.find((existing) => existing.id === item.pk);

            if (existingItem) {
                return existingItem;
            }
            return {
                id: item.pk,
                entrygroupId: item.entryGroupId,
                movementId: item.movementId,
                remainingAmount: item.balance,
                documentOrigin: item.documentOrigin,
                currency: item.currency,
                fields: []
            };
        });

        setFormData(updatedFormData);
    }, [selectedDocuments]);

    useEffect(() => {
        if (day !== null && nWeeks !== '') {
            setColumns(getDatesOffPeriods(day, nWeeks, formData, setFormData));
        } else {
            setColumns(initialColumns);
        }
    }, [day, nWeeks, selectedDocuments, formData]);

    return (
        <>
            {openConfirm ? (
                <ConfirmSave
                    open={openConfirm}
                    handleClose={handleCloseConfirm}
                    data={finalData}
                    day={day}
                    nWeeks={nWeeks}
                    handleFormSubmit={handleFormSubmit}
                />
            ) : null}
            {isOpenModalCashbox ? (
                <SelectAccount
                    boxAccounts={cashboxes}
                    documents={selectedDocuments}
                    handleClose={closeModalCashbox}
                    isOpen={isOpenModalCashbox}
                    defaultDate={dateSelected}
                    defaultDocument={documentSelected}
                    cashboxSelected={cashboxSelected}
                    autocompleteCashBoxColumns={autocompleteCashBoxColumns}
                    setCashboxField={setCashboxField}
                />
            ) : null}
            {errors ? <ErrorDisplay open={open} handleClose={handleClose} errors={errors} /> : null}

            <Box sx={{ marginBottom: '2rem' }}>
                <Typography variant="h2">Programar Pagos</Typography>
            </Box>

            {loadingDay ? (
                <LoaderBox />
            ) : (
                <>
                    {!day ? (
                        <Box sx={{ justifyContent: 'center', display: 'flex', gap: 2, alignItems: 'center' }}>
                            <Typography variant="h3">No se ha encontrado la configuración</Typography>
                            <Button variant="contained" fullWidth={false} onClick={() => navigate(`${paymentScheduleRoute}?setting`)}>
                                Crear Configuración
                            </Button>
                        </Box>
                    ) : (
                        <>
                            <Box sx={{ marginY: '1rem', display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, justifyContent: 'space-between', gap: { xs: 0.5, sm: 0 } }}>
                                <Box sx={{ display: 'flex', gap: '1rem' }}>
                                    <TextField
                                        label="Tiempo"
                                        value={nWeeks}
                                        className="select-field"
                                        onChange={({ target: { value } }) => setNWeeks(value === '' ? '' : Number(value))}
                                        select
                                        sx={{ width: { xs: '100%' } }}
                                    >
                                        <MenuItem value={1}>1 Semana</MenuItem>
                                        <MenuItem value={2}>2 Semanas</MenuItem>
                                        <MenuItem value={3}>3 Semanas</MenuItem>
                                        <MenuItem value={4}>1 Mes</MenuItem>
                                        <MenuItem value={8}>2 Meses</MenuItem>
                                        <MenuItem value={12}>3 Meses</MenuItem>
                                    </TextField>
                                </Box>
                                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, flexWrap: { sm: 'wrap' }, justifyContent: 'space-between', gap: { xs: 0.5, sm: 0 } }}>
                                    {step === 0 ? (
                                        <Button
                                            sx={{ m: { xs: 0, sm: 1 }, width: { xs: '100%', sm: 'auto' } }}
                                            variant="contained"
                                            color="info"
                                            startIcon={<CurrencyExchangeIcon />}
                                            onClick={() => setStep(1)}
                                        >
                                            Comparar con Saldos
                                        </Button>
                                    ) : (
                                        <Button
                                            sx={{ m: { xs: 0, sm: 1 }, width: { xs: '100%', sm: 'auto' } }}
                                            variant="contained"
                                            color="primary"
                                            startIcon={<TodayIcon />}
                                            onClick={() => setStep(0)}
                                        >
                                            Ver Programación
                                        </Button>
                                    )}
                                    <Button
                                        sx={{ m: { xs: 0, sm: 1 }, width: { xs: '100%', sm: 'auto' } }}
                                        variant="contained"
                                        color="success"
                                        startIcon={<CloudUploadIcon />}
                                        onClick={submit}
                                    >
                                        Guardar
                                    </Button>
                                </Box>
                            </Box>
                            {formData.length <= 0 ? (
                                <Box sx={{ justifyContent: 'center', display: 'flex', width: '100%' }}>
                                    <Typography variant="h3">Elige documentos para programar pagos</Typography>
                                </Box>
                            ) : (
                                <>
                                    <DisplayRetentionAlert />
                                    <section style={{ display: step === 0 ? 'block' : 'none' }}>
                                        <Grid columns={columns} data={selectedDocuments} options={options} />
                                    </section>
                                    <section style={{ display: step === 1 ? 'block' : 'none' }}>
                                        <CompareBalances
                                            balances={cashboxBalances}
                                            returnFunction={() => setStep(0)}
                                            amountPen={amountPen}
                                            amountUsd={amountUsd}
                                        />
                                    </section>
                                </>
                            )}
                        </>
                    )}
                </>
            )}
        </>
    );
}
