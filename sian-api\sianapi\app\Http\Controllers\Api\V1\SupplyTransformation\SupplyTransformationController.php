<?php

namespace App\Http\Controllers\Api\V1\SupplyTransformation;


use App\Http\Controllers\SianController;
use App\Models\Auth\LoginPivot;
use App\Models\Fake\Currency;
use App\Models\Procedures\SpGetMovementReferences;
use App\Models\Procedures\SpGetProductPresentations;
use App\Models\Procedures\SpGetWarehouseItems;
use App\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class SupplyTransformationController extends Controller {
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */
    public function getAvaliableDocuments(Request $request) {
        try {

            $validate = Validator::make($request->all(), [
                "page" => "required|integer",
                "pageSize" => "required|integer",
                "startDateEmision" => "date",
                "endDateEmision" => "date|after_or_equal:startDateEmision|required_with:startDateEmision",
                "document" => "sometimes|string",
                "documentType" => "sometimes",
                "businessPartner" => "sometimes",
                "warehouse" => "sometimes",
            ]);


            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $page = intval($request->query('page'));
            $pageSize = intval($request->query('pageSize'));
            $startIndex = ($page - 1) * $pageSize;

            $query = $this->getListDocumentsQuery();

            if ($request->has('startDateEmision') && $request->has('endDateEmision')) {
                $query->whereDate('M.emission_date', '>=', $request->input('startDateEmision'));
                $query->whereDate('M.emission_date', '<=', $request->input('endDateEmision'));
            }

            if ($request->has('documentType')) {
                $documentType = $request->input('documentType');
                $arrayDocumentType = explode(",", $documentType);
                $query->whereIn("M.document_code", $arrayDocumentType);
            }

            if ($request->has('businessPartner')) {
                $businessPartner = $request->input('businessPartner');
                $arrayBusinessPartner = explode(",", $businessPartner);
                $query->whereIn("XP.person_id", $arrayBusinessPartner);
            }

            if ($request->has('warehouse')) {
                $warehouse = $request->input('warehouse');
                $arrayWarehouse = explode(",", $warehouse);
                $query->whereIn("W.warehouse_id", $arrayWarehouse);
            }

            if ($request->has('document')) {
                $document = $request->input('document');
                $query->having('document', 'like', '%' . $document . '%');
            }

            $totalItems = $query->count();

            $data = $query->offset($startIndex)->limit($pageSize)->get();

            $response = [
                'success' => true,
                'pagination' => [
                    'page' => $page,
                    'pageSize' => $pageSize,
                    'totalRecords' => $totalItems,
                    'totalPages' => ceil($totalItems / $pageSize)
                ],
                'data' => $data
            ];

            return response()->json($response);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    private function getListDocumentsQuery() {
        return DB::table('movement as M')
            ->select([
                'M.movement_id',
                DB::raw("CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) AS document"),
                'XP.identification_number',
                DB::raw("REPLACE(XP.person_name, ',', ' ') AS company_name"),
                'M.emission_date',
                DB::raw("REPLACE(P.person_name, ',', ' ') AS person_name"),
                "M.route",
                "M.store_id",
                "W.warehouse_id",
                "W.warehouse_name",
                DB::raw("
                    EXISTS(
                        SELECT 1
                        FROM item AS I
                        STRAIGHT_JOIN product_link AS PL ON I.product_id = PL.product_id
                        LEFT JOIN recipe AS RE ON RE.product_id = PL.product_parent_id
                        WHERE I.movement_id = M.movement_id AND I.is_transformed = 0 AND RE.recipe_id IS NULL
                    ) AS have_transformable_items"),
            ])
            ->join('ability as A', 'A.movement_id', '=', 'M.movement_id')
            ->join('warehouse_movement as WM', 'WM.movement_id', '=', 'M.movement_id')
            ->join('warehouse as W', 'W.warehouse_id', '=', 'WM.warehouse_id')
            ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
            ->join('person as P', 'P.person_id', '=', 'M.person_id')
            ->where('M.status', 1)
            ->whereIn('M.route', [
                'warehouse/transferenceIn',
                'warehouse/buyIn',
                // 'warehouse/transformationIn'
            ])
            ->groupBy([
                'M.movement_id',
                'M.document_code',
                'M.document_serie',
                'M.document_correlative',
                'XP.identification_number',
                'XP.person_name',
                'M.emission_date',
                'P.person_name',
                "M.route",
                "M.store_id",
                'W.warehouse_id',
                'W.warehouse_name'
            ])
            ->havingRaw('have_transformable_items = 1');
    }


    public function getDocumentData($movement_id) {
        $warehouse_movement = $this->getDocumentDetailQuery($movement_id)->first();
        if (!$warehouse_movement) {
            return response()->json([
                'success' => false,
                'error' => 'El movimiento no Existe o no cumple con los requisitos'
            ]);
        }

        $references = SpGetMovementReferences::execute($movement_id);

        if (isset($references) && count($references) > 0) {
            $warehouse_movement->references = $references;
        }

        $items = SpGetWarehouseItems::execute($movement_id);
        if (!$items || count($items) < 1) {
            return response()->json([
                'success' => false,
                'error' => 'El movimiento no cuenta con items transformables'
            ]);
        }

        $product_ids = [];

        foreach ($items as &$item) {
            $product_id = $item->product_id;

            if (!in_array($product_id, $product_ids)) {
                $product_ids[] = $product_id;
            }

            $transformationResults = $this->getTransformationResultsByID($product_id);
            $item->transformationResults = $transformationResults;

            if ($transformationResults && count($transformationResults) > 0) {
                foreach ($transformationResults as $product) {
                    if (!in_array($product->product_id, $product_ids)) {
                        $product_ids[] = $product->product_id;
                    }
                }
            }
        }

        $recipePresentations = SpGetProductPresentations::getAssociative(
            SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
            $product_ids,
            Currency::PEN,
            0
        );

        $final_items = [];
        foreach ($items as &$principal_item) {
            $transformationResults = $principal_item->transformationResults;
            if ($transformationResults && count($transformationResults) > 0) {
                foreach ($transformationResults as &$product) {
                    if (isset($recipePresentations[$product->product_id])) {
                        $equivalence_key = number_format($product->equivalence, 2, '.', ',');
                        $product->presentations = $recipePresentations[$product->product_id];
                        $product->presentation = $recipePresentations[$product->product_id][$equivalence_key];
                    }
                }
                if ($principal_item->is_transformed == 0) {
                    $final_items[] = $principal_item;
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'warehouse_movement' => $warehouse_movement,
                'items' => $final_items
            ]
        ], 200);
    }

    private function getDocumentDetailQuery($movement_id) {
        return DB::table('movement as M')
            ->select([
                'M.movement_id',
                'M.document_code',
                'M.business_unit_id',
                'B.business_unit_name',
                DB::raw("CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document"),
                'M.document_serie',
                'M.document_correlative',
                'M.route',
                'M.emission_date',
                'M.status',
                'M.person_id',
                'M.aux_person_id',
                'XP.identification_number',
                DB::raw("REPLACE(XP.person_name, ',', ' ') as aux_person_name"),
                DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                'WM.warehouse_id',
                'W.warehouse_name',
                'ML.movement_parent_id',
                'FC.route as movement_parent_route',
                DB::raw("CONCAT_WS('-', FC.document_code, FC.document_serie, FC.document_correlative) as movement_parent"),
                'M.store_id',
                'ST.store_name',
                DB::raw('M.status AND PA.is_creditable as is_creditable'),
                'S.title'
            ])
            ->join('ability as A', 'A.movement_id', '=', 'M.movement_id')
            ->join('business_unit as B', 'B.business_unit_id', '=', 'M.business_unit_id')
            ->join('scenario as S', 'S.route', '=', 'M.route')
            ->join('store as ST', 'ST.store_id', '=', 'M.store_id')
            ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
            ->join('person as P', 'P.person_id', '=', 'M.person_id')
            ->join('warehouse_movement as WM', 'WM.movement_id', '=', 'M.movement_id')
            ->join('warehouse as W', 'W.warehouse_id', '=', 'WM.warehouse_id')
            ->join('movement_link as ML', 'ML.movement_id', '=', 'M.movement_id')
            ->join('movement as FC', 'FC.movement_id', '=', 'ML.movement_parent_id')
            ->join('ability as PA', 'PA.movement_id', '=', 'FC.movement_id')
            ->whereIn('M.route', [
                'warehouse/buyIn',
                'warehouse/transferenceIn',
                // 'warehouse/transformationIn'
                ])
            ->where('M.movement_id', '=', $movement_id);
    }

    private function getTransformationResultsByID($product_id) {
        return DB::table('product_link as PL')
            ->select([
                'P.product_id',
                'P.product_name',
                'PRES.equivalence',
                'PRES.measure_name',
                'P.item_type_id',
                'P.product_type',
                'P.allow_decimals',
            ])
            ->join('product as P', 'P.product_id', '=', 'PL.product_parent_id')
            ->join('presentation as PRES', 'PRES.product_id', '=', 'P.product_id')
            ->leftJoin('recipe as RE', 'RE.product_id', '=', 'PL.product_parent_id')
            ->where('PRES.default', 1)
            ->whereNull('RE.recipe_id')
            ->where('PL.product_id', $product_id)
            ->get();
    }


    public function store(Request $request) {
        $requestData = $request->json()->all();

        $validator = Validator::make(
            $requestData,
            [
                "otr" => 'required',
                "username" => 'required',
            ]
        );

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'error' => $validator->errors()
            ], 400);
        }


        $host = '';

        $originHeader = $request->headers->get('Origin');

        if ($originHeader) {
            $parsedUrl = parse_url($originHeader);
            $host = $parsedUrl['host'] ?? '';
        }

        $tenant = Tenant::whereDomain($host)->first();

        try {

            $username = $requestData['username'];
            $hash = LoginPivot::getHashFromAuth($request->header('Authorization'));
            $otr = $requestData['otr'];
            $savePaysSianResponse = SianController::saveORTOnSIAN($otr, $username, $hash, 'https://' . $tenant['sian_domain']);

            // Handle both object and array responses
            $statusCode = 500;
            if (is_object($savePaysSianResponse) && isset($savePaysSianResponse->code)) {
                $statusCode = $savePaysSianResponse->code;
            } elseif (is_array($savePaysSianResponse) && isset($savePaysSianResponse['code'])) {
                $statusCode = $savePaysSianResponse['code'];
            }

            return response()->json($savePaysSianResponse, $statusCode);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }




}
