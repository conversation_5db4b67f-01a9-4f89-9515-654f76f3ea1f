/* eslint-disable */
import React, { useState, useEffect } from 'react';
import { Divider, Typography } from '@mui/material';
import { Box } from '@mui/system';
import dayjs from 'dayjs';
import { foodSalesReportEndpoint } from 'services/apiEnpoints';
import MainCard from 'ui-component/cards/MainCard';
import axiosServices from 'utils/axios';
import FormControls from './form-controls';
import SalesTable from './sales-table';
import { GROUP_DIVISION, GROUP_LINE, GROUP_PRODUCT, GROUP_SUBLINE, PRODUCT_TYPE_FOOD } from 'utils/product_types';
import { BlockLoader } from 'ui-component/loaders/loaders';
import useModal from 'hooks/useModal';
import { getStoreWithSalesPromise } from 'services/storeService';

export default function FoodSales() {
    const [startDate, setStartDate] = useState(dayjs().subtract(7, 'day'));
    const [endDate, setEndDate] = useState(dayjs());
    const [limit, setLimit] = useState(10);
    const [salesData, setSalesData] = useState([]);
    const [projectionFactor, setProjectionFactor] = useState(0);
    const [isLoading, setIsLoading] = useState(false);
    const [selectedStores, setSelectedStores] = useState([]);
    const [stores, setStores] = useState([]);

    const [productType, setProductType] = useState(PRODUCT_TYPE_FOOD);
    const [division, setDivision] = useState([]);
    const [line, setLine] = useState([]);
    const [subline, setSubline] = useState([]);
    const [category, setCategory] = useState([]);
    const [productIds, setProductIds] = useState('');
    const [displayValue, setDisplayValue] = useState(false);
    const [onlyTerrace, setOnlyTerrace] = useState(0);

    const [group, setGroup] = useState(GROUP_PRODUCT);

    const [groupName, setGroupName] = useState('product');
    const [groupTitle, setGroupTitle] = useState('PRODUCTO');

    const [orderBy, setOrderBy] = useState(1);
    const [showProyection, openProyection, closeProyection] = useModal(false);

    useEffect(() => {
        getStores().then((result) => {
            if (result.success) {
                setStores(result.data);
                setSelectedStores(result.data.map(({ store_id }) => store_id));
            }
            fetchSalesData();
        });
    }, []);

    useEffect(() => {
        setGroup(GROUP_PRODUCT);
    }, [productType]);

    const handleGroup = (value) => {
        switch (value) {
            case GROUP_DIVISION:
                setGroupName('division');
                setGroupTitle('DIVISIÓN');
                return 'division';
            case GROUP_LINE:
                setGroupName('line');
                setGroupTitle('LINEA');
                return 'line';
            case GROUP_SUBLINE:
                setGroupName('subline');
                setGroupTitle('SUBLINEA');
                return 'subline';
            default:
                setGroupName('product');
                setGroupTitle('PRODUCTO');
                break;
        }
    };

    useEffect(() => handleGroup(group), [salesData]);

    const fetchSalesData = () => {
        setIsLoading(true);

        const filters = {
            orderBy
        };

        if (division.length > 0) {
            filters.division = division;
        }

        if (line.length > 0) {
            filters.line = line;
        }

        if (subline.length > 0) {
            filters.subline = subline;
        }

        if (category.length > 0) {
            filters.category = category;
        }

        if (productIds.trim() !== '') {
            filters.productIds = productIds;
        }

        if (selectedStores.length > 0) {
            filters.storeIds = selectedStores;
        }

        const clasificationFilters = new URLSearchParams(filters);

        axiosServices
            .get(
                `${foodSalesReportEndpoint}?${clasificationFilters}&startDate=${startDate.format('YYYY-MM-DD')}&endDate=${endDate.format(
                    'YYYY-MM-DD'
                )}&limit=${limit}&productType=${productType}&group=${group}&onlyTerrace=${onlyTerrace}`
            )
            .then((response) => {
                if (response.data.success) {
                    setSalesData(response.data.data);
                } else {
                    console.error('Error fetching sales data');
                }
            })
            .catch((error) => console.error('Error:', error))
            .finally(() => setIsLoading(false));
    };

    const getStores = async () => {
        if (stores.length === 0) {
            setIsLoading(true);
            return await getStoreWithSalesPromise();
        }
        return true;
    };

    const handleProjectionChange = (event, newValue) => {
        setProjectionFactor(newValue);
    };

    const resetProjection = () => {
        setProjectionFactor(0);
    };

    return (
        <MainCard>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                <Typography variant="h1">Ventas por Tienda</Typography>
                <Divider />
                <FormControls
                    startDate={startDate}
                    setStartDate={setStartDate}
                    endDate={endDate}
                    setEndDate={setEndDate}
                    limit={limit}
                    setLimit={setLimit}
                    selectedStores={selectedStores}
                    setSelectedStores={setSelectedStores}
                    projectionFactor={projectionFactor}
                    handleProjectionChange={handleProjectionChange}
                    resetProjection={resetProjection}
                    fetchSalesData={fetchSalesData}
                    isLoading={isLoading}
                    stores={stores}
                    productType={productType}
                    setProductType={setProductType}
                    division={division}
                    setDivision={setDivision}
                    line={line}
                    setLine={setLine}
                    subline={subline}
                    setSubline={setSubline}
                    category={category}
                    setCategory={setCategory}
                    showProyection={showProyection}
                    openProyection={openProyection}
                    closeProyection={closeProyection}
                    productIds={productIds}
                    setProductIds={setProductIds}
                    displayValue={displayValue}
                    setDisplayValue={setDisplayValue}
                    group={group}
                    setGroup={setGroup}
                    orderBy={orderBy}
                    setOrderBy={setOrderBy}
                    onlyTerrace={onlyTerrace}
                    setOnlyTerrace={setOnlyTerrace}
                />
            </Box>
            <Divider />
            <BlockLoader loading={isLoading}>
                <SalesTable
                    data={salesData}
                    projectionFactor={projectionFactor}
                    showProyection={showProyection}
                    displayValue={displayValue}
                    groupName={groupName}
                    groupTitle={groupTitle}
                    stores={stores}
                />
            </BlockLoader>
        </MainCard>
    );
}
