import React from 'react';
import { useSelector } from 'react-redux';
import MUIDataTable from 'mui-datatables';
import { findMainProductWithDerivedProduct, findDerivedProductRecursively, processAnalisys } from '../../utils/dataProcessing';

/**
 * Componente para mostrar el análisis de productos derivados
 */
const DerivedProductAnalysis = ({ row, columns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const derivedAnalysis = derivedProduct?.analisys ? processAnalisys(derivedProduct, storeData) : null;

    // Crear columnas completas para análisis si las columnas recibidas no tienen unit_quantity_proyected
    const hasProjectedColumn = columns.some(col => col.name === 'unit_quantity_proyected');

    let analysisColumns = columns;
    if (!hasProjectedColumn && derivedAnalysis && derivedAnalysis.length > 0) {
        // Agregar columnas faltantes basándose en las claves de los datos
        const dataKeys = Object.keys(derivedAnalysis[0]);
        const missingColumns = dataKeys.filter(key => 
            !columns.some(col => col.name === key) && 
            !['pk', 'product_id', 'presentations', 'equivalence_default'].includes(key)
        );

        const additionalColumns = missingColumns.map(key => ({
            name: key,
            label: key.toUpperCase().replace(/_/g, ' '),
            options: {
                filter: true,
                sort: true
            }
        }));

        analysisColumns = [...columns, ...additionalColumns];
    }

    if (!derivedAnalysis || derivedAnalysis.length === 0) {
        return <div>No hay datos de análisis disponibles</div>;
    }

    const options = {
        filterType: 'checkbox',
        responsive: 'standard',
        selectableRows: 'none',
        download: false,
        print: false,
        search: false,
        viewColumns: false,
        pagination: false,
        elevation: 0,
        tableBodyHeight: 'auto',
        tableBodyMaxHeight: 'none',
        customToolbar: null,
        toolbar: false,
        filter: false,
        sort: false,
        rowsPerPage: 10,
        rowsPerPageOptions: [],
        textLabels: {
            body: {
                noMatch: "No hay datos",
                toolTip: "Ordenar",
            },
            pagination: {
                next: "Siguiente",
                previous: "Anterior",
                rowsPerPage: "Filas por página:",
                displayRows: "de",
            },
            toolbar: {
                search: "Buscar",
                downloadCsv: "Descargar CSV",
                print: "Imprimir",
                viewColumns: "Ver Columnas",
                filterTable: "Filtrar Tabla",
            },
            filter: {
                all: "Todos",
                title: "FILTROS",
                reset: "REINICIAR",
            },
            viewColumns: {
                title: "Mostrar Columnas",
                titleAria: "Mostrar/Ocultar Columnas de Tabla",
            },
            selectedRows: {
                text: "fila(s) seleccionada(s)",
                delete: "Eliminar",
                deleteAria: "Eliminar Filas Seleccionadas",
            },
        }
    };

    return (
        <MUIDataTable
            data={derivedAnalysis}
            columns={analysisColumns}
            options={options}
        />
    );
};

export default DerivedProductAnalysis;
