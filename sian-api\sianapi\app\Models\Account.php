<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class Account
 * 
 * @property int $account_id
 * @property string $account_code
 * @property string $account_name
 * @property bool $system
 * @property bool $level
 * @property bool $element_code
 * @property string|null $account_parent
 * @property string|null $counterpart
 * @property string|null $reverse_account
 * @property string|null $owner
 * @property int|null $owner_id
 * @property bool $status
 * @property bool $is_wildcard
 * @property bool $is_item_wildcard
 * @property bool $is_usable
 * @property bool $has_destinies
 * @property string|null $destiny_type
 * @property bool $has_combination
 * @property bool $has_children
 * 
 * @property Account|null $account
 * @property Element $element
 * @property Collection|Account[] $accounts
 * @property Cashbox $cashbox
 * @property Collection|Entry[] $entries
 * @property Collection|EntryGroup[] $entry_groups
 * @property Collection|FixedAsset[] $fixed_assets
 * @property Collection|GlobalVar[] $global_vars
 * @property Collection|MovementSetting[] $movement_settings
 * @property Collection|OperationDynamic[] $operation_dynamics
 * @property Collection|ProductTypeSetting[] $product_type_settings
 *
 * @package App\Models
 */
class Account extends Model
{
	protected $table = 'account';
	protected $primaryKey = 'account_code';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'account_id' => 'int',
		'system' => 'bool',
		'level' => 'bool',
		'element_code' => 'bool',
		'owner_id' => 'int',
		'status' => 'bool',
		'is_wildcard' => 'bool',
		'is_item_wildcard' => 'bool',
		'is_usable' => 'bool',
		'has_destinies' => 'bool',
		'has_combination' => 'bool',
		'has_children' => 'bool'
	];

	protected $fillable = [
		'account_id',
		'account_name',
		'system',
		'level',
		'element_code',
		'account_parent',
		'counterpart',
		'reverse_account',
		'owner',
		'owner_id',
		'status',
		'is_wildcard',
		'is_item_wildcard',
		'is_usable',
		'has_destinies',
		'destiny_type',
		'has_combination',
		'has_children'
	];

	public function account()
	{
		return $this->belongsTo(Account::class, 'reverse_account');
	}

	public function element()
	{
		return $this->belongsTo(Element::class, 'element_code');
	}

	public function owner()
	{
		return $this->belongsTo(Owner::class, 'owner');
	}

	public function accounts()
	{
		return $this->hasMany(Account::class, 'reverse_account');
	}

	public function cashbox()
	{
		return $this->hasOne(Cashbox::class, 'account_code');
	}

	public function entries()
	{
		return $this->hasMany(Entry::class, 'dynamic_account');
	}

	public function entry_groups()
	{
		return $this->hasMany(EntryGroup::class, 'account_group');
	}

	public function fixed_assets()
	{
		return $this->hasMany(FixedAsset::class, 'account_code_depreciation');
	}

	public function global_vars()
	{
		return $this->hasMany(GlobalVar::class, 'surplus_account_code');
	}

	public function movement_settings()
	{
		return $this->hasMany(MovementSetting::class, 'variation_account');
	}

	public function operation_dynamics()
	{
		return $this->hasMany(OperationDynamic::class, 'account_code');
	}

	public function product_type_settings()
	{
		return $this->hasMany(ProductTypeSetting::class, 'warehouse_account');
	}
}
