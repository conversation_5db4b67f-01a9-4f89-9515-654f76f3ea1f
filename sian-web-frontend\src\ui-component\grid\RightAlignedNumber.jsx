import { Typography } from '@mui/material';
import { formatNumberWithCommas } from 'utils/number';

const RightAlignedNumber = ({ value, decimals = 2, ...props }) => {
    const formattedValue = formatNumberWithCommas(value, decimals);

    return (
        <Typography sx={{ textAlign: 'right', px: 1 }} {...props}>
            {formattedValue}
        </Typography>
    );
};

export default RightAlignedNumber;
