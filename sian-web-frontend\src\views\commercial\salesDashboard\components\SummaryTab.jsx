/* eslint-disable */
import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    styled,
    CircularProgress,
    Backdrop,
    Button,
    TextField,
    MenuItem,
    IconButton,
    Menu,
    Divider,
    Switch,
    FormControlLabel,
    Stack
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import { es } from 'date-fns/locale';
import { startOfMonth, endOfMonth, startOfWeek, endOfWeek, subDays, subMonths, isSameDay, format, getDaysInMonth, addDays } from 'date-fns';
import { getSalesDashboardGoalsPromise } from 'services/salesDashboard';
import PredictionModal from './PredictionModal';
import {
    CalendarToday as CalendarTodayIcon,
    Schedule as ScheduleIcon,
    Clear as ClearIcon,
    CompareArrows as CompareIcon,
    ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import {
    updateGlobalPercentages,
    trafficLightColors,
    LegendGroup,
    getStoreColor,
    getStatusLabel,
    MetricPercentage,
    StoreGrid,
    StoreName,
    MetricCard,
    MetricHeader,
    MetricTitle,
    MetricDate,
    MetricContent,
    MetricInfo,
    ChartContainer,
    MetricValues,
    MetricValue,
    ChartLegend,
    LegendItem,
    LegendDot,
    TotalCard,
    TotalHeader,
    TotalTitle,
    TotalTitleIcon,
    TotalStats,
    StatItem,
    StatLabel,
    StatValue,
    ProgressBarContainer,
    ProgressLabel,
    StyledLinearProgress,
    PredictButton,
    ButtonContainer,
    UpdatedAt,
    TotalSection,
    FooterLegend
} from './summary/components';
import { RadialBarChart, RadialBar, ResponsiveContainer, PolarAngleAxis, BarChart, Bar, XAxis, YAxis, Tooltip } from 'recharts';
import DateRangeInput from './DateRangeInput';
import { formatFriendlyDate, getFirstDayOfCurrentMonth, getFirstDayOfGivenMonth, getTodayDate, getTodayDateTime, isFullMonth } from 'utils/dates';

let globalLegendPercentages = {
    green: 80,
    yellow: 51,
    orange: 50,
    red: 25
};

const SummaryTab = () => {
    const [data, setData] = useState([]);
    const [compareData, setCompareData] = useState([]); // Datos del periodo para comparar
    const [loading, setLoading] = useState(true);
    const [compareLoading, setCompareLoading] = useState(false);
    const [apiMode, setApiMode] = useState(0); // 0 para la api oficial, 1 para api url completa
    const [selectedTab, setSelectedTab] = useState('day'); // 'day', 'month', 'date'
    const [dateRange, setDateRange] = useState([getTodayDate(), getTodayDate()]); // [startDate, endDate]
    const [dateRangePopperOpen, setDateRangePopperOpen] = useState(false);
    const [predictionModalOpen, setPredictionModalOpen] = useState(false);
    const [selectedStore, setSelectedStore] = useState(null);
    const [selectedStoreData, setSelectedStoreData] = useState(null);
    const [showComparison, setShowComparison] = useState(false); // Controla si se muestra la comparación
    const [comparePeriod, setComparePeriod] = useState(1); // Número de periodos hacia atrás para comparar
    const [customCompareDate, setCustomCompareDate] = useState(null);
    const [anchorEl, setAnchorEl] = useState(null);
    const [compareMenuAnchor, setCompareMenuAnchor] = useState(null);
    const [originalTab, setOriginalTab] = useState('day'); // Guardar la pestaña original antes de la comparación
    const [compareTicketCount, setCompareTicketCount] = useState(0);

    // Estado para los menús personalizados
    const [showDateRangeMenu, setShowDateRangeMenu] = useState(false);
    const [showCalendarMenu, setShowCalendarMenu] = useState(false);

    // Función para comprobar si dos fechas son el mismo día
    const isSameDay = (date1, date2) => {
        if (!date1 || !date2) return false;
        return date1.getDate() === date2.getDate() && date1.getMonth() === date2.getMonth() && date1.getFullYear() === date2.getFullYear();
    };

    // Función para determinar si el periodo actual permite comparación
    const isComparisonAllowed = () => {
        const today = getTodayDate();

        // "Hoy" no permite comparación
        if (selectedTab === 'day') return false;

        // "Avance del mes" no permite comparación
        if (selectedTab === 'period') return false;

        // "Mes" solo permite comparación si NO es el mes actual
        if (selectedTab === 'month' || selectedTab === 'thisMonth') {
            return true;
            const isCurrentMonth = today.getMonth() === dateRange[0].getMonth() && today.getFullYear() === dateRange[0].getFullYear();
            return !isCurrentMonth;
        }

        // Fecha personalizada solo permite comparación si NO es hoy
        if (selectedTab === 'date') {
            return !isSameDay(dateRange[0], today);
        }

        // El resto de periodos (ayer, mes pasado, etc.) sí permiten comparación
        return true;
    };

    // Función para formatear fechas correctamente para Perú (UTC-5)
    const formatPeruDate = (date) => {
        if (!date) return '';
        // Creamos una nueva fecha para no modificar la original
        const d = new Date(date);

        // Obtenemos año, mes y día en formato peruano
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');

        // Retornamos en formato YYYY-MM-DD
        return `${year}-${month}-${day}`;
    };

    // Función para obtener la fecha actual considerando horario de Perú
    const getPeruDate = () => {
        // Crear una nueva fecha que represente el momento actual
        const now = getTodayDateTime();

        // Ajustar a la zona horaria de Perú (UTC-5)
        // Esto asegura que la fecha sea correcta para Perú
        const peruDate = new Date(now.toLocaleString('en-US', { timeZone: 'America/Lima' }));

        return peruDate;
    };

    // Función para calcular el porcentaje de avance de ventas
    const calculatePercentage = (salesOrStore, goal = null) => {
        // Si se pasa un objeto store completo
        if (typeof salesOrStore === 'object' && salesOrStore !== null) {
            const storeData = salesOrStore;
            if (!storeData) return 0;

            let salesValue, goalValue;

            if (selectedTab === 'day' || selectedTab === 'yesterday') {
                salesValue = parseFloat(storeData.today_sales || 0);
                goalValue = parseFloat(storeData.today_goal || 0);
            } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {
                salesValue = parseFloat(storeData.progress_sales || 0);
                goalValue = parseFloat(storeData.period_goal || 0);
            } else {
                // Para semana actual o semana pasada
                salesValue = parseFloat(storeData.progress_sales || 0);
                goalValue = parseFloat(storeData.today_goal || 0) * totalDays;
            }

            if (goalValue === 0) return 0;
            return (salesValue / goalValue) * 100;
        }
        // Si se pasan valores numéricos directamente
        else {
            const sales = parseFloat(salesOrStore || 0);
            const targetGoal = parseFloat(goal || 0);
            if (!targetGoal || targetGoal === 0) return 0;
            return (sales / targetGoal) * 100;
        }
    };

    // Nueva función para cerrar el menú de opciones de rango de fechas
    const handleDateRangeMenuClose = () => {
        setDateRangePopperOpen(false);
    };

    // Maneja la apertura del menú de comparación
    const handleCompareMenuOpen = (event) => {
        setCompareMenuAnchor(event.currentTarget);
    };

    // Maneja el cierre del menú de comparación
    const handleCompareMenuClose = () => {
        setCompareMenuAnchor(null);
    };

    // Maneja la selección de una opción de comparación rápida
    const handleQuickCompareSelect = (periodType, value) => {
        setComparePeriod(value);
        // Calcular la fecha correspondiente basada en la selección
        const today = getPeruDate();
        let newDate = new Date(today);

        if (periodType === 'days') {
            newDate.setDate(today.getDate() - value);
        } else if (periodType === 'months') {
            newDate.setMonth(today.getMonth() - value);
        } else if (periodType === 'years') {
            newDate.setFullYear(today.getFullYear() - value);
        }

        // Ajustar la fecha según el tipo de vista seleccionada
        if (selectedTab === 'month') {
            // Para vista mensual, asegurarse de que sea el primer día del mes
            newDate = new Date(newDate.getFullYear(), newDate.getMonth(), 1);
        }

        setCustomCompareDate(newDate);
        handleCompareMenuClose();

        if (showComparison) {
            fetchComparisonData(value, newDate);
        }
    };

    // Maneja la selección de fecha personalizada
    const handleDatePickerChange = (newDate) => {
        if (newDate) {
            setCustomCompareDate(newDate);
            // Calculamos el offset aproximado en días para mantener la compatibilidad
            const today = getPeruDate();
            const diffTime = Math.abs(today - newDate);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            setComparePeriod(diffDays);

            if (showComparison) {
                fetchComparisonData(diffDays, newDate);
            }
        }
        setAnchorEl(null); // cerrar el selector de fecha
    };

    // Actualizar fetchComparisonData para manejar rangos de fechas
    const fetchComparisonData = async (days, specificDate, endDate = null) => {
        setCompareLoading(true);
        try {
            const params = {};
            const today = getPeruDate();

            if (specificDate) {
                // Si se proporciona una fecha específica, la usamos directamente
                if (selectedTab === 'day') {
                    params.date = formatPeruDate(specificDate);
                } else if (selectedTab === 'month') {
                    //params.month = (specificDate.getMonth() + 1).toString();
                    //params.year = specificDate.getFullYear().toString();
                    // Usar rango de fechas en lugar de month/year para mayor precisión en la comparación
                    if (specificDate && endDate) {
                        // Si se proporcionan fechas específicas de inicio y fin para la comparación
                        params.date_from = formatPeruDate(specificDate);
                        params.date_to = formatPeruDate(endDate);
                    } else if (specificDate) {
                        // Si solo se proporciona una fecha, calcular el rango del mes anterior
                        const startOfMonth = new Date(specificDate.getFullYear(), specificDate.getMonth(), 1);
                        const daysInMonth = new Date(specificDate.getFullYear(), specificDate.getMonth() + 1, 0).getDate();
                        const endOfMonth = new Date(
                            specificDate.getFullYear(),
                            specificDate.getMonth(),
                            Math.min(daysInMonth, dateRange[1].getDate())
                        );

                        params.date_from = formatPeruDate(startOfMonth);
                        params.date_to = formatPeruDate(endOfMonth);
                    } else {
                        // Si no se proporciona fecha, usar los mismos rangos que generados en generateSmartComparisonPeriod
                        const [compStart, compEnd] = generateSmartComparisonPeriod();
                        params.date_from = formatPeruDate(compStart);
                        params.date_to = formatPeruDate(compEnd);
                    }
                } else {
                    // Para rango de fechas, usar fecha_desde y fecha_hasta
                    const compareEndDate = endDate || specificDate;
                    params.date_from = formatPeruDate(specificDate);
                    params.date_to = formatPeruDate(compareEndDate);
                }
            } else {
                // Cálculo basado en días hacia atrás
                const compareDate = subDays(today, days);
                if (selectedTab === 'day') {
                    params.date = formatPeruDate(compareDate);
                } else if (selectedTab === 'month') {
                    params.month = (compareDate.getMonth() + 1).toString();
                    params.year = compareDate.getFullYear().toString();
                } else {
                    // Para rango de fechas, calcular un rango equivalente en el pasado
                    const diffTime = Math.abs(dateRange[1] - dateRange[0]);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                    const compareStartDate = subDays(dateRange[0], days);
                    const compareEndDate = subDays(dateRange[1], days);

                    params.date_from = formatPeruDate(compareStartDate);
                    params.date_to = formatPeruDate(compareEndDate);
                }
            }

            const response = await getSalesDashboardGoalsPromise(params);
            setCompareData(response.data);

            // Calcular la cantidad de tickets para la comparación
            const compareTickets = response.data.reduce((acc, store) => acc + parseInt(store.today_tickets || 0), 0);
            setCompareTicketCount(compareTickets);

            // Calcular totales para el periodo de comparación
            const totalResults = calculateTotals(response.data);
            // setCompareTotals(totalResults);

            // Calcular porcentaje total de cumplimiento para comparación
            let comparePercentageTotal;
            if (selectedTab === 'day' || selectedTab === 'yesterday') {
                comparePercentageTotal = calculatePercentage(totalResults.today_sales, totalResults.today_goal);
            } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {
                comparePercentageTotal = calculatePercentage(totalResults.sale_progress, totalResults.period_goal);
            } else {
                comparePercentageTotal = calculatePercentage(totalResults.sale_progress, totalResults.today_goal * totalDays);
            }
            // setCompareTotalPercentage(comparePercentageTotal);
        } catch (error) {
            console.error('Error fetching comparison data:', error);
        } finally {
            setCompareLoading(false);
        }
    };

    // Función para alternar la vista de comparación
    const toggleComparison = () => {
        const newValue = !showComparison;
        setShowComparison(newValue);

        if (newValue) {
            // Guardar la pestaña original
            setOriginalTab(selectedTab);

            // Generar periodo de comparación inteligente
            const [comparisonStartDate, comparisonEndDate, diffDays] = generateSmartComparisonPeriod();

            // Usar la fecha de inicio como fecha de comparación principal
            setCustomCompareDate(comparisonStartDate);
            setComparePeriod(diffDays);

            // Obtener datos de comparación basados en el periodo generado
            fetchComparisonData(diffDays, comparisonStartDate, comparisonEndDate);
        } else {
            // Limpiar comparación
            setCustomCompareDate(null);
            setCompareData([]);
        }
    };

    const desactivateComparison = () => {
        setShowComparison(false);
        setCustomCompareDate(null);
        setComparePeriod(0);
        setCompareData([]);
    }

    useEffect(() => {
        const fetchData = async () => {
            setLoading(true); // Activar loading al inicio
            try {
                let response;
                if (apiMode === 0) {
                    // Crear un objeto con los parámetros según la pestaña seleccionada
                    const params = {};

                    // Obtener la fecha actual para usar en las consultas (usando zona horaria de Perú)
                    const today = getPeruDate();
                    const formattedDate = formatPeruDate(today); // YYYY-MM-DD con zona horaria de Perú

                    if (selectedTab === 'day') {
                        // Para compatibilidad con modo 'día', usar solo una fecha
                        // Si es un rango del mismo día, usar ese día, sino usar la fecha actual
                        if (dateRange[0] && dateRange[1] && isSameDay(dateRange[0], dateRange[1])) {
                            params.date = formatPeruDate(dateRange[0]);
                        } else {
                            params.date = formattedDate;
                        }
                    } else if (selectedTab === 'yesterday') {
                        // Para el caso específico de "Ayer"
                        const yesterday = subDays(today, 1);
                        params.date = formatPeruDate(yesterday);
                    } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {
                        // Para el caso de "Esta semana" (últimos 7 días)
                        const weekStart = new Date(today);
                        weekStart.setDate(today.getDate() - 6); // 6 días atrás + hoy = 7 días
                        params.date_from = formatPeruDate(weekStart);
                        params.date_to = formatPeruDate(today);
                    } else if (selectedTab === 'lastWeek') {
                        // Para semana pasada (7 días anteriores a la semana actual)
                        const lastWeekStart = new Date(today);
                        lastWeekStart.setDate(today.getDate() - 13); // 7 días antes del inicio de los últimos 7 días
                        const lastWeekEnd = new Date(today);
                        lastWeekEnd.setDate(today.getDate() - 7); // 7 días antes de hoy
                        params.date_from = formatPeruDate(lastWeekStart);
                        params.date_to = formatPeruDate(lastWeekEnd);
                    } else if (selectedTab === 'period' || selectedTab === 'thisPeriod') {
                        // Para este periodo, desde inicio del mes hasta hoy
                        const startOfCurrentMonth = startOfMonth(today);
                        params.date_from = formatPeruDate(startOfCurrentMonth);
                        params.date_to = formatPeruDate(today);
                    } else if (selectedTab === 'month') {

                        // Usar rango de fechas en lugar de mes/año para mayor precisión
                        if (dateRange[0] && dateRange[1]) {
                            params.date_from = formatPeruDate(dateRange[0]);
                            params.date_to = formatPeruDate(dateRange[1]);
                        } else {
                            // Fallback al mes actual si no hay rango definido
                            params.month = (today.getMonth() + 1).toString();
                            params.year = today.getFullYear().toString();
                        }
                    } else if (selectedTab === 'date') {
                        // Configurar parámetros para consulta por rango de fechas
                        if (dateRange[0] && dateRange[1]) {
                            // Validación adicional para asegurarse de que las fechas estén en el mismo mes/año
                            // si estamos en modo 'date' pero el API requiere fechas del mismo mes
                            if (
                                dateRange[0].getMonth() === dateRange[1].getMonth() &&
                                dateRange[0].getFullYear() === dateRange[1].getFullYear()
                            ) {
                                params.date_from = formatPeruDate(dateRange[0]);
                                params.date_to = formatPeruDate(dateRange[1]);
                            } else {
                                // Si se requieren fechas del mismo mes pero no lo son, ajustar el rango
                                // al último día del mes de la fecha inicial
                                const lastDayOfStartMonth = endOfMonth(dateRange[0]);
                                params.date_from = formatPeruDate(dateRange[0]);
                                params.date_to = formatPeruDate(lastDayOfStartMonth);
                                console.warn(
                                    'Las fechas deben ser del mismo mes para la API. Ajustando fecha final al último día del mes inicial.'
                                );
                            }
                        } else {
                            // Comportamiento por defecto (desde inicio de mes hasta hoy)
                            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                            params.date_from = formatPeruDate(firstDayOfMonth);
                            params.date_to = formattedDate;
                        }
                    }

                    // params.business_unit_id = ...;
                    // params.store_id = ...;
                    response = await getSalesDashboardGoalsPromise(params);
                    setData(response.data);
                    if (response.legend_percentages) {
                        updateGlobalPercentages(response.legend_percentages);
                    }
                }
            } catch (error) {
                console.error('Error fetching data:', error);
                // Si falla la primera API, intentar con la segunda
                if (apiMode === 0) {
                    setApiMode(1);
                }
            } finally {
                // Agregar delay para evitar parpadeos
                setTimeout(() => {
                    setLoading(false);
                }, 500);
            }
        };

        fetchData();
    }, [apiMode, selectedTab, dateRange]); // Agregar dateRange como dependencia

    const handlePredictClick = (storeName) => {
        // Encontrar los datos de la tienda seleccionada
        let storeData;
        if (storeName === 'Total MIA') {
            // Para el caso de Total MIA, creamos un objeto con los totales
            if (selectedTab === 'day') {
                storeData = {
                    business_unit_name: 'Total MIA',
                    today_sales: totals.today_sales,
                    today_goal: totals.today_goal,
                    progress_sales: totals.sale_progress, // Mantener para compatibilidad
                    period_goal: totals.period_goal, // Mantener para compatibilidad
                    query_date: data[0]?.query_date,
                    selected_tab: 'day'
                };
            } else if (selectedTab === 'month') {
                storeData = {
                    business_unit_name: 'Total MIA',
                    today_sales: totals.today_sales, // Mantener para compatibilidad
                    today_goal: totals.today_goal, // Mantener para compatibilidad
                    progress_sales: totals.sale_progress,
                    period_goal: totals.period_goal,
                    query_date: data[0]?.query_date,
                    selected_tab: 'month'
                };
            } else {
                // Para el tab 'date', calculamos la meta ajustada por días transcurridos
                storeData = {
                    business_unit_name: 'Total MIA',
                    today_sales: totals.today_sales, // Mantener para compatibilidad
                    today_goal: totals.today_goal, // Meta diaria original
                    progress_sales: totals.sale_progress, // Ventas acumuladas
                    period_goal: totals.period_goal, // Mantener para compatibilidad
                    date_goal: dateTotals.goal, // Meta ajustada por días
                    elapsed_days: elapsedDays, // Días transcurridos
                    query_date: data[0]?.query_date,
                    selected_tab: 'date'
                };
            }
        } else {
            // Para tiendas individuales, buscamos en el array de datos
            const store = data.find((store) => store.business_unit_name === storeName);

            if (store) {
                if (selectedTab === 'day') {
                    storeData = {
                        ...store,
                        selected_tab: 'day'
                    };
                } else if (selectedTab === 'month') {
                    storeData = {
                        ...store,
                        selected_tab: 'month'
                    };
                } else {
                    // Para el tab 'date', calculamos la meta ajustada por días transcurridos
                    const dateGoal = parseFloat(store.today_goal) * elapsedDays;
                    storeData = {
                        ...store,
                        date_goal: dateGoal, // Meta ajustada por días
                        elapsed_days: elapsedDays, // Días transcurridos
                        selected_tab: 'date'
                    };
                }
            }
        }

        if (storeData) {
            setSelectedStore(storeName);
            setSelectedStoreData(storeData);
            setPredictionModalOpen(true);
        }
    };

    if (loading) {
        return (
            <Backdrop
                sx={{
                    color: '#fff',
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)'
                }}
                open={loading}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: 2
                    }}
                >
                    <CircularProgress
                        size={50}
                        thickness={4}
                        sx={{
                            color: '#b3256e'
                        }}
                    />
                    <Typography
                        variant="h6"
                        sx={{
                            color: '#b3256e',
                            fontWeight: 'bold'
                        }}
                    >
                        Cargando datos...
                    </Typography>
                </Box>
            </Backdrop>
        );
    }

    // Formato para montos con símbolo de moneda
    const formatNumber = (value) => {
        if (value === undefined || value === null) return 'S/ 0.00';
        return `S/ ${parseFloat(value).toLocaleString('es-PE', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        })}`;
    };

    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    // Cálculo de días transcurridos y días totales para el rango seleccionado
    const calculateDateRangeInfo = () => {
        // Si no hay rango de fechas, devolver valores por defecto
        if (!dateRange[0] || !dateRange[1]) return { elapsedDays: 1, totalDays: 1 };

        // Función para normalizar una fecha (ponerla a medianoche)
        const normalizeDate = (date) => {
            const d = new Date(date);
            d.setHours(0, 0, 0, 0);
            return d;
        };

        // Calcular la diferencia en días entre dos fechas normalizadas
        const getDayDifference = (start, end) => {
            const diffTime = Math.abs(end - start);
            return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir ambos días
        };

        const today = getPeruDate();
        const normalizedToday = normalizeDate(today);

        // Determinar el rango según la pestaña seleccionada
        let startDate, endDate, currentDate;
        if (selectedTab === 'day' || selectedTab === 'yesterday') {
            // Para día, el rango es solo un día
            return { elapsedDays: 1, totalDays: 1 };
        } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {
            // Para semana, mostrar los últimos 7 días incluido hoy
            startDate = normalizeDate(new Date(today));
            startDate.setDate(startDate.getDate() - 6); // 6 días atrás + hoy = 7 días
            endDate = normalizeDate(today);
            currentDate = normalizedToday;
        } else if (selectedTab === 'lastWeek') {
            // Para semana pasada, los 7 días anteriores a la semana actual
            startDate = normalizeDate(new Date(today));
            startDate.setDate(startDate.getDate() - 13);
            endDate = normalizeDate(new Date(today));
            endDate.setDate(endDate.getDate() - 7);
            currentDate = endDate; // Ya pasaron todos los días
        } else if (selectedTab === 'period' || selectedTab === 'thisPeriod') {
            // Para este periodo, desde inicio del mes hasta hoy
            startDate = normalizeDate(startOfMonth(today));
            endDate = normalizeDate(today);
            currentDate = normalizedToday;
        } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {
            // Para este mes, desde inicio del mes hasta hoy
            startDate = normalizeDate(startOfMonth(today));
            endDate = normalizeDate(today);
            currentDate = normalizedToday;
        } else if (selectedTab === 'lastMonth') {
            // Para mes pasado, todo el mes anterior
            const lastMonth = subMonths(today, 1);
            startDate = normalizeDate(startOfMonth(lastMonth));
            endDate = normalizeDate(endOfMonth(lastMonth));
            currentDate = endDate; // Ya pasaron todos los días
        } else {
            // Para otros casos o rango personalizado
            startDate = normalizeDate(dateRange[0]);
            endDate = normalizeDate(dateRange[1]);
            currentDate = normalizedToday;
        }

        // Cálculo de días
        const totalDays = getDayDifference(startDate, endDate);
        let elapsedDays;
        if (currentDate > endDate) {
            // Si la fecha actual es posterior al final del rango, todos los días han transcurrido
            elapsedDays = totalDays;
        } else if (currentDate < startDate) {
            // Si la fecha actual es anterior al inicio del rango, aún no ha transcurrido ningún día
            elapsedDays = 0;
        } else {
            // Si la fecha actual está dentro del rango, calcular los días transcurridos
            elapsedDays = getDayDifference(startDate, currentDate);
        }

        return { elapsedDays, totalDays };
    };

    const { elapsedDays, totalDays } = calculateDateRangeInfo();

    // Calcular totales para tarjetas y gráficos
    const calculateTotals = () => {
        if (!data || data.length === 0)
            return {
                today_sales: 0,
                today_goal: 0,
                period_goal: 0,
                sale_progress: 0,
                today_average_ticket: 0,
                today_tickets: 0
            };

        return data.reduce(
            (acc, store) => {
                return {
                    today_sales: acc.today_sales + parseFloat(store.today_sales || 0),
                    today_goal: acc.today_goal + parseFloat(store.today_goal || 0),
                    period_goal: acc.period_goal + parseFloat(store.period_goal || 0),
                    sale_progress: acc.sale_progress + parseFloat(store.progress_sales || 0),
                    today_average_ticket: parseFloat(store.today_average_ticket || 0),
                    today_tickets: acc.today_tickets + parseInt(store.today_tickets || 0)
                };
            },
            {
                today_sales: 0,
                today_goal: 0,
                period_goal: 0,
                sale_progress: 0,
                today_average_ticket: 0,
                today_tickets: 0
            }
        );
    };

    // Calcular totales
    const totals = calculateTotals();

    // Calcular los totales para el tab de fecha
    const dateTotals = {
        goal: totals.today_goal * totalDays,
        remaining: totals.today_goal * totalDays - totals.sale_progress
    };

    // Calcular porcentaje de cumplimiento total
    const totalPercentage =
        selectedTab === 'day' || selectedTab === 'yesterday'
            ? calculatePercentage(totals.today_sales, totals.today_goal) // Para hoy/ayer
            : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
            ? calculatePercentage(totals.sale_progress, totals.period_goal) // Para cualquier mes
            : calculatePercentage(totals.sale_progress, totals.today_goal * totalDays); // Para semana/rango, meta diaria * total de días

    const DateSelectorButton = styled(Button)(({ theme }) => ({
        borderColor: '#b3256e',
        color: '#b3256e',
        minWidth: '220px',
        display: 'flex',
        justifyContent: 'space-between',
        '&:hover': {
            borderColor: '#9a1e5c',
            backgroundColor: 'rgba(179, 37, 110, 0.04)'
        },
        boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        borderRadius: '8px',
        padding: '8px 16px',
        transition: 'all 0.2s ease'
    }));

    const DateSelectorContainer = styled(Box)(({ theme }) => ({
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        padding: '10px 16px',
        backgroundColor: '#f8f9fa',
        borderRadius: '10px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
        marginBottom: '16px',
        position: 'relative',
        justifyContent: 'center', // Cambiado de 'flex-end' a 'center'
        flexWrap: 'wrap',
        overflow: 'visible',
        zIndex: 1
    }));

    const CompareButton = styled(Button)(({ theme, active }) => ({
        backgroundColor: active ? '#b3256e' : 'transparent',
        color: active ? 'white' : '#b3256e',
        borderColor: '#b3256e',
        '&:hover': {
            backgroundColor: active ? '#9a1e5c' : 'rgba(179, 37, 110, 0.04)',
            borderColor: '#9a1e5c'
        },
        fontWeight: active ? 'bold' : 'normal',
        borderRadius: '8px',
        padding: '6px 16px',
        transition: 'all 0.2s ease',
        boxShadow: active ? '0 2px 5px rgba(179, 37, 110, 0.2)' : 'none'
    }));

    const DateRangeActionButton = styled(Button)(({ theme }) => ({
        padding: '8px 16px',
        fontSize: '1rem',
        textTransform: 'none',
        '&:hover': {
            backgroundColor: 'rgba(179, 37, 110, 0.08)'
        },
        '&.MuiButton-containedPrimary': {
            backgroundColor: '#b3256e',
            '&:hover': {
                backgroundColor: '#9a1e5c'
            }
        }
    }));

    const DateRangeTextField = styled(TextField)(({ theme }) => ({
        '& .MuiOutlinedInput-root': {
            padding: '8px 12px',
            fontSize: '1rem',
            '& fieldset': {
                borderColor: '#b3256e',
                borderWidth: '1px'
            },
            '&:hover fieldset': {
                borderColor: '#9a1e5c'
            },
            '&.Mui-focused fieldset': {
                borderColor: '#b3256e'
            }
        }
    }));

    // Estilos personalizados para los menús
    const CustomMenuContainer = styled(Box)(({ theme }) => ({
        position: 'absolute',
        top: '100%',
        left: 0,
        backgroundColor: '#ffffff',
        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
        borderRadius: '8px',
        padding: '8px 0',
        marginTop: '8px',
        zIndex: 9999,
        minWidth: '200px',
        overflow: 'visible'
    }));

    const CustomCalendarContainer = styled(Box)(({ theme }) => ({
        position: 'absolute',
        top: '100%',
        left: '50%',
        transform: 'translateX(-50%)',
        backgroundColor: '#ffffff',
        boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
        borderRadius: '8px',
        padding: '16px',
        marginTop: '8px',
        zIndex: 9999,
        width: '340px',
        overflow: 'visible'
    }));

    const CustomMenuItem = styled(Box)(({ theme }) => ({
        padding: '10px 16px',
        cursor: 'pointer',
        '&:hover': {
            backgroundColor: 'rgba(179, 37, 110, 0.08)'
        },
        transition: 'background-color 0.2s'
    }));

    // Componente para mostrar información de comparación
    const ComparisonInfo = styled(Box)(({ theme }) => ({
        display: 'flex',
        alignItems: 'center',
        padding: '6px 12px',
        borderRadius: '6px',
        backgroundColor: 'rgba(179, 37, 110, 0.12)',
        fontSize: '0.85rem',
        color: '#b3256e',
        marginLeft: '12px',
        fontWeight: 500,
        border: '1px solid rgba(179, 37, 110, 0.2)',
        boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
        '& .MuiSvgIcon-root': {
            fontSize: '1.1rem',
            marginRight: '6px'
        }
    }));

    // Función para formatear texto de periodo de comparación
    const getComparisonText = () => {
        if (!showComparison || !customCompareDate) return '';

        // Calcular fecha de fin del periodo comparativo
        const getCompareEndDate = () => {
            if (selectedTab === 'day') {
                return customCompareDate;
            } else if (selectedTab === 'week' || selectedTab === 'thisWeek') {
                // Para semana actual, calculamos el final como la diferencia entre inicio y fin + customCompareDate
                const diffTime = dateRange[1].getTime() - dateRange[0].getTime();
                const compareEndDate = new Date(customCompareDate.getTime() + diffTime);
                return compareEndDate;
            } else if (selectedTab === 'lastWeek') {
                // Para semana pasada, usar el final del periodo de comparación (7 días antes del inicio de los últimos 7 días)
                const compareEndDate = new Date(today);
                compareEndDate.setDate(today.getDate() - 7); // 7 días antes de hoy (final del periodo anterior)
                return compareEndDate;
            } else if (selectedTab === 'month' || selectedTab === 'thisMonth') {
                const currentMonth = getPeruDate().getMonth();
                const compareMonth = customCompareDate.getMonth();
                // Si estamos comparando con un mes distinto, usar último día hasta la fecha actual
                if (currentMonth !== compareMonth) {
                    // Si estamos a día 15, el mes anterior también termina el 15
                    const currentDay = getPeruDate().getDate();
                    return new Date(
                        customCompareDate.getFullYear(),
                        customCompareDate.getMonth(),
                        Math.min(currentDay, getDaysInMonth(customCompareDate))
                    );
                } else {
                    // Si es el mismo mes pero año anterior, usar el mismo día que hoy pero en esa fecha
                    return customCompareDate;
                }
            } else if (selectedTab === 'lastMonth') {
                // Obtener el día actual para mantener consistencia en la comparación
                const currentDay = getPeruDate().getDate();
                // Usar el mismo día en el mes anterior o el último día del mes anterior si el mes es más corto
                return new Date(
                    customCompareDate.getFullYear(),
                    customCompareDate.getMonth(),
                    Math.min(currentDay, getDaysInMonth(customCompareDate))
                );
            } else if (selectedTab === 'period') {
                // Para periodo actual (del 1 al día actual)
                const diffTime = dateRange[1].getTime() - dateRange[0].getTime();
                const compareEndDate = new Date(customCompareDate.getTime() + diffTime);
                return compareEndDate;
            } else {
                // Para fechas personalizadas, calcular mediante diferencia
                const diffTime = dateRange[1].getTime() - dateRange[0].getTime();
                const compareEndDate = new Date(customCompareDate.getTime() + diffTime);
                return compareEndDate;
            }

            return compareEndDate;
        };

        const compareEndDate = getCompareEndDate();

        // Usar formato claro según el tipo de periodo
        if (selectedTab === 'day') {
            return `${formatDate(customCompareDate)}`;
        } else if (selectedTab === 'yesterday') {
            return `${formatDate(customCompareDate)}`;
        } else if (selectedTab === 'week' || selectedTab === 'thisWeek' || selectedTab === 'lastWeek') {
            // Comprobar si las fechas son el mismo día
            if (isSameDay(customCompareDate, compareEndDate)) {
                return `${formatDate(customCompareDate)}`;
            }
            return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;
        } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {
            const compareMonthName = customCompareDate.toLocaleString('default', {
                month: 'long',
                year: 'numeric'
            });
            return `${compareMonthName}`;
        } else if (selectedTab === 'period') {
            // Comprobar si las fechas son el mismo día
            if (isSameDay(customCompareDate, compareEndDate)) {
                return `${formatDate(customCompareDate)}`;
            }
            return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;
        } else {
            // Para fechas personalizadas, comprobar si es el mismo día
            if (isSameDay(customCompareDate, compareEndDate)) {
                return `${formatDate(customCompareDate)}`;
            }
            return `${formatDate(customCompareDate)} al ${formatDate(compareEndDate)}`;
        }
    };

    // Función simple para mostrar/ocultar menú de rangos
    const toggleDateRangeMenu = () => {
        setShowDateRangeMenu(!showDateRangeMenu);
        setShowCalendarMenu(false); // Cerrar el otro menú
    };

    // Función simple para mostrar/ocultar menú de calendario
    const toggleCalendarMenu = () => {
        setShowCalendarMenu(!showCalendarMenu);
        setShowDateRangeMenu(false); // Cerrar el otro menú
    };

    // Función para cerrar ambos menús
    const closeAllMenus = () => {
        setShowDateRangeMenu(false);
        setShowCalendarMenu(false);
    };

    const applyPresetRange = (preset) => {
        const today = getTodayDate();
        let newStartDate = null;
        let newEndDate = null;
        let newTab = '';

        switch (preset) {
            case 'today':
                newStartDate = today;
                newEndDate = today;
                newTab = 'day';
                break;
            case 'yesterday':
                newStartDate = new Date(today);
                newStartDate.setDate(today.getDate() - 1);
                newEndDate = newStartDate;
                newTab = 'yesterday';
                break;
            case 'thisWeek':
                // Cambiar a últimos 7 días incluyendo hoy
                newStartDate = new Date(today);
                newStartDate.setDate(today.getDate() - 6); // 6 días atrás + hoy = 7 días
                newEndDate = today;
                newTab = 'week';
                break;
            case 'lastWeek':
                // Cambiar a los 7 días anteriores a últimos 7 días
                newStartDate = new Date(today);
                newStartDate.setDate(today.getDate() - 13); // 7 días antes del inicio de esta semana
                newEndDate = new Date(today);
                newEndDate.setDate(today.getDate() - 7); // 7 días antes de hoy
                newTab = 'lastWeek';
                break;
            case 'thisMonth':
                /* Implementación anterior
                newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
                newEndDate = today;
                newTab = 'month';  
                */
               newEndDate = new Date(today);
               newEndDate.setDate(today.getDate() - 1); // Yesterday  
               if(today.getTime() === getFirstDayOfCurrentMonth().getTime()){
                    newStartDate = getFirstDayOfGivenMonth(newEndDate);
                }else{  
                    newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
                }
                newTab = 'month';
                break;
            case 'thisPeriod':
                newStartDate = new Date(today.getFullYear(), today.getMonth(), 1);
                newEndDate = today;
                newTab = 'period';
                break;
            case 'lastMonth':
                newStartDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                newEndDate = new Date(today.getFullYear(), today.getMonth(), 0);
                newTab = 'lastMonth';
                break;
            default:
                return;
        }

        // Desactivar la comparación al cambiar de periodo
        if (showComparison) {
            setShowComparison(false);
            setCustomCompareDate(null);
            setCompareData([]);
        }

        setDateRange([newStartDate, newEndDate]);
        setSelectedTab(newTab);
        handleDateRangeMenuClose();
        toggleDateRangeMenu(false);
        closeAllMenus();
    };

    // Función para generar un período de comparación inteligente basado en el período actual
    const generateSmartComparisonPeriod = () => {
        const today = getPeruDate();
        let comparisonStartDate, comparisonEndDate;

        if (dateRange[0] && dateRange[1]) {
            // Verificar si el rango actual corresponde a "Periodo Actual" (del 1 al día actual del mes)
            const isThisPeriod =
                dateRange[0].getDate() === 1 &&
                dateRange[0].getMonth() === today.getMonth() &&
                dateRange[0].getFullYear() === today.getFullYear() &&
                dateRange[1].getDate() === today.getDate() &&
                dateRange[1].getMonth() === today.getMonth() &&
                dateRange[1].getFullYear() === today.getFullYear();

            // Calcular la duración en días del período seleccionado
            const diffTime = Math.abs(dateRange[1] - dateRange[0]);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 para incluir ambos días

            // Verificar si el rango actual corresponde a "Esta semana"
            const startOfCurrentWeek = new Date(
                getTodayDate().setDate(getTodayDate().getDate() - getTodayDate().getDay() + (getTodayDate().getDay() === 0 ? -6 : 1))
            );
            const isThisWeek =
                dateRange[0].getDate() === startOfCurrentWeek.getDate() &&
                dateRange[0].getMonth() === startOfCurrentWeek.getMonth() &&
                dateRange[0].getFullYear() === startOfCurrentWeek.getFullYear() &&
                dateRange[1].getDate() === today.getDate() &&
                dateRange[1].getMonth() === today.getMonth() &&
                dateRange[1].getFullYear() === today.getFullYear();

            // Verificar si el rango corresponde a "Semana pasada"
            const startOfLastWeek = new Date(
                getTodayDate().setDate(getTodayDate().getDate() - getTodayDate().getDay() + (getTodayDate().getDay() === 0 ? -6 : 1))
            );
            const endOfLastWeek = new Date(getTodayDate().setDate(getTodayDate().getDate() - 7));
            const isLastWeek =
                dateRange[0].getDate() === startOfLastWeek.getDate() &&
                dateRange[0].getMonth() === startOfLastWeek.getMonth() &&
                dateRange[0].getFullYear() === startOfLastWeek.getFullYear() &&
                dateRange[1].getDate() === endOfLastWeek.getDate() &&
                dateRange[1].getMonth() === endOfLastWeek.getMonth() &&
                dateRange[1].getFullYear() === endOfLastWeek.getFullYear();

            if (isThisPeriod) {
                // Si es "Periodo Actual", comparar con el mismo rango pero del mes anterior
                const previousMonth = subMonths(today, 1);
                comparisonStartDate = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), 1);

                // Asegurarnos de mantener la misma cantidad de días para el período anterior
                const targetDay = Math.min(today.getDate(), getDaysInMonth(previousMonth));
                comparisonEndDate = new Date(previousMonth.getFullYear(), previousMonth.getMonth(), targetDay);

                return [comparisonStartDate, comparisonEndDate, diffDays];
            } else if (isThisWeek) {
                // Si es "Esta semana", comparar con la semana anterior completa
                comparisonStartDate = subDays(dateRange[0], 7);
                comparisonEndDate = subDays(dateRange[1], 7);
                return [comparisonStartDate, comparisonEndDate, diffDays];
            } else if (isLastWeek) {
                // Si es "Semana pasada", comparar con la semana anterior a esa
                comparisonStartDate = subDays(dateRange[0], 7);
                comparisonEndDate = subDays(dateRange[1], 7);
                return [comparisonStartDate, comparisonEndDate, diffDays];
            } else if (selectedTab === 'day' || selectedTab === 'yesterday') {
                // Si es un solo día, comparar con el día anterior
                comparisonStartDate = subDays(dateRange[0], 1);
                comparisonEndDate = comparisonStartDate;
            } else if (selectedTab === 'month') {
                /*
                // Si es un mes, comparar con el mes anterior
                comparisonStartDate = subMonths(dateRange[0], 1);
                // Mantener la misma cantidad de días en el mes, o usar el último día del mes anterior
                const lastDayOfPrevMonth = endOfMonth(comparisonStartDate);
                const targetEndDay = Math.min(dateRange[1].getDate(), lastDayOfPrevMonth.getDate());
                comparisonEndDate = new Date(lastDayOfPrevMonth.getFullYear(), lastDayOfPrevMonth.getMonth(), targetEndDay);
                */
                // Si es mes (1 del mes al día anterior), comparar con mismo rango del mes anterior
                // Primer día del mes anterior
                comparisonStartDate = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth() - 1, dateRange[0].getDate());

                // Para el día final, necesitamos calcular el mismo día del mes anterior (o el último día si el mes anterior es más corto)
                const daysInPreviousMonth = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth(), 0).getDate();
                const targetEndDay = Math.min(dateRange[1].getDate(), daysInPreviousMonth);

                comparisonEndDate = new Date(dateRange[0].getFullYear(), dateRange[0].getMonth() - 1, targetEndDay);
            } else {
                // Para cualquier otro período, retroceder exactamente la misma cantidad de días
                comparisonStartDate = subDays(dateRange[0], diffDays);
                comparisonEndDate = subDays(dateRange[1], diffDays);
            }

            return [comparisonStartDate, comparisonEndDate, diffDays];
        }

        // En caso de no tener un rango de fechas definido, usar el día actual y comparar con el día anterior
        comparisonStartDate = subDays(today, 1);
        comparisonEndDate = comparisonStartDate;
        return [comparisonStartDate, comparisonEndDate, 1];
    };

    // Función para calcular la proyección de cierre basada en el ritmo actual
    const calculateProjection = (store) => {
        if (!store) return { projectedSale: 0, projectedPercentage: 0 };

        // Obtener la fecha actual de Perú
        const today = getPeruDate();

        // Obtener el porcentaje actual de la tienda usando la función existente
        const currentPercentage = calculatePercentage(store);

        // Para Hoy: proyectar basado en la hora del día
        if (selectedTab === 'day') {
            // Meta diaria
            const todayGoal = parseFloat(store.today_goal || 0);

            // Incrementar el porcentaje en un 20% pero sin limitarlo al 100%
            const projectedPercentage = currentPercentage * 1.2;

            // Calcular la venta proyectada a partir del porcentaje
            const projectedSale = (projectedPercentage / 100) * todayGoal;

            return {
                projectedSale,
                projectedPercentage
            };
        }
        // Para Semana: proyectar basado en días transcurridos de la semana
        else if (selectedTab === 'week' || selectedTab === 'thisWeek') {
            // Meta semanal
            const weeklyGoal = parseFloat(store.period_goal || 0);

            // Ventas actuales para calcular directamente la proyección
            const currentSales = parseFloat(store.progress_sales || 0);

            // Siendo sábado, proyectar 20% más de ventas para el cierre de la semana
            const projectedSale = currentSales * 1.2;

            // CAMBIO: Usar la misma lógica que para period/month para ser consistentes
            // Aumentar el porcentaje actual directamente en lugar de recalcularlo
            const projectedPercentage = currentPercentage * 1.2; // Si las ventas aumentan 20%, el porcentaje también

            return {
                projectedSale,
                projectedPercentage
            };
        }
        // Para Mes: proyectar basado en días transcurridos del mes
        else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'period') {
            // Meta mensual
            const monthlyGoal = parseFloat(store.period_goal || 0);

            // Ventas actuales para calcular directamente la proyección
            const currentSales = parseFloat(store.progress_sales || 0);

            // Proyectar 15% más de ventas para el cierre del mes
            const projectedSale = currentSales * 1.15;

            // CAMBIO: Usar la misma lógica que el cálculo del porcentaje de avance actual
            // Para asegurar que el porcentaje de proyección sea coherente con el actual
            const projectedPercentage = currentPercentage * 1.15; // Si las ventas aumentan 15%, el porcentaje también

            return {
                projectedSale,
                projectedPercentage
            };
        }

        // Para periodos que ya cerraron o casos no considerados
        return { projectedSale: 0, projectedPercentage: 0 };
    };

    return (
        <Box sx={{ maxWidth: '1600px', margin: '0 auto', padding: '16px' }}>
            <DateSelectorContainer className="DateSelectorContainer">
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {/* Botones de periodos predefinidos */}
                    <CompareButton
                        active={selectedTab === 'day'}
                        onClick={() => {
                            applyPresetRange('today');
                            // Disable comparison when selecting "Today" as it's an open cycle
                            if (showComparison) setShowComparison(false);
                        }}
                    >
                        Hoy
                    </CompareButton>

                    <CompareButton active={selectedTab === 'yesterday'} onClick={() => applyPresetRange('yesterday')}>
                        Ayer
                    </CompareButton>

                    <CompareButton
                        active={selectedTab === 'month'}
                        onClick={() => {
                            applyPresetRange('thisMonth');
                            // Check if current month - disable comparison if it's the current month
                            const today = getTodayDate();
                            const isCurrentMonth =
                                today.getMonth() === dateRange[0].getMonth() && today.getFullYear() === dateRange[0].getFullYear();
                            if (isCurrentMonth && showComparison) setShowComparison(false);
                        }}
                    >
                        Mes
                    </CompareButton>

                    <CompareButton
                        active={selectedTab === 'period'}
                        onClick={() => {
                            applyPresetRange('thisPeriod');
                            // Disable comparison when selecting "Avance del mes" as it's an open cycle
                            if (showComparison) setShowComparison(false);
                        }}
                    >
                        Avance del mes
                    </CompareButton>

                    {/* Date picker directamente en la interfaz */}
                    {selectedTab === 'month' ? (
                        <DateRangeInput setMainDateRange={setDateRange} mainDateRange={dateRange} desactivateComparison={desactivateComparison}/>
                    ) : (
                        <DatePicker
                            label="Fecha específica"
                            value={dateRange[0]}
                            inputFormat="dd-MM-yyyy"
                            onChange={(newValue) => {
                                if (newValue && !isNaN(new Date(newValue).getTime())) {
                                    // Set both start and end date to the same value
                                    setDateRange([newValue, newValue]);

                                    // Always disable comparison when selecting a new date
                                    if (showComparison) {
                                        setShowComparison(false);
                                    }

                                    setSelectedTab('date');
                                }
                            }}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    size="small"
                                    sx={{
                                        width: '170px',
                                        '& .MuiOutlinedInput-root': {
                                            borderRadius: '50px',
                                            fontSize: '0.85rem'
                                        }
                                    }}
                                />
                            )}
                            maxDate={getTodayDate()}
                        />
                    )}
                </Box>

                {/* Radio button para comparación - solo visible para ciclos cerrados */}
                {isComparisonAllowed() && (
                    <Box sx={{ display: 'flex', alignItems: 'center', ml: 2 }}>
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={showComparison}
                                    onChange={toggleComparison}
                                    size="small"
                                    sx={{
                                        '& .MuiSwitch-switchBase.Mui-checked': {
                                            color: '#b3256e'
                                        },
                                        '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                                            backgroundColor: '#b3256e',
                                            opacity: 0.5
                                        }
                                    }}
                                />
                            }
                            label="Comparar con periodo anterior"
                            sx={{
                                '& .MuiFormControlLabel-label': {
                                    fontSize: '0.85rem',
                                    color: '#555',
                                    fontWeight: showComparison ? 'medium' : 'normal'
                                }
                            }}
                        />
                    </Box>
                )}
            </DateSelectorContainer>
            <TotalSection>
                <TotalCard>
                    <TotalHeader showComparison={showComparison}>
                        {/* Título, fecha y comparación en la misma línea */}
                        <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-start', alignItems: 'center', mb: 1 }}>
                            {/* Título */}
                            <TotalTitle sx={{ display: 'flex', alignItems: 'center' }}>
                                <TotalTitleIcon>M</TotalTitleIcon>
                                TOTAL MIA:
                            </TotalTitle>

                            {/* Fecha en negrita y tamaño grande */}
                            <Typography sx={{fontWeight: 'bold', fontSize: '1.2rem', ml: 2 }}>
                                {
                                    selectedTab === 'day'
                                        ? getTodayDate().toLocaleDateString('es-ES', { weekday: 'long', day: '2-digit', month: 'long', year: 'numeric' }).toUpperCase()
                                        : selectedTab === 'yesterday'
                                        ? new Date(getTodayDate().setDate(getTodayDate().getDate() - 1)).toLocaleDateString('es-ES', {
                                            weekday: 'long', day: '2-digit', month: 'long', year: 'numeric'
                                        }).toUpperCase()
                                        : ['week', 'thisWeek', 'lastWeek', 'period', 'date'].includes(selectedTab)
                                        ? isSameDay(dateRange[0], dateRange[1])
                                            ? formatFriendlyDate(dateRange[0])
                                            : `${formatFriendlyDate(dateRange[0])} - ${formatFriendlyDate(dateRange[1])}`
                                        : selectedTab === 'month'
                                        ? isFullMonth(dateRange[0], dateRange[1])
                                            ? dateRange[0].toLocaleDateString('es-ES', { month: 'long', year: 'numeric' }).toUpperCase()
                                            : `${formatFriendlyDate(dateRange[0])} - ${formatFriendlyDate(dateRange[1])}`
                                        : formatFriendlyDate(getPeruDate())
                                }
                            </Typography>


                            {/* Margen entre la fecha y el texto de comparación */}
                            {showComparison && <Box sx={{ width: '20px' }} />}

                            {/* Texto de comparación al mismo nivel que la fecha y el título */}
                            {showComparison && (
                                <ComparisonInfo sx={{ width: 'auto' }}>
                                    <CompareIcon />
                                    <Typography component="span" sx={{ fontSize: 'inherit', fontWeight: 'medium' }}>
                                        Comparando con {getComparisonText()}
                                    </Typography>
                                </ComparisonInfo>
                            )}
                        </Box>
                    </TotalHeader>

                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                        <Box>
                            <Typography variant="body2" sx={{ color: '#666', fontSize: '0.8rem' }}>
                                {selectedTab === 'day' || selectedTab === 'yesterday'
                                    ? 'Venta diaria'
                                    : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
                                    ? 'Venta del mes'
                                    : 'Venta a la fecha'}
                            </Typography>
                            <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                                {formatNumber(
                                    selectedTab === 'day' || selectedTab === 'yesterday' ? totals.today_sales : totals.sale_progress
                                )}
                            </Typography>
                            {showComparison && compareData && compareData.length > 0 && (
                                <>
                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', mt: 0.5 }}>
                                        {formatNumber(
                                            selectedTab === 'day' || selectedTab === 'yesterday'
                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.today_sales || 0), 0)
                                                : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0)
                                                : compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0)
                                        )}
                                    </Typography>
                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.65rem', fontStyle: 'italic' }}>
                                        en periodo anterior
                                    </Typography>
                                </>
                            )}
                        </Box>

                        <Box sx={{ width: '55%', display: 'flex', flexDirection: 'column', gap: 0.5, mx: 1 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'center', mb: 0.5, alignItems: 'center' }}>
                                <Typography variant="body1" sx={{ color: '#555', fontSize: '1rem', mr: 1, fontWeight: 'medium' }}>
                                    Cumplimiento:
                                </Typography>
                                <MetricPercentage percentage={totalPercentage} sx={{ fontSize: '1.1rem' }}>
                                    {Math.min(totalPercentage, 999.99).toFixed(2)}%
                                </MetricPercentage>
                            </Box>
                            {showComparison && compareData && compareData.length > 0 && (
                                <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', mr: 0.5 }}>
                                        Anterior:
                                    </Typography>
                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', fontStyle: 'italic' }}>
                                        {Math.min(
                                            selectedTab === 'day' || selectedTab === 'yesterday'
                                                ? calculatePercentage(
                                                      compareData.reduce((sum, item) => sum + parseFloat(item.today_sales || 0), 0),
                                                      compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0)
                                                  )
                                                : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
                                                ? calculatePercentage(
                                                      compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0),
                                                      compareData.reduce((sum, item) => sum + parseFloat(item.period_goal || 0), 0)
                                                  )
                                                : calculatePercentage(
                                                      compareData.reduce((sum, item) => sum + parseFloat(item.progress_sales || 0), 0),
                                                      compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) *
                                                          totalDays
                                                  ),
                                            999.99
                                        ).toFixed(2)}
                                        %
                                    </Typography>
                                </Box>
                            )}

                            <StyledLinearProgress
                                variant="determinate"
                                value={Math.min(totalPercentage, 100)}
                                percentage={Math.min(totalPercentage, 100)}
                            />
                        </Box>

                        <Box sx={{ textAlign: 'right' }}>
                            <Typography variant="body2" sx={{ color: '#666', fontSize: '0.8rem' }}>
                                {selectedTab === 'day' || selectedTab === 'yesterday'
                                    ? 'Meta diaria'
                                    : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
                                    ? 'Meta del mes'
                                    : 'Meta a la fecha'}
                            </Typography>
                            <Typography variant="h6" sx={{ fontWeight: 'bold', fontSize: '1.1rem' }}>
                                {formatNumber(
                                    selectedTab === 'day' || selectedTab === 'yesterday'
                                        ? totals.today_goal
                                        : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
                                        ? totals.period_goal
                                        : totals.today_goal * totalDays
                                )}
                            </Typography>
                            {showComparison && compareData && compareData.length > 0 && (
                                <>
                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.75rem', mt: 0.5 }}>
                                        {formatNumber(
                                            selectedTab === 'day' || selectedTab === 'yesterday'
                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0)
                                                : selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth'
                                                ? compareData.reduce((sum, item) => sum + parseFloat(item.period_goal || 0), 0)
                                                : compareData.reduce((sum, item) => sum + parseFloat(item.today_goal || 0), 0) * totalDays
                                        )}
                                    </Typography>
                                    <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.65rem', fontStyle: 'italic' }}>
                                        en periodo anterior
                                    </Typography>
                                </>
                            )}
                        </Box>

                        <Box sx={{ ml: 2 }}>
                            <PredictButton
                                onClick={() => handlePredictClick('Total MIA')}
                                startIcon={<span></span>}
                                sx={{ padding: '6px 20px', fontSize: '0.9rem' }}
                            >
                                Análisis IA
                            </PredictButton>
                        </Box>
                    </Box>
                </TotalCard>
            </TotalSection>

            <StoreGrid>
                {data.map((store, index) => {
                    // Calcular porcentaje para hoy/ayer
                    const dailyPercentage = calculatePercentage(store.today_sales, store.today_goal);

                    // Calcular porcentaje para mes
                    const monthlyPercentage = calculatePercentage(store.progress_sales, store.period_goal);

                    // Calcular porcentaje para semana/rango (meta diaria * total días)
                    const dateGoal = parseFloat(store.today_goal) * totalDays;
                    const datePercentage = calculatePercentage(store.progress_sales, dateGoal);

                    let currentPercentage;
                    if (selectedTab === 'day' || selectedTab === 'yesterday') {
                        currentPercentage = dailyPercentage;
                    } else if (selectedTab === 'month' || selectedTab === 'thisMonth' || selectedTab === 'lastMonth') {
                        currentPercentage = monthlyPercentage;
                    } else {
                        currentPercentage = datePercentage;
                    }

                    // Buscar datos comparativos para esta tienda
                    let compareStore = null;
                    let comparePercentage = 0;
                    let percentageDifference = 0;

                    if (showComparison && compareData.length > 0) {
                        compareStore = compareData.find((cs) => cs.business_unit_id === store.business_unit_id);

                        if (compareStore) {
                            if (selectedTab === 'day' || selectedTab === 'yesterday') {
                                comparePercentage = calculatePercentage(compareStore.today_sales, compareStore.today_goal);
                            } else if (selectedTab === 'month') {
                                comparePercentage = calculatePercentage(compareStore.progress_sales, compareStore.period_goal);
                            } else {
                                const compareGoal = parseFloat(compareStore.today_goal) * totalDays;
                                comparePercentage = calculatePercentage(compareStore.progress_sales, compareGoal);
                            }

                            percentageDifference = currentPercentage - comparePercentage;
                        }
                    }

                    return (
                        <Box key={store.business_unit_id}>
                            <MetricCard>
                                <MetricHeader style={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                    <StoreName>{store.business_unit_name}</StoreName>
                                    
                                </MetricHeader>

                                <MetricContent>
                                    {/* Sección del gráfico - ahora a ancho completo y más grande */}
                                    <Box
                                        sx={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            alignItems: 'center',
                                            width: '100%',
                                            position: 'relative'
                                        }}
                                    >
                                        {/* Gráfico RadialBar como "sombrero" */}
                                        <ChartContainer sx={{ position: 'relative', mb: 1 }}>
                                            <ResponsiveContainer width="100%" height={180}>
                                                <RadialBarChart
                                                    cx="50%"
                                                    cy="50%"
                                                    innerRadius={70}
                                                    outerRadius={90}
                                                    barSize={10}
                                                    data={[
                                                        // Comparison period bar should be first in array (drawn underneath)
                                                        ...(showComparison && compareStore
                                                            ? [
                                                                  {
                                                                      name: 'Comparación',
                                                                      value: calculatePercentage(compareStore),
                                                                      fill: 'rgba(150, 150, 150, 0.8)',
                                                                      innerRadius: 55,
                                                                      outerRadius: 65
                                                                  }
                                                              ]
                                                            : []),
                                                        // Current period bar should be last (drawn on top)
                                                        {
                                                            name: 'Avance',
                                                            value: currentPercentage,
                                                            fill: getStoreColor(store.business_unit_name, index, currentPercentage),
                                                            innerRadius: 70,
                                                            outerRadius: 90
                                                        }
                                                    ]}
                                                    startAngle={180}
                                                    endAngle={0}
                                                >
                                                    <PolarAngleAxis type="number" domain={[0, 100]} angleAxisId={0} tick={false} />
                                                    <RadialBar
                                                        background
                                                        backgroundClassName="radial-bar-background"
                                                        dataKey="value"
                                                        cornerRadius={0}
                                                    />
                                                </RadialBarChart>
                                            </ResponsiveContainer>

                                            {/* Porcentaje centrado dentro del gráfico */}
                                            <Box
                                                sx={{
                                                    position: 'absolute',
                                                    top: '41%',
                                                    left: '50%',
                                                    transform: 'translate(-50%, -50%)',
                                                    zIndex: 10,
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center'
                                                }}
                                            >
                                                <MetricPercentage
                                                    percentage={currentPercentage}
                                                    sx={{ fontSize: '1.4rem', fontWeight: '700' }}
                                                >
                                                    {currentPercentage.toFixed(2)}%
                                                </MetricPercentage>
                                                {showComparison && compareStore && (
                                                    <>
                                                        <Typography sx={{ fontSize: '0.8rem', color: 'rgba(100, 100, 100, 0.6)', mt: 0.5 }}>
                                                            {calculatePercentage(compareStore).toFixed(2)}%
                                                        </Typography>
                                                        <Typography
                                                            sx={{
                                                                fontSize: '0.65rem',
                                                                fontStyle: 'italic',
                                                                color: 'rgba(100, 100, 100, 0.6)'
                                                            }}
                                                        >
                                                            en periodo anterior
                                                        </Typography>
                                                    </>
                                                )}
                                            </Box>

                                            {/* Monto de Venta Total (izquierda) */}
                                            <Box
                                                sx={{
                                                    position: 'absolute',
                                                    bottom: '-5px',
                                                    left: '0',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    width: '100px',
                                                    textAlign: 'center'
                                                }}
                                            >
                                                <Typography sx={{ fontWeight: 'bold', fontSize: '0.9rem', color: '#333' }}>
                                                    {formatNumber(
                                                        selectedTab === 'day' || selectedTab === 'yesterday'
                                                            ? store.today_sales
                                                            : store.progress_sales
                                                    )}
                                                </Typography>
                                                <Typography sx={{ fontSize: '0.75rem', color: '#666' }}>Venta</Typography>
                                                {showComparison && compareStore && (
                                                    <>
                                                        <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}>
                                                            {formatNumber(
                                                                selectedTab === 'day' || selectedTab === 'yesterday'
                                                                    ? compareStore.today_sales
                                                                    : compareStore.progress_sales
                                                            )}
                                                        </Typography>
                                                        <Typography
                                                            sx={{
                                                                color: 'rgba(100, 100, 100, 0.6)',
                                                                fontSize: '0.65rem',
                                                                fontStyle: 'italic'
                                                            }}
                                                        >
                                                            en periodo anterior
                                                        </Typography>
                                                    </>
                                                )}
                                            </Box>

                                            {/* Monto de Meta (derecha) */}
                                            <Box
                                                sx={{
                                                    position: 'absolute',
                                                    bottom: '-5px',
                                                    right: '0',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    width: '100px',
                                                    textAlign: 'center'
                                                }}
                                            >
                                                <Typography sx={{ fontWeight: 'bold', fontSize: '0.9rem', color: '#333' }}>
                                                    {formatNumber(
                                                        selectedTab === 'day' || selectedTab === 'yesterday'
                                                            ? store.today_goal
                                                            : selectedTab === 'month' ||
                                                              selectedTab === 'thisMonth' ||
                                                              selectedTab === 'lastMonth'
                                                            ? store.period_goal
                                                            : store.today_goal * totalDays
                                                    )}
                                                </Typography>
                                                <Typography sx={{ fontSize: '0.75rem', color: '#666' }}>Meta</Typography>
                                                {showComparison && compareStore && (
                                                    <>
                                                        <Typography sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}>
                                                            {formatNumber(
                                                                selectedTab === 'day' || selectedTab === 'yesterday'
                                                                    ? compareStore.today_goal
                                                                    : selectedTab === 'month' ||
                                                                      selectedTab === 'thisMonth' ||
                                                                      selectedTab === 'lastMonth'
                                                                    ? compareStore.period_goal
                                                                    : compareStore.today_goal * totalDays
                                                            )}
                                                        </Typography>
                                                        <Typography
                                                            sx={{
                                                                color: 'rgba(100, 100, 100, 0.6)',
                                                                fontSize: '0.65rem',
                                                                fontStyle: 'italic'
                                                            }}
                                                        >
                                                            en periodo anterior
                                                        </Typography>
                                                    </>
                                                )}
                                            </Box>

                                            {/* Monto Faltante (centro abajo) */}
                                            <Box
                                                sx={{
                                                    position: 'absolute',
                                                    bottom: '-45px',
                                                    left: '50%',
                                                    transform: 'translateX(-50%)',
                                                    display: 'flex',
                                                    flexDirection: 'column',
                                                    alignItems: 'center',
                                                    justifyContent: 'center',
                                                    textAlign: 'center'
                                                }}
                                            >
                                                {['day', 'period'].includes(selectedTab) &&
                                                    (() => {
                                                        const difference =
                                                            selectedTab === 'day' || selectedTab === 'yesterday'
                                                                ? store.today_sales - store.today_goal
                                                                : selectedTab === 'month' ||
                                                                  selectedTab === 'thisMonth' ||
                                                                  selectedTab === 'lastMonth'
                                                                ? store.progress_sales - store.period_goal
                                                                : store.progress_sales - store.today_goal * totalDays;

                                                        const isExcess = difference >= 0;

                                                        return (
                                                            <>
                                                                <Typography
                                                                    sx={{
                                                                        fontWeight: 'bold',
                                                                        fontSize: '0.9rem',
                                                                        color: isExcess ? '#388e3c' : '#888'
                                                                    }}
                                                                >
                                                                    {formatNumber(Math.abs(difference))}
                                                                </Typography>
                                                                <Typography sx={{ fontSize: '0.75rem', color: '#666' }}>
                                                                    {isExcess ? 'Excedente' : 'Faltante'}
                                                                </Typography>
                                                            </>
                                                        );
                                                    })()}
                                            </Box>
                                        </ChartContainer>
                                    </Box>

                                    {/* Sección de información - con diseño más compacto */}
                                    <MetricInfo>
                                        <MetricValues
                                            sx={{
                                                display: 'grid',
                                                gridTemplateColumns: 'repeat(2, 1fr)',
                                                gap: '8px',
                                                width: '100%',
                                                mt: 3
                                            }}
                                        >
                                            {selectedTab === 'day' ? (
                                                <>
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>
                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>
                                                            Ticket Promedio
                                                        </Typography>
                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>
                                                            {formatNumber(store.today_average_ticket)}
                                                        </Typography>
                                                        {showComparison && compareStore && (
                                                            <>
                                                                <Typography
                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}
                                                                >
                                                                    {formatNumber(compareStore.today_average_ticket)}
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: 'rgba(100, 100, 100, 0.6)',
                                                                        fontSize: '0.65rem',
                                                                        fontStyle: 'italic'
                                                                    }}
                                                                >
                                                                    en periodo anterior
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </Box>
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>
                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>
                                                            Bolsa Promedio
                                                        </Typography>
                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>
                                                            {store.today_drop_size}
                                                        </Typography>
                                                        {showComparison && compareStore && (
                                                            <>
                                                                <Typography
                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}
                                                                >
                                                                    {compareStore.today_drop_size}
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: 'rgba(100, 100, 100, 0.6)',
                                                                        fontSize: '0.65rem',
                                                                        fontStyle: 'italic'
                                                                    }}
                                                                >
                                                                    en periodo anterior
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </Box>
                                                </>
                                            ) : selectedTab === 'month' ? (
                                                <>
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>
                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>
                                                            Ticket Promedio
                                                        </Typography>
                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>
                                                            {formatNumber(store.progress_average_ticket)}
                                                        </Typography>
                                                        {showComparison && compareStore && (
                                                            <>
                                                                <Typography
                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}
                                                                >
                                                                    {formatNumber(compareStore.progress_average_ticket)}
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: 'rgba(100, 100, 100, 0.6)',
                                                                        fontSize: '0.65rem',
                                                                        fontStyle: 'italic'
                                                                    }}
                                                                >
                                                                    en periodo anterior
                                                                </Typography>
                                                            </>
                                                        )}

                                                        {/* Proyección de cierre para Mes */}
                                                        {(selectedTab === 'month' || selectedTab === 'thisMonth') && !showComparison && (
                                                            <>
                                                                <Typography
                                                                    sx={{
                                                                        color: '#000',
                                                                        fontSize: '0.75rem',
                                                                        fontWeight: 'medium',
                                                                        mt: 1.5,
                                                                        borderTop: '1px dashed rgba(0,0,0,0.1)',
                                                                        pt: 0.5,
                                                                        textAlign: 'center',
                                                                        width: '100%'
                                                                    }}
                                                                >
                                                                    Proyección de cierre
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: '#000',
                                                                        fontSize: '0.9rem',
                                                                        fontWeight: 'bold',
                                                                        textAlign: 'center',
                                                                        width: '100%'
                                                                    }}
                                                                >
                                                                    {(() => {
                                                                        //const projection = calculateProjection(store);
                                                                        const projectionSales = store.closing_projection_sales;
                                                                        const projectionPercentage =
                                                                            store.closing_projection_percentage * 100;
                                                                        return (
                                                                            <>
                                                                                {formatNumber(projectionSales)}
                                                                                <span
                                                                                    style={{
                                                                                        fontSize: '0.7rem',
                                                                                        fontWeight: 'normal',
                                                                                        marginLeft: '4px'
                                                                                    }}
                                                                                >
                                                                                    ({projectionPercentage.toFixed(2)}%)
                                                                                </span>
                                                                            </>
                                                                        );
                                                                    })()}
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </Box>

                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>
                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>
                                                            Bolsa Promedio
                                                        </Typography>
                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>
                                                            {store.progress_drop_size}
                                                        </Typography>
                                                        {showComparison && compareStore && (
                                                            <>
                                                                <Typography
                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}
                                                                >
                                                                    {compareStore.progress_drop_size}
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: 'rgba(100, 100, 100, 0.6)',
                                                                        fontSize: '0.65rem',
                                                                        fontStyle: 'italic'
                                                                    }}
                                                                >
                                                                    en periodo anterior
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </Box>
                                                </>
                                            ) : (
                                                <>
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>
                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>
                                                            Ticket Promedio
                                                        </Typography>
                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>
                                                            {formatNumber(store.progress_average_ticket)}
                                                        </Typography>
                                                        {showComparison && compareStore && (
                                                            <>
                                                                <Typography
                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}
                                                                >
                                                                    {formatNumber(compareStore.progress_average_ticket)}
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: 'rgba(100, 100, 100, 0.6)',
                                                                        fontSize: '0.65rem',
                                                                        fontStyle: 'italic'
                                                                    }}
                                                                >
                                                                    en periodo anterior
                                                                </Typography>
                                                            </>
                                                        )}

                                                        {/* Proyección de cierre para Semana y Periodo */}
                                                        {(selectedTab === 'week' || selectedTab === 'thisWeek') && (
                                                            <>
                                                                <Typography
                                                                    sx={{
                                                                        color: '#000',
                                                                        fontSize: '0.75rem',
                                                                        fontWeight: 'medium',
                                                                        mt: 1.5,
                                                                        borderTop: '1px dashed rgba(0,0,0,0.1)',
                                                                        pt: 0.5,
                                                                        textAlign: 'center',
                                                                        width: '100%'
                                                                    }}
                                                                >
                                                                    Proyección de cierre
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: '#000',
                                                                        fontSize: '0.9rem',
                                                                        fontWeight: 'bold',
                                                                        textAlign: 'center',
                                                                        width: '100%'
                                                                    }}
                                                                >
                                                                    {(() => {
                                                                        const projection = calculateProjection(store);
                                                                        return (
                                                                            <>
                                                                                {formatNumber(projection.projectedSale)}
                                                                                <span
                                                                                    style={{
                                                                                        fontSize: '0.7rem',
                                                                                        fontWeight: 'normal',
                                                                                        marginLeft: '4px'
                                                                                    }}
                                                                                >
                                                                                    ({projection.projectedPercentage.toFixed(2)}%)
                                                                                </span>
                                                                            </>
                                                                        );
                                                                    })()}
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </Box>
                                                    <Box sx={{ display: 'flex', flexDirection: 'column', p: 1 }}>
                                                        <Typography sx={{ color: '#666', fontSize: '0.85rem', fontWeight: 'medium' }}>
                                                            Bolsa Promedio
                                                        </Typography>
                                                        <Typography sx={{ color: '#333', fontSize: '0.95rem', fontWeight: 'bold' }}>
                                                            {store.progress_drop_size}
                                                        </Typography>
                                                        {showComparison && compareStore && (
                                                            <>
                                                                <Typography
                                                                    sx={{ color: 'rgba(100, 100, 100, 0.6)', fontSize: '0.7rem', mt: 0.5 }}
                                                                >
                                                                    {store.progress_drop_size}
                                                                </Typography>
                                                                <Typography
                                                                    sx={{
                                                                        color: 'rgba(100, 100, 100, 0.6)',
                                                                        fontSize: '0.65rem',
                                                                        fontStyle: 'italic'
                                                                    }}
                                                                >
                                                                    en periodo anterior
                                                                </Typography>
                                                            </>
                                                        )}
                                                    </Box>
                                                </>
                                            )}
                                        </MetricValues>
                                    </MetricInfo>
                                </MetricContent>

                                <ButtonContainer>
                                    <PredictButton onClick={() => handlePredictClick(store.business_unit_name)} startIcon={<span></span>}>
                                        Análisis IA
                                    </PredictButton>
                                </ButtonContainer>
                            </MetricCard>
                        </Box>
                    );
                })}
            </StoreGrid>
            <FooterLegend>
                <LegendGroup>
                    <LegendDot color={trafficLightColors.danger} />
                    <Typography variant="body2">0 - {globalLegendPercentages.red}% - Bajo</Typography>
                </LegendGroup>
                <LegendGroup>
                    <LegendDot color={trafficLightColors.orange} />
                    <Typography variant="body2">
                        {globalLegendPercentages.red + 1}% - {globalLegendPercentages.orange}% - Regular
                    </Typography>
                </LegendGroup>
                <LegendGroup>
                    <LegendDot color={trafficLightColors.warning} />
                    <Typography variant="body2">
                        {globalLegendPercentages.orange + 1}% - {globalLegendPercentages.green - 1}% - Bueno
                    </Typography>
                </LegendGroup>
                <LegendGroup>
                    <LegendDot color={trafficLightColors.success} />
                    <Typography variant="body2">{globalLegendPercentages.green}% - 100% Excelente</Typography>
                </LegendGroup>
            </FooterLegend>
            <PredictionModal
                open={predictionModalOpen}
                onClose={() => setPredictionModalOpen(false)}
                storeName={selectedStore}
                storeData={selectedStoreData}
                selectedTab={selectedTab}
            />
            {/* Menu de comparación */}
            <Menu
                id="compare-menu"
                anchorEl={compareMenuAnchor}
                open={Boolean(compareMenuAnchor)}
                onClose={handleCompareMenuClose}
                anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'left'
                }}
                transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left'
                }}
                PaperProps={{
                    elevation: 3,
                    sx: { borderRadius: '8px' }
                }}
                disableScrollLock
                disableAutoFocusItem
                keepMounted={false}
            >
                <MenuItem onClick={() => handleQuickCompareSelect('days', 1)}>1 día</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('days', 7)}>7 días</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('days', 14)}>14 días</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('days', 30)}>30 días</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('months', 1)}>1 mes</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('months', 3)}>3 meses</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('months', 6)}>6 meses</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('years', 1)}>1 año</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('years', 2)}>2 años</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('years', 3)}>3 años</MenuItem>
                <MenuItem onClick={() => handleQuickCompareSelect('custom', 0)}>Fecha personalizada</MenuItem>
            </Menu>
        </Box>
    );
};
export default SummaryTab;
