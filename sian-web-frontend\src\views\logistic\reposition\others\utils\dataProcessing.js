import { round } from 'utils/number';
import { UNIT_EQUIVALENCE } from 'models/Presentation';

/**
 * Procesa los datos de análisis agregando campos necesarios para el formateo
 */
export const processAnalisys = (productData, storeData) => {
    if (!productData || !productData.analisys || !Array.isArray(productData.analisys)) {
        return [];
    }

    const listData = productData.analisys.map((item) => {
        const store = storeData.find((s) => s.store_id === item.store_id);

        return {
            ...item,
            pk: `${item.store_id}_${productData.product_id}`,
            store_name: store ? store.store_name : 'Tienda no encontrada',
            product_id: productData.product_id,
            presentations: productData.presentations || {},
            equivalence_default: productData.equivalence_default || 1,
            presentation: productData.presentation || '',
            measure_default: productData.measure_default || productData.measure_name || 'GR'
        };
    });

    return listData;
};

export const findDerivedProductRecursively = (derivedProducts, targetId) => {
    if (!derivedProducts || !Array.isArray(derivedProducts)) return null;

    const foundDerived = derivedProducts.find((derived) => {
        if (derived.product_id === targetId) {
            return true;
        }

        if (derived.derivedProducts && Array.isArray(derived.derivedProducts)) {
            const found = findDerivedProductRecursively(derived.derivedProducts, targetId);
            return found !== null;
        }
        return false;
    });

    if (foundDerived) {
        if (foundDerived.product_id === targetId) {
            return foundDerived;
        }
        // Si no es el producto actual, buscar en sus derivedProducts
        return findDerivedProductRecursively(foundDerived.derivedProducts, targetId);
    }

    return null;
};

export const findMainProductWithDerivedProduct = (data, derivedProductId) => {
    if (!data || !Array.isArray(data)) return null;

    const foundProduct = data.find((product) => {
        if (product.derivedProducts && Array.isArray(product.derivedProducts)) {
            const found = findDerivedProductRecursively(product.derivedProducts, derivedProductId);
            return found !== null;
        }
        return false;
    });

    return foundProduct || null;
};

export const isRowDataAvailable = (rowData) => rowData && !rowData.notAvailable;

export const calculateDerivedProductNeto = (derivedProduct) => {
    if (!derivedProduct.analisys || !Array.isArray(derivedProduct.analisys)) {
        return 0;
    }

    const sumaNetos = derivedProduct.analisys.reduce((sum, analysis) => {
        const storeStock = parseFloat(analysis.purchase_stock || 0);
        const storeProjection = parseFloat(analysis.unit_quantity_proyected || 0);
        const storeNeto = storeProjection - storeStock;
        return sum + storeNeto;
    }, 0);

    const stockAlmacenPrincipal = parseFloat(derivedProduct.supplying_stock || 0);
    const netoFinal = sumaNetos - stockAlmacenPrincipal;

    return Math.max(0, netoFinal);
};

export const calculateProyeccionFinal = (materiaPrima) => {
    if (!materiaPrima.derivedProducts || materiaPrima.derivedProducts.length === 0) {
        return 0;
    }

    // Calcular la suma de netos de todos los productos derivados
    let totalDerivedNetos = 0;

    materiaPrima.derivedProducts.forEach((derivedProduct) => {
        // Usar el NETO ya calculado en el procesamiento
        const derivedNeto = parseFloat(derivedProduct.calculated_neto || 0);
        totalDerivedNetos += derivedNeto;
    });

    // Multiplicar por waste_info.multiplier_to_total
    const multiplier = parseFloat(materiaPrima?.waste_info?.multiplier_to_total || 1);
    const proyeccionFinal = totalDerivedNetos * multiplier;

    // Si es negativo, devolver 0
    return Math.max(0, proyeccionFinal);
};

/**
 * Calcula el NETO de un producto derivado basado en la suma de to_transform del análisis
 */
export const calculateDerivedProductNetoFromAnalysis = (derivedProduct) => {
    if (!derivedProduct.analisys || !Array.isArray(derivedProduct.analisys)) {
        return 0;
    }

    // Sumar todos los to_transform del análisis
    const netoValue = derivedProduct.analisys.reduce((sum, analysis) => sum + parseFloat(analysis.to_transform || 0), 0);

    return netoValue;
};

/**
 * Procesa los datos de RAW_MATERIAL agregando campos calculados y cantidad a comprar
 */
export const processRawMaterialData = (data, updateFormSupplyDataAction, removeFormSupplyDataAction) => {
    if (!data || !Array.isArray(data)) return data;

    return data.map((product) => {
        let processedDerivedProducts = product.derivedProducts;
        let proyeccionFinal = 0;
        let cReponer = 0;

        if (product.derivedProducts && product.derivedProducts.length > 0) {
            processedDerivedProducts = product.derivedProducts.map((derivedProduct) => {
                const calculatedNetoFromAnalysis = calculateDerivedProductNetoFromAnalysis(derivedProduct);

                const calculatedNeto = calculateDerivedProductNeto(derivedProduct);

                let derivedCReponer = 0;
                let derivedNetoFinal = 0;
                let grossAmount = 0;

                if (derivedProduct.derivedProducts && derivedProduct.derivedProducts.length > 0) {
                    derivedCReponer = derivedProduct.derivedProducts.reduce((sum, subDerived) => {
                        const subDerivedNeto = calculateDerivedProductNetoFromAnalysis(subDerived);
                        return sum + subDerivedNeto;
                    }, 0);

                    const stockTiendas = parseFloat(derivedProduct.purchase_stock || 0);
                    const stockAlmacen = parseFloat(derivedProduct.supplying_stock || 0);

                    if (derivedProduct.waste_info) {
                        derivedNetoFinal = Math.max(
                            0,
                            derivedCReponer * derivedProduct.waste_info.multiplier_to_total - (stockTiendas + stockAlmacen)
                        );
                    } else {
                        derivedNetoFinal = Math.max(0, derivedCReponer - (stockTiendas + stockAlmacen));
                    }
                } else {
                    derivedCReponer = calculatedNetoFromAnalysis;
                    derivedNetoFinal = calculatedNeto;
                }

                if (derivedProduct.waste_info) {
                    grossAmount = derivedCReponer * derivedProduct.waste_info.multiplier_to_total;
                }

                return {
                    ...derivedProduct,
                    calculated_neto: derivedNetoFinal,
                    calculated_neto_from_analysis: calculatedNetoFromAnalysis,
                    c_reponer: derivedCReponer,
                    gross_amount: grossAmount
                };
            });

            cReponer = processedDerivedProducts.reduce(
                (sum, derivedProduct) => sum + parseFloat(derivedProduct.calculated_neto_from_analysis || 0),
                0
            );

            const productWithProcessedDerived = {
                ...product,
                derivedProducts: processedDerivedProducts
            };

            proyeccionFinal = calculateProyeccionFinal(productWithProcessedDerived);
        }

        const stockAlmacenPrincipal = parseFloat(product.supplying_stock || 0);
        const cantidadAComprar = Math.max(0, proyeccionFinal - stockAlmacenPrincipal);

        if (updateFormSupplyDataAction) {
            const pk = product.product_id;
            if (cantidadAComprar > 0) {
                const newData = {
                    product_id: product.product_id,
                    product_name: product.product_name,
                    quantity_oc: round(cantidadAComprar),
                    provider: product.provider ?? 'SIN PROVEEDOR',
                    provider_id: product.provider_id ?? '0000',
                    provider_number: product.provider_number ?? '**********',
                    equivalence: UNIT_EQUIVALENCE,
                    presentation: product.presentation,
                    unit_price: parseFloat(product.unit_price || 0)
                };

                updateFormSupplyDataAction({ pk, newData });
            } else if (removeFormSupplyDataAction) {
                removeFormSupplyDataAction(pk);
            }
        }

        return {
            ...product,
            derivedProducts: processedDerivedProducts,
            proyeccion_final: proyeccionFinal,
            cantidad_a_comprar: cantidadAComprar,
            c_reponer: cReponer
        };
    });
};
