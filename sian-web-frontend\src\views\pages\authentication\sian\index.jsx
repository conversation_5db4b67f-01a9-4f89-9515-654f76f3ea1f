import { useEffect, useState } from 'react';
import sianLogo from 'assets/images/sian-logo.svg';
import { useLocation } from 'react-router-dom';
import axios from 'utils/axios';
import Grid from '@mui/material/Grid';
import CircularProgress from '@mui/material/CircularProgress';
import Box from '@mui/material/Box';
import Background from 'assets/images/auth/background.svg';
import { Typography } from '@mui/material';

const setSession = (authorisation, user, org, files) => {
    authorisation = authorisation ?? {};

    if (Object.values(authorisation).length > 0) {
        localStorage.setItem('authorisation', JSON.stringify(authorisation));
        localStorage.setItem('token', authorisation.token);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('org', JSON.stringify(org));
        localStorage.setItem('files', JSON.stringify(files));
        axios.defaults.headers.common.Authorization = `Bearer ${authorisation.token}`;
    } else {
        localStorage.removeItem('authorisation');
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('org');
        localStorage.removeItem('files');
        delete axios.defaults.headers.common.Authorization;
    }
};

const Index = () => {
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(false);
    const [message, setMessage] = useState('');
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const user = params.get('user');
    const token = params.get('token');
    const domainRedirect = params.get('domain_redirect');
    const adminUrlRedirect = params.get('admin_url_redirect') ?? '';
    const path = params.get('url') ?? '';
    const redirectParam = params.get('redirect') ?? '';

    const buildRedirectUrl = (domain, pathSian = 'site/login.html', userToken = '', path = '', redirect = '') => {
        let urlRedirect = `${domain}/admin/${pathSian}`;

        const params = [];
        if (redirect !== '') {
            params.push(`redirect=${redirect}`);
        }
        if (userToken !== '') {
            params.push(`token=${userToken}`);
        }
        if (path !== '') {
            params.push(`path=${path}`);
        }

        if (params.length > 0) {
            urlRedirect += `?${params.join('&')}`;
        }

        return urlRedirect;
    };

    const loginFromSian = async (domain, path, username, token, redirect) => {
        try {
            const response = await axios.post('/api/V1/auth/login-from-sian', { username, token });
            if (response.status === 200) {
                if (response.data.success) {
                    const { authorisation, data, files } = response.data;
                    const { userToken, pathSian, user, org } = data;

                    setSession(authorisation, user, org, files);
                    setMessage('Se inicio sesión correctamente.');
                    setLoading(false);
                    setError(false);

                    const urlRedirect = buildRedirectUrl(domain, pathSian, userToken, path, redirect);

                    window.location.replace(urlRedirect);
                }
                if (!response.data.success) {
                    setMessage(response.data.message);
                    setLoading(false);
                    setError(true);
                    const errorRedirectUrl = buildRedirectUrl(domain, 'site/login.html', '', '', redirect);
                    window.location.replace(errorRedirectUrl);
                }
            }
            const errorRedirectUrl = buildRedirectUrl(domain, 'site/login.html', '', '', redirect);
            window.location.replace(errorRedirectUrl);
        } catch (err) {
            setMessage(JSON.stringify(err));
            setLoading(false);
            setError(true);

            const errorRedirectUrl = buildRedirectUrl(domain, 'site/login.html', '', '', redirect);
            window.location.replace(errorRedirectUrl);
        }
    };

    useEffect(() => {
        loginFromSian(domainRedirect, path, user, token, redirectParam);
        return () => {
            setMessage('');
        };
    }, []);

    return (
        <Box
            sx={{
                backgroundImage: `url(${Background})`,
                position: 'absolute',
                backgroundPosition: 'bottom left',
                backgroundRepeat: 'no-repeat',
                backgroundSize: 'cover',
                overflow: 'hidden',
                width: '100%'
            }}
        >
            <Grid
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    width: '100%',
                    height: '100vh',
                    flexDirection: 'column',
                    gap: 2
                }}
                container
            >
                <Grid sx={{ width: '15rem' }} item>
                    <img className="logo-sian" src={sianLogo} alt="Hardtech" />
                </Grid>
                <Grid sx={{ textAlign: 'center', display: 'flex', flexDirection: 'column', gap: 2 }} item>
                    {loading && <Typography variant="h3">Iniciando sesión en SIAN 2</Typography>}
                    {!loading && (
                        <>
                            {error ? (
                                <Typography variant="h3">Error: No se puedo iniciar sesión {message}</Typography>
                            ) : (
                                <Typography variant="h3">{message}</Typography>
                            )}
                            <Typography variant="h4">Redireccionando...</Typography>
                        </>
                    )}
                </Grid>
                <Grid>
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignContent: 'center' }}>
                        <CircularProgress />
                    </Box>
                </Grid>
            </Grid>
        </Box>
    );
};

export default Index;
