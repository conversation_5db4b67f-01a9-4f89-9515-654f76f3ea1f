Stack trace:
Frame         Function      Args
0007FFFFA4E0  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA4E0, 0007FFFF93E0) msys-2.0.dll+0x1FE8E
0007FFFFA4E0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA7B8) msys-2.0.dll+0x67F9
0007FFFFA4E0  000210046832 (000210286019, 0007FFFFA398, 0007FFFFA4E0, 000000000000) msys-2.0.dll+0x6832
0007FFFFA4E0  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA4E0  000210068E24 (0007FFFFA4F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA7C0  00021006A225 (0007FFFFA4F0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD081A0000 ntdll.dll
7FFD06660000 KERNEL32.DLL
7FFD05870000 KERNELBASE.dll
7FFD07B30000 USER32.dll
7FFD052F0000 win32u.dll
7FFD07230000 GDI32.dll
7FFD053D0000 gdi32full.dll
7FFD05320000 msvcp_win.dll
7FFD05690000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD065A0000 advapi32.dll
7FFD06FE0000 msvcrt.dll
7FFD06D80000 sechost.dll
7FFD07D10000 RPCRT4.dll
7FFD04800000 CRYPTBASE.DLL
7FFD05EA0000 bcryptPrimitives.dll
7FFD068B0000 IMM32.DLL
