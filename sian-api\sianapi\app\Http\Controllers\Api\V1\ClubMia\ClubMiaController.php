<?php

namespace App\Http\Controllers\Api\V1\ClubMia;

use App\Http\Controllers\Controller;
use App\Http\Controllers\SianController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ClubMiaController extends Controller
{
    /**
     * Register a new Club Mia member
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        try {
            // Validate the request according to SIAN API requirements
            $validator = Validator::make($request->all(), [
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|string|email|max:255',
                'phone' => 'required|string|max:20',
                'address' => 'required|string|max:500',
                'birthDate' => 'required|date',
                'dni' => 'required|string|max:20',
                'ruc' => 'nullable|string|max:20',
                'addresses' => 'nullable|array',
                'phones' => 'nullable|array',
                'companyName' => 'nullable|string|max:255',
                'sian_domain' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Extract SIAN domain
            $sianDomain = $data['sian_domain'];

            // Remove SIAN domain from data to send
            unset($data['sian_domain']);

            Log::info('Club Mia Registration Request', [
                'email' => $data['email'],
                'firstName' => $data['firstName'],
                'lastName' => $data['lastName'],
                'dni' => $data['dni']
            ]);

            // Build the SIAN API URL for Club Mia registration
            $url = $sianDomain . '/apiSian/signin/clubMia/register';

            Log::info('SIAN Club Mia Registration URL', ['url' => $url]);

            // Send registration request to SIAN (no authentication required for Club Mia)
            $response = Http::withoutVerifying()->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($url, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('SIAN Club Mia Registration Success', [
                    'status' => $response->status(),
                    'response' => $responseData
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Club Mia member registered successfully',
                    'data' => $responseData,
                    'status_code' => $response->status()
                ]);
            } else {
                $errorData = $response->json();

                Log::error('SIAN Club Mia Registration Error', [
                    'status' => $response->status(),
                    'error' => $errorData,
                    'body' => $response->body()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to register Club Mia member in SIAN',
                    'error' => $errorData,
                    'status_code' => $response->status()
                ], $response->status());
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Club Mia Registration Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during Club Mia registration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login a Club Mia member
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        try {
            // Validate the request according to SIAN API requirements
            $validator = Validator::make($request->all(), [
                'dni' => 'required|string|max:20',
                'username' => 'required|string',
                'password' => 'required|string',
                'lifetime' => 'nullable|integer|min:1',
                'sian_domain' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Extract SIAN domain
            $sianDomain = $data['sian_domain'];

            // Remove SIAN domain from data to send
            unset($data['sian_domain']);

            Log::info('Club Mia Login Request', [
                'dni' => $data['dni'],
                'username' => $data['username']
            ]);

            // Build the SIAN API URL for Club Mia login
            $url = $sianDomain . '/apiSian/signin/clubMia/login';

            Log::info('SIAN Club Mia Login URL', ['url' => $url]);

            // Send login request to SIAN (no authentication required for Club Mia)
            $response = Http::withoutVerifying()->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($url, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('SIAN Club Mia Login Success', [
                    'status' => $response->status(),
                    'response' => $responseData
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Club Mia login successful',
                    'data' => $responseData,
                    'status_code' => $response->status()
                ]);
            } else {
                $errorData = $response->json();

                Log::error('SIAN Club Mia Login Error', [
                    'status' => $response->status(),
                    'error' => $errorData,
                    'body' => $response->body()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to login to Club Mia',
                    'error' => $errorData,
                    'status_code' => $response->status()
                ], $response->status());
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Club Mia Login Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during Club Mia login',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get member profile information
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function profile(Request $request)
    {
        try {
            // TODO: Implement authentication middleware and get authenticated member
            // For now, return simulated profile data
            
            return response()->json([
                'success' => true,
                'message' => 'Profile retrieved successfully',
                'data' => [
                    'member_id' => uniqid('clubmia_'),
                    'name' => 'Test Member',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567890',
                    'birth_date' => '1990-01-01',
                    'gender' => 'M',
                    'document_type' => 'DNI',
                    'document_number' => '12345678',
                    'address' => 'Test Address 123',
                    'status' => 'active',
                    'created_at' => now()->toISOString(),
                    'points' => 150,
                    'membership_level' => 'Bronze'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Club Mia Profile Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred retrieving profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
