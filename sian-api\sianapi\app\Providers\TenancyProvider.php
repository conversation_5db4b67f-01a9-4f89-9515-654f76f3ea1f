<?php

namespace App\Providers;

use App\Tenant;
use Illuminate\Queue\Events\JobProcessing;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Log;

class TenancyProvider extends ServiceProvider
{
    /**
     * Bootstrap services.
     *
     * @return void
     */
    public function boot()
    {
        $this->configureRequests();

        $this->configureQueue();
    }

    /**
     * Configure tenant-specific settings for requests.
     */
    public function configureRequests()
    {
        if (!$this->app->runningInConsole()) {
            $protocol = env('IS_HTTPS', false) ? 'https://' : 'http://';
            $host = $this->getSIANHost($this->app['request']);
            $addressUrl = $protocol . $host;

            if (config('app.globals.sian_origin') !== $addressUrl) {
                $oTenant = Tenant::whereDomain($host)->firstOrFail();
                config(['app.globals.sian_origin' => $addressUrl]);
                config(['app.globals.sian_url' => $oTenant->sian_domain]);
                $oTenant->configure()->use();
            }
        }
    }

    /**
     * Configure tenant-specific settings for queue jobs.
     */
    public function configureQueue()
    {
        $this->app['queue']->createPayloadUsing(function () {
            return $this->app['tenant'] ? ['tenant_id' => $this->app['tenant']->id] : [];
        });

        $this->app['events']->listen(JobProcessing::class, function ($event) {
            if (isset($event->job->payload()['tenant_id'])) {
                Tenant::find($event->job->payload()['tenant_id'])->configure()->use();
            }
        });
    }

    /**
     * Get the SIAN host from the request.
     *
     * @param \Illuminate\Http\Request $request
     * @return string
     */
    private function getSIANHost($request)
    {
        $originHeader = $request->headers->get('Origin');

        if ($originHeader) {
            $parsedUrl = parse_url($originHeader);
            $host = $parsedUrl['host'] ?? '';
            return $host;
        } else {
            $host = $request->getHost();
            return $host;
        }
    }
}
