import { useState, useCallback } from 'react';

export default function useModal(mode = false) {
    const [isOpen, setIsOpen] = useState(false);

    const handleOpen = useCallback(() => {
        setIsOpen(true);
    }, []);

    const handleClose = useCallback(() => {
        setIsOpen(false);
    }, []);

    const toggle = () => {
        setIsOpen(!isOpen);
    };

    if (!mode) {
        return [isOpen, handleOpen, handleClose, toggle];
    }
    return {
        isOpen,
        handleOpen,
        handleClose,
        toggle
    };
}

export function useModalRoute({ history }) {
    const [isOpen, setIsOpen] = useState(false);

    const handleOpen = useCallback(() => {
        setIsOpen(true);
    }, []);

    const handleClose = useCallback(() => {
        history.handleClose();
    }, []);

    const toggle = useCallback(() => {
        setIsOpen(!isOpen);
    }, []);

    return [isOpen, handleOpen, handleClose, toggle];
}
