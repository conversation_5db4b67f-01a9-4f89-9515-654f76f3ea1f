import { Box, Button, Chip, Fab, Typography, Menu, MenuItem } from '@mui/material';
import React, { useEffect, useState, useMemo, useCallback } from 'react';
import MainCard from 'ui-component/cards/MainCard';
import SyncIcon from '@mui/icons-material/Sync';
import { useDispatch, useSelector } from 'store';
import { useNavigate } from 'react-router-dom';
import { recipeRouteCreate, recipeRouteEdit, recipeRouteView } from 'routes/routes';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import RecipeFilter from 'ui-component/filters/RecipeFilter';
import { BlockLoader } from 'ui-component/loaders/loaders';
import { clearAnalysis, deleteRecipe, getRecipesWithPagination, setNewPage, setNewPageSize } from 'store/slices/recipe/recipe';
import useModal from 'hooks/useModal';
import RecipeCosts from './others/RecipeCosts';
import Grid from 'ui-component/grid/Grid';
import { parseStringtoDateWithHour } from 'utils/dates';
import IconButton from '@mui/material/IconButton';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Swal from 'sweetalert2';
import { RECIPE_TYPE_CART } from 'utils/recipeUtils';
import TablePagination from 'ui-component/pagination/TablePagination';
import InitialForm from './forms/InitialForm';
import DisplayCurrency from 'ui-component/display/DisplayCurrency';

const OptionsComponent = ({
    value,
    data,
    permissions,
    handleOpen,
    navigate,
    setIdSelected,/*  */
    handlerCreateModal,
    dispatch,
    deleteRecipe,
    reload
}) => {
    const index = data.findIndex((recipe) => recipe.recipeID === value);
    const [anchorEl, setAnchorEl] = useState(null);

    if (index >= 0) {
        const { recipeID, productName, isMerchandise, type, initialized } = data[index];

        const open = Boolean(anchorEl);
        const handleClick = (event) => {
            setAnchorEl(event.currentTarget);
        };

        const handleClose = () => {
            setAnchorEl(null);
            setIdSelected(null);
        };

        return (
            <>
                <IconButton
                    id="basic-button"
                    aria-controls={open ? 'basic-menu' : undefined}
                    aria-haspopup="true"
                    aria-expanded={open ? 'true' : undefined}
                    onClick={handleClick}
                >
                    <MoreVertIcon />
                </IconButton>

                <Menu
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                    MenuListProps={{
                        'aria-labelledby': 'basic-button'
                    }}
                >
                    {initialized === 1 ? (
                        <>
                            {permissions?.costs && isMerchandise === 1 ? (
                                <MenuItem
                                    onClick={() => {
                                        handleClose();
                                        handleOpen(recipeID, productName);
                                    }}
                                    sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 2 }}
                                >
                                    Estructura de Precios
                                </MenuItem>
                            ) : null}

                            {permissions?.view ? (
                                <MenuItem
                                    onClick={() => {
                                        handleClose();
                                        navigate(recipeRouteView(recipeID));
                                    }}
                                    sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 2 }}
                                >
                                    Ver
                                </MenuItem>
                            ) : null}
                        </>
                    ) : null}
                    {permissions?.update ? (
                        <MenuItem
                            onClick={() => {
                                handleClose();
                                setIdSelected(recipeID);
                                handlerCreateModal[1]();
                            }}
                            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 2 }}
                        >
                            Editar Datos Generales
                        </MenuItem>
                    ) : null}
                    {permissions?.update ? (
                        <MenuItem
                            onClick={() => {
                                handleClose();
                                navigate(recipeRouteEdit(recipeID));
                            }}
                            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 2 }}
                        >
                            Editar Insumos
                            {type === RECIPE_TYPE_CART ? ' y Costos' : null}
                        </MenuItem>
                    ) : null}

                    {permissions?.delete ? (
                        <MenuItem
                            onClick={() => {
                                handleClose();
                                Swal.fire({
                                    title: '¿Estás seguro?',
                                    text: '¡No podrás revertir esto!',
                                    icon: 'warning',
                                    showCancelButton: true,
                                    confirmButtonColor: '#3085d6',
                                    cancelButtonColor: '#d33',
                                    confirmButtonText: 'Sí, eliminarlo'
                                }).then((result) => {
                                    if (result.isConfirmed) {
                                        dispatch(deleteRecipe(recipeID)).then(() => reload());
                                    }
                                });
                            }}
                            sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 2 }}
                        >
                            Eliminar
                        </MenuItem>
                    ) : null}
                </Menu>
            </>
        );
    }
    return '-';
};

export default function Recipe({ permissions }) {
    const dispatch = useDispatch();
    const navigate = useNavigate();
    const { data, loading, page, pageSize, totalRecords, totalPages } = useSelector((state) => state.recipe);
    const [idSelected, setIdSelected] = useState(null);
    const [title, setTitle] = useState(null);
    const [isOpen, openModal, closeModal] = useModal();
    const handlerCreateModal = useModal();

    const handleClose = () => {
        closeModal();
        setIdSelected(null);
        dispatch(clearAnalysis());
    };

    const handleOpen = (id, name) => {
        setIdSelected(id);
        setTitle(`Costos de ${name}`);
        openModal();
    };

    const [filters, setFilters] = useState({});

    const reload = useCallback(() => {
        if (permissions) {
            dispatch(getRecipesWithPagination(pageSize, page, filters));
        }
    }, [permissions, dispatch, pageSize, page, filters]);

    const search = useCallback(() => {
        if (permissions) {
            dispatch(setNewPage(1));
            dispatch(getRecipesWithPagination(pageSize, 1, filters));
        }
    }, [permissions, dispatch, pageSize, filters]);



    const columns = [
        {
            label: 'ID',
            name: 'recipeID',
            options: {
                filter: false,
                display: false
            }
        },
        {
            label: 'ID PRODUCTO',
            name: 'productID',
            options: {
                filter: true,
                display: true
            }
        },
        {
            label: 'PRODUCTO',
            name: 'productName',
            options: {
                filter: true,
                display: true
            }
        },
        {
            label: 'TIPO RECETA',
            name: 'type',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) =>
                    value === RECIPE_TYPE_CART ? <Chip label="CARTA" color="success" /> : <Chip label="INSUMO" color="warning" />
            }
        },
        {
            label: 'LINEA',
            name: 'lineName',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) => <Chip label={value} color="primary" variant="outlined" />
            }
        },
        {
            label: 'SUBLINEA',
            name: 'sublineName',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) => <Chip label={value} color="success" variant="outlined" />
            }
        },
        {
            label: 'PRECIO',
            name: 'price',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) => <DisplayCurrency value={value} />
            }
        },
        {
            label: 'USUARIO',
            name: 'createUserName',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'F.CREACIÓN',
            name: 'createdAt',
            options: {
                filter: true,
                display: false,
                customBodyRender: (value) => parseStringtoDateWithHour(value)
            }
        },
        {
            label: 'USUARIO ACTU',
            name: 'createUserName',
            options: {
                filter: true,
                display: false
            }
        },
        {
            label: 'F.ULT ACTUALIZACION',
            name: 'createdAt',
            options: {
                filter: true,
                display: true,
                customBodyRender: (value) => (value ? parseStringtoDateWithHour(value) : 'No Definido')
            }
        },
        {
            label: 'ACCIONES',
            name: 'recipeID',
            options: {
                filter: false,
                sort: false,
                empty: true,
                display: true,
                customBodyRender: (id) => (
                    <OptionsComponent
                        value={id}
                        data={data}
                        permissions={permissions}
                        handleOpen={handleOpen}
                        navigate={navigate}
                        setIdSelected={setIdSelected}
                        handlerCreateModal={handlerCreateModal}
                        dispatch={dispatch}
                        deleteRecipe={deleteRecipe}
                        reload={reload}
                    />
                )
            }
        }
    ];




    const options = useMemo(() => ({
        customFooter: () => (
            <TablePagination
                page={page}
                pageSize={pageSize}
                dataLength={data.length}
                totalRecords={totalRecords}
                totalPages={totalPages}
                setPage={(value) => dispatch(setNewPage(value))}
                setPageSize={(value) => dispatch(setNewPageSize(value))}
            />
        )
    }), [page, pageSize, data.length, totalRecords, totalPages, dispatch]);

    useEffect(() => {
        if (data.length === 0) {
            reload();
        }
    }, [dispatch]);

    useEffect(() => {
        reload();
    }, [pageSize, page]);

    const FloatingButtons = ({ permissions }) => (
        <Box sx={{ position: 'fixed', bottom: 16, right: 16, display: { lg: 'none', xs: 'flex' }, gap: 1 }}>
            <Fab variant="extended" color="success" onClick={reload} disabled={!permissions}>
                <SyncIcon sx={{ mr: 1 }} />
                Actualizar
            </Fab>
            <Fab variant="extended" color="warning" onClick={() => navigate(recipeRouteCreate)} disabled={!permissions.create}>
                <RestaurantIcon sx={{ mr: 1 }} />
                Crear Receta
            </Fab>
        </Box>
    );

    return (
        <>
            {isOpen ? <RecipeCosts isOpen={isOpen} handleClose={handleClose} title={title} maxWidth="xl" productID={idSelected} /> : null}
            <FloatingButtons permissions={permissions} />
            <MainCard sx={{ px: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', pb: '3rem' }}>
                    <Typography variant="h1">Recetas</Typography>
                    <Box sx={{ display: { xs: 'none', lg: 'flex' }, gap: '1rem' }}>
                        <Button variant="contained" color="success" startIcon={<SyncIcon />} onClick={reload} disabled={!permissions}>
                            Actualizar
                        </Button>
                        <InitialForm
                            permissions={permissions}
                            reload={reload}
                            handlerModal={handlerCreateModal}
                            productID={idSelected}
                            clearIDSelected={() => setIdSelected(null)}
                        />
                    </Box>
                </Box>
                <RecipeFilter setFilters={setFilters} handleSearch={search} disabled={!permissions} />
                <BlockLoader loading={loading}>
                    {data.length < 1 ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', my: 2 }}>
                            <Typography variant="h4">¡No hay recetas registradas!</Typography>
                        </Box>
                    ) : (
                        <Grid data={data} columns={columns} options={options} />
                    )}
                </BlockLoader>
            </MainCard>
        </>
    );
}
