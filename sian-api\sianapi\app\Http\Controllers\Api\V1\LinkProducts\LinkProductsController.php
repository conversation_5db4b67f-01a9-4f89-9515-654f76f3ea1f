<?php

namespace App\Http\Controllers\Api\V1\LinkProducts;


use App\Http\Controllers\SianController;
use App\Models\Auth\LoginPivot;
use App\Models\Fake\Currency;
use App\Models\Procedures\SpGetProductPresentations;
use App\Models\Procedures\SpGetWarehouseItems;
use App\Models\ProductLink;
use App\Tenant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class LinkProductsController extends Controller {

    const MODE_WITH_LINKS = 1;
    const MODE_WITHOUT_LINKS = 0;
    /**
     *
     *
     * @return \Illuminate\Http\Response
     */

    public function index() {

        try {
            $data = $this->getProducts(self::MODE_WITH_LINKS)->get();

            return response()->json($data);
        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }


    public function getParentItems(Request $request) {

        try {
            $query = $this->getProducts(self::MODE_WITHOUT_LINKS);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('P.product_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('product')) {
                $product = $request->input('product');
                $arrayProduct = explode(",", $product);
                $query->whereNotIn("P.product_id", $arrayProduct);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(50);
            }

            $data = $query->get();

            return response()->json($data);

        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }

    public function getChildrenItems(Request $request) {

        try {
            $query = $this->getProducts(self::MODE_WITHOUT_LINKS);

            if ($request->has('keyword')) {
                $keyword = $request->input('keyword');
                $query->where('P.product_name', 'like', '%' . $keyword . '%');
            }

            if ($request->has('product')) {
                $product = $request->input('product');
                $arrayProduct = explode(",", $product);
                $query->whereNotIn("P.product_id", $arrayProduct);
            }

            if ($request->has('limit')) {
                $limit = $request->input('limit');
                $query->limit($limit);
            } else {
                $query->limit(50);
            }

            $data = $query->get();

            return response()->json($data);

        } catch (\Throwable $th) {
            return response()->json([
                'success' => false,
                'message' => $th->getMessage()
            ]);
        }
    }


    private function getProducts($queryMode) {
        $query = DB::table('merchandise as ME')
            ->select([
                'ME.product_id as product_id',
                'P.product_name as product_name',
                'ME.model as model',
                'ME.subline_id as subline_id',
                'ME.serialized as serialized',
                'P.status as status',
                'P.is_supply as is_supply',
                'PD.equivalence',
                'PD.measure_name',
                'PM.equivalence as unit_equivalence',
                'PM.measure_name as unit_measure_name',
                DB::raw('(
                SELECT COUNT(*)
                FROM product_link PL
                LEFT JOIN recipe LRE ON LRE.product_id = PL.product_parent_id
                WHERE PL.product_id = ME.product_id
                AND LRE.recipe_id IS NULL
            ) as children_quantity')
            ])
            ->join('product as P', 'P.product_id', '=', 'ME.product_id')
            ->join('presentation as PM', function ($join) {
                $join->on('PM.product_id', '=', 'ME.product_id')
                    ->where('PM.equivalence', '=', 1);
            })
            ->join('presentation as PD', function ($join) {
                $join->on('PD.product_id', '=', 'ME.product_id')
                    ->where('PD.default', '=', 1);
            })
            ->leftJoin('recipe as RE', 'RE.product_id', '=', 'ME.product_id')
            ->whereNull('RE.recipe_id')
            ->where('P.is_supply', '=', 1)
            ->where('P.status', "=", 1)
            ->groupBy(
                'ME.product_id',
                'P.product_name',
                'ME.model',
                'ME.subline_id',
                'ME.serialized',
                'P.status',
                'P.is_supply',
                'PD.equivalence',
                'PD.measure_name',
                'PM.equivalence',
                'PM.measure_name'
            );

        if ($queryMode === self::MODE_WITH_LINKS) {
            $query->havingRaw('children_quantity > 0');
        }

        $query->orderByDesc('product_id');

        return $query;
    }

    public function getDocumentData($movement_id) {
        $warehouse_movement = $this->getDocumentDetailQuery($movement_id)->first();
        if (!$warehouse_movement) {
            return response()->json([
                'success' => false,
                'error' => 'El movimiento no Existe o no cumple con los requisitos'
            ]);
        }

        $items = SpGetWarehouseItems::execute($movement_id);
        if (!$items || count($items) < 1) {
            return response()->json([
                'success' => false,
                'error' => 'El movimiento no cuenta con items transformables'
            ]);
        }

        $product_ids = [];

        foreach ($items as $item) {
            $product_id = $item->product_id;

            if (!in_array($product_id, $product_ids)) {
                $product_ids[] = $product_id;
            }

            $transformationResults = $this->getTransformationResultsByID($product_id);
            $item->transformationResults = $transformationResults;

            if ($transformationResults && count($transformationResults) > 0) {
                foreach ($transformationResults as $product) {
                    if (!in_array($product->product_id, $product_ids)) {
                        $product_ids[] = $product->product_id;
                    }
                }
            }
        }

        $recipePresentations = SpGetProductPresentations::getAssociative(
            SpGetProductPresentations::MODE_COMBOBOX_WITHOUT_PRICES,
            $product_ids,
            Currency::PEN,
            0
        );

        foreach ($items as &$principal_items) {
            $transformationResults = $principal_items->transformationResults;
            if ($transformationResults && count($transformationResults) > 0) {
                foreach ($transformationResults as &$product) {
                    if (isset($recipePresentations[$product->product_id])) {
                        $product->presentations = $recipePresentations[$product->product_id];
                        $product->presentation = $recipePresentations[$product->product_id][$product->equivalence];
                    }
                }
            }
        }

        return response()->json([
            'success' => true,
            'data' => [
                'warehouse_movement' => $warehouse_movement,
                'items' => $items
            ]
        ], 200);
    }

    private function getDocumentDetailQuery($movement_id) {
        return DB::table('movement as M')
            ->select([
                'M.movement_id',
                'M.document_code',
                'M.business_unit_id',
                'B.business_unit_name',
                DB::raw("CONCAT_WS('-', M.document_code, M.document_serie, M.document_correlative) as document"),
                'M.document_serie',
                'M.document_correlative',
                'M.route',
                'M.emission_date',
                'M.status',
                'M.person_id',
                'M.aux_person_id',
                'XP.identification_number',
                DB::raw("REPLACE(XP.person_name, ',', ' ') as aux_person_name"),
                DB::raw("REPLACE(P.person_name, ',', ' ') as person_name"),
                'WM.warehouse_id',
                'W.warehouse_name',
                'ML.movement_parent_id',
                'FC.route as movement_parent_route',
                DB::raw("CONCAT_WS('-', FC.document_code, FC.document_serie, FC.document_correlative) as movement_parent"),
                'M.store_id',
                'ST.store_name',
                DB::raw('M.status AND PA.is_creditable as is_creditable'),
                'S.title'
            ])
            ->join('ability as A', 'A.movement_id', '=', 'M.movement_id')
            ->join('business_unit as B', 'B.business_unit_id', '=', 'M.business_unit_id')
            ->join('scenario as S', 'S.route', '=', 'M.route')
            ->join('store as ST', 'ST.store_id', '=', 'M.store_id')
            ->join('person as XP', 'XP.person_id', '=', 'M.aux_person_id')
            ->join('person as P', 'P.person_id', '=', 'M.person_id')
            ->join('warehouse_movement as WM', 'WM.movement_id', '=', 'M.movement_id')
            ->join('warehouse as W', 'W.warehouse_id', '=', 'WM.warehouse_id')
            ->join('movement_link as ML', function ($join) {
                $join->on('ML.movement_id', '=', 'M.movement_id')
                    ->where('ML.default_ability', '=', 'is_dispatchable');
            })
            ->join('movement as FC', 'FC.movement_id', '=', 'ML.movement_parent_id')
            ->join('ability as PA', 'PA.movement_id', '=', 'FC.movement_id')
            ->whereIn('M.route', ['warehouse/buyIn', 'warehouse/transferenceIn'])
            ->where('M.movement_id', '=', $movement_id);
    }

    private function getTransformationResultsByID($product_id) {
        return DB::table('product_link as PL')
            ->select([
                'P.product_id',
                'P.product_name',
                'PRES.equivalence',
                'PRES.measure_name'
            ])
            ->join('product as P', 'P.product_id', '=', 'PL.product_parent_id')
            ->join('presentation as PRES', function ($join) {
                $join->on('PRES.product_id', '=', 'P.product_id')
                    ->where('PRES.default', '=', 1);
            })
            ->leftJoin('recipe as RE', 'RE.product_id', '=', 'PL.product_parent_id')
            ->whereNull('RE.recipe_id')
            ->where('PL.product_id', $product_id)
            ->get();
    }


    public function show($productID) {
        try {
            $data = $this->getChildrensByProductID($productID);
            return response()->json($data);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }


    public function store(Request $request) {
        try {

            $deleteLinks = $request->input('deleteLinks');
            $newLinks = $request->input('newLinks');

            if (!isset($deleteLinks) && !isset($newLinks)) {
                return response()->json([
                    'success' => false,
                    'error' => "No se han envidado cmbios"
                ], 400);
            }


            DB::beginTransaction();

            foreach ($newLinks as $newLink) {
                if (isset($newLink['product_parent_id'], $newLink['product_id'])) {
                    $productLink = new ProductLink();
                    $productLink->product_parent_id = $newLink['product_parent_id'];
                    $productLink->product_id = $newLink['product_id'];
                    $productLink->quantity = 1;
                    $productLink->equivalence = 1;
                    $productLink->unit_cost = 0;
                    $productLink->waste_percentage = 0;

                    $productLink->save();
                }
            }

            foreach ($deleteLinks as $deleteLink) {
                if (isset($deleteLink['product_parent_id'], $deleteLink['product_id'])) {
                    ProductLink::where('product_parent_id', $deleteLink['product_parent_id'])
                        ->where('product_id', $deleteLink['product_id'])
                        ->delete();
                }
            }

            DB::commit();

            $data = $this->getChildrensByProductID($request->input('product_id'));

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $ex) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }


    public function getChildrensByProductID($productID) {
        return DB::table('product_link as PL')
            ->select([
                'PL.product_link_id',
                'P.product_id',
                'P.product_name',
                'PRES.equivalence',
                'PRES.measure_name',
                'UPRES.equivalence as unit_equivalence',
                'UPRES.measure_name as unit_measure_name'
            ])
            ->join('product as P', 'P.product_id', '=', 'PL.product_parent_id')
            ->join('presentation as PRES', function ($join) {
                $join->on('PRES.product_id', '=', 'P.product_id')
                    ->where('PRES.default', '=', 1);
            })
            ->join('presentation as UPRES', function ($join) {
                $join->on('UPRES.product_id', '=', 'P.product_id')
                    ->where('UPRES.equivalence', '=', 1);
            })
            ->leftJoin('recipe as RE', 'RE.product_id', '=', 'PL.product_parent_id')
            ->whereNull('RE.recipe_id')
            ->where('PL.product_id', '=', $productID)
            ->get();

    }



}
