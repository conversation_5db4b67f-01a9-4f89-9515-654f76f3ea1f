<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountingFile
 * 
 * @property int $accounting_file_id
 * @property string $accounting_file_code
 * @property string $accounting_file_name
 * @property string $exchange_rate
 * @property bool $autochecked
 * @property bool $status
 * 
 * @property Collection|AccountingYear[] $accounting_years
 * @property Collection|Document[] $documents
 * @property Collection|Operation[] $operations
 * @property Collection|Scenario[] $scenarios
 *
 * @package App\Models
 */
class AccountingFile extends Model
{
	protected $table = 'accounting_file';
	protected $primaryKey = 'accounting_file_id';
	public $timestamps = false;

	protected $casts = [
		'autochecked' => 'bool',
		'status' => 'bool'
	];

	protected $fillable = [
		'accounting_file_code',
		'accounting_file_name',
		'exchange_rate',
		'autochecked',
		'status'
	];

	public function accounting_years()
	{
		return $this->hasMany(AccountingYear::class);
	}

	public function documents()
	{
		return $this->hasMany(Document::class);
	}

	public function operations()
	{
		return $this->hasMany(Operation::class);
	}

	public function scenarios()
	{
		return $this->hasMany(Scenario::class);
	}
}
