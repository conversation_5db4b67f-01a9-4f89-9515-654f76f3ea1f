import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import PerfectScrollbar from 'react-perfect-scrollbar';
import MainCard from 'ui-component/cards/MainCard';
import { getApprovalsByType } from 'services/administrationService';
import {
    Chip,
    Table,
    TableBody,
    TableCell as MaterialTableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
    Skeleton,
    Tooltip,
    Box,
    Menu,
    MenuItem,
    Checkbox,
    ListItemText,
    Fab
} from '@mui/material';
import { parseDateToApi, parseStringDateToDateTime, parseStringDateToDate } from 'utils/dates';
import useLoading from 'hooks/useLoading';
import SIANLink from 'ui-component/SIAN/SIANLink';
import DisplayCurrency from 'ui-component/display/DisplayCurrency';
import { colorStateData, listTypeRequestData } from 'models/Confirmation';
import { BlockLoader } from 'ui-component/loaders/loaders';
import { loadUserData } from 'views/commercial/salesDashboard/components/stores/functions';
import ViewColumnIcon from '@mui/icons-material/ViewColumn';

const COLUMN_CONFIG = [
    { key: 'group', label: 'Grupo', visible: true, hiddenByDefault: true },
    { key: 'document', label: 'Documento', visible: true, hiddenByDefault: false },
    { key: 'requestType', label: 'T. Solicitud', visible: true, hiddenByDefault: true },
    { key: 'observations', label: 'Observaciones', visible: true, hiddenByDefault: true },
    { key: 'project', label: 'Proyecto', visible: true, hiddenByDefault: false },
    { key: 'requester', label: 'Solicitante', visible: true, hiddenByDefault: true },
    { key: 'approvedBy', label: 'Aprobado por', visible: true, hiddenByDefault: false },
    { key: 'requestDate', label: 'F. Solicitud', visible: true, hiddenByDefault: false },
    { key: 'requirementDate', label: 'F. Entrega', visible: true, hiddenByDefault: false },
    { key: 'approvalDate', label: 'F. Aprobacion', visible: true, hiddenByDefault: true },
    { key: 'total', label: 'Total', visible: true, hiddenByDefault: false }
];

const ColumnVisibilityControl = ({ columns, onColumnToggle }) => {
    const [anchorEl, setAnchorEl] = useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleToggle = (columnKey) => {
        onColumnToggle(columnKey);
    };

    return (
        <Box>
            <Fab
                onClick={handleClick}
                size="small"
                color="info"
                aria-label="column-settings"
                sx={{
                    backgroundColor: 'background.paper',
                    boxShadow: 1,
                    '&:hover': {
                        backgroundColor: 'action.hover'
                    }
                }}
            >
                <ViewColumnIcon fontSize="small" color="primary" />
            </Fab>
            <Menu
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                PaperProps={{
                    sx: { minWidth: 200 }
                }}
            >
                {columns.map((column) => (
                    <MenuItem key={column.key} onClick={() => handleToggle(column.key)}>
                        <Checkbox checked={column.visible} size="small" sx={{ mr: 1 }} />
                        <ListItemText primary={column.label} sx={{ fontSize: '0.875rem' }} />
                    </MenuItem>
                ))}
            </Menu>
        </Box>
    );
};

const SkeletonList = ({ rows = 12, columns, isColumnVisible }) => (
    <>
        {Array.from({ length: rows }).map((_, i) => (
            <TableRow key={i} hover>
                {isColumnVisible('group') && (
                    <TableCell sx={{ pl: 2 }}>
                        <Skeleton variant="text" width="80%" />
                    </TableCell>
                )}
                {isColumnVisible('document') && (
                    <TableCell>
                        <Skeleton variant="text" width="60%" />
                    </TableCell>
                )}
                {isColumnVisible('requestType') && (
                    <TableCell>
                        <Skeleton variant="rounded" width={80} height={24} />
                    </TableCell>
                )}
                {isColumnVisible('observations') && (
                    <TableCell>
                        <Skeleton variant="text" width="90%" />
                    </TableCell>
                )}
                {isColumnVisible('project') && (
                    <TableCell>
                        <Skeleton variant="text" width="85%" />
                    </TableCell>
                )}
                {isColumnVisible('requester') && (
                    <TableCell>
                        <Skeleton variant="text" width="75%" />
                    </TableCell>
                )}
                {isColumnVisible('approvedBy') && (
                    <TableCell>
                        <Skeleton variant="text" width="75%" />
                    </TableCell>
                )}
                {isColumnVisible('requestDate') && (
                    <TableCell>
                        <Skeleton variant="text" width="70%" />
                    </TableCell>
                )}
                {isColumnVisible('requirementDate') && (
                    <TableCell>
                        <Skeleton variant="text" width="70%" />
                    </TableCell>
                )}
                {isColumnVisible('approvalDate') && (
                    <TableCell>
                        <Skeleton variant="text" width="70%" />
                    </TableCell>
                )}
                {isColumnVisible('total') && (
                    <TableCell align="right">
                        <Skeleton variant="text" width={80} />
                    </TableCell>
                )}
            </TableRow>
        ))}
    </>
);

const HeadTableCell = ({ children, sx, ...props }) => (
    <MaterialTableCell sx={sx} {...props}>
        <Typography sx={{ fontSize: '0.8rem' }}>
            <strong>{children}</strong>
        </Typography>
    </MaterialTableCell>
);

const TableCell = ({ children, textCenter = null, showTooltip = null, maxTextLength = 50, ...props }) => {
    const isTextLong = typeof children === 'string' && children.length > maxTextLength;
    const displayText = isTextLong ? `${children.slice(0, maxTextLength)}...` : children;

    const content = (
        <Typography
            sx={{
                fontSize: '0.75rem',
                textAlign: textCenter ? 'center' : 'start',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                maxWidth: '200px',
                display: 'inline-block',
                cursor: 'pointer'
            }}
        >
            {displayText}
        </Typography>
    );

    return (
        <MaterialTableCell {...props}>
            {showTooltip ? (
                <Tooltip title={children} arrow placement="top">
                    {content}
                </Tooltip>
            ) : (
                content
            )}
        </MaterialTableCell>
    );
};

const getApprovals = ({ filters, startLoading, endLoading, setState }) => {
    const { dateRange, selectedGroups, ...otherFilters } = { ...filters };
    const dataToApi = {
        start_date: parseDateToApi(dateRange[0]),
        end_date: parseDateToApi(dateRange[1]),
        groups: Object.keys(selectedGroups).map((key) => key),
        ...otherFilters
    };
    startLoading();
    getApprovalsByType(dataToApi)
        .then((response) => {
            if (response.success) {
                setState(response.data);
            }
        })
        .catch((ex) => console.error(ex))
        .finally(() => endLoading());
};

export default function ApprovalTableCard({ title, filters }) {
    const [approvals, setApprovals] = useState([]);
    const [loading, startLoading, endLoading] = useLoading(true);
    const [currentPersonID] = useState(loadUserData().person.id);

    const [columns, setColumns] = useState(() =>
        COLUMN_CONFIG.map((col) => ({
            ...col,
            visible: !col.hiddenByDefault
        }))
    );

    const handleColumnToggle = (columnKey) => {
        setColumns((prev) => prev.map((col) => (col.key === columnKey ? { ...col, visible: !col.visible } : col)));
    };

    const isColumnVisible = (columnKey) => {
        const column = columns.find((col) => col.key === columnKey);
        return column ? column.visible : true;
    };

    useEffect(() => {
        getApprovals({ filters, endLoading, startLoading, setState: setApprovals });
    }, [filters]);

    const tableContent = (
        <PerfectScrollbar
            style={{
                height: '35.6rem',
                minHeight: '35.6rem'
            }}
        >
            <TableContainer
                sx={{
                    minWidth: { xs: 300, sm: 600, md: 800 },
                    overflowX: 'auto'
                }}
            >
                <Table
                    size="small"
                    stickyHeader
                    sx={{
                        '& .MuiTableCell-root': {
                            fontSize: { xs: '0.7rem', sm: '0.75rem', md: '0.8rem' },
                            padding: { xs: '4px 8px', sm: '6px 12px', md: '8px 16px' }
                        }
                    }}
                >
                    <TableHead>
                        <TableRow>
                            {isColumnVisible('group') && <HeadTableCell sx={{ pl: 3 }}>Grupo</HeadTableCell>}
                            {isColumnVisible('document') && <HeadTableCell>Documento</HeadTableCell>}
                            {isColumnVisible('requestType') && <HeadTableCell>T. Solicitud</HeadTableCell>}
                            {isColumnVisible('observations') && <HeadTableCell>Observaciones</HeadTableCell>}
                            {isColumnVisible('project') && <HeadTableCell sx={{ minWidth: '180px' }}>Proyecto</HeadTableCell>}
                            {isColumnVisible('requester') && <HeadTableCell sx={{ minWidth: '180px' }}>Solicitante</HeadTableCell>}
                            {isColumnVisible('approvedBy') && <HeadTableCell sx={{ minWidth: '180px' }}>Aprobado por</HeadTableCell>}
                            {isColumnVisible('requestDate') && <HeadTableCell>F. Solicitud</HeadTableCell>}
                            {isColumnVisible('requirementDate') && <HeadTableCell>F. Entrega</HeadTableCell>}
                            {isColumnVisible('approvalDate') && <HeadTableCell>F. Aprobacion</HeadTableCell>}
                            {isColumnVisible('total') && <HeadTableCell>Total</HeadTableCell>}
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        <BlockLoader
                            loading={loading}
                            loaderComponent={<SkeletonList columns={columns} isColumnVisible={isColumnVisible} />}
                        >
                            {approvals.length === 0 && !loading ? (
                                <TableRow>
                                    <TableCell colSpan={columns.filter((col) => col.visible).length} sx={{ textAlign: 'center', py: 4 }}>
                                        <Typography variant="body2" color="text.secondary">
                                            No hay datos disponibles
                                        </Typography>
                                    </TableCell>
                                </TableRow>
                            ) : (
                                approvals.map((approval, index) => (
                                    <TableRow hover key={index}>
                                        {isColumnVisible('group') && (
                                            <TableCell sx={{ pl: 2 }}>
                                                <strong>{approval.user_group_name}</strong>
                                            </TableCell>
                                        )}
                                        {isColumnVisible('document') && (
                                            <TableCell>
                                                <SIANLink id={approval.movement_id} route={approval.route}>
                                                    {approval.document}
                                                </SIANLink>
                                            </TableCell>
                                        )}
                                        {isColumnVisible('requestType') && (
                                            <TableCell>
                                                <Chip
                                                    label={listTypeRequestData[approval.option_type]}
                                                    color={colorStateData[approval.option_type]}
                                                    size="small"
                                                />
                                            </TableCell>
                                        )}
                                        {isColumnVisible('observations') && <TableCell showTooltip>{approval.observation}</TableCell>}
                                        {isColumnVisible('project') && <TableCell showTooltip>{approval.project_name}</TableCell>}
                                        {isColumnVisible('requester') && (
                                            <TableCell showTooltip>
                                                {approval.person_request_id === currentPersonID ? (
                                                    <Chip label={approval.user_request} color="secondary" size="small" />
                                                ) : (
                                                    approval.user_request
                                                )}
                                            </TableCell>
                                        )}
                                        {isColumnVisible('approvedBy') && (
                                            <TableCell showTooltip>
                                                {approval.person_id === currentPersonID ? (
                                                    <Chip label={approval.user_name} color="info" size="small" />
                                                ) : (
                                                    approval.user_name
                                                )}
                                            </TableCell>
                                        )}
                                        {isColumnVisible('requestDate') && (
                                            <TableCell>
                                                {approval.request_date
                                                    ? parseStringDateToDateTime(approval.request_date).toLocaleString('es-PE', {
                                                          timeZone: 'America/Lima',
                                                          day: 'numeric',
                                                          month: 'numeric',
                                                          year: 'numeric'
                                                      })
                                                    : '-'}
                                            </TableCell>
                                        )}
                                        {isColumnVisible('requirementDate') && (
                                            <TableCell>
                                                {approval.requirement_date
                                                    ? parseStringDateToDate(approval.requirement_date).toLocaleDateString('es-PE', {
                                                          day: 'numeric',
                                                          month: 'numeric',
                                                          year: 'numeric'
                                                      })
                                                    : '-'}
                                            </TableCell>
                                        )}
                                        {isColumnVisible('approvalDate') && (
                                            <TableCell>
                                                {parseStringDateToDateTime(approval.confirm_date).toLocaleString('es-PE', {
                                                    timeZone: 'America/Lima',
                                                    day: 'numeric',
                                                    month: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit',
                                                    hour12: true
                                                })}
                                            </TableCell>
                                        )}
                                        {isColumnVisible('total') && (
                                            <TableCell align="right" sx={{ pr: -4 }}>
                                                {approval[`total_${approval.currency}`] > 0 ? (
                                                    <DisplayCurrency
                                                        currency={approval.currency}
                                                        value={approval[`total_${approval.currency}`]}
                                                    />
                                                ) : null}
                                            </TableCell>
                                        )}
                                    </TableRow>
                                ))
                            )}
                            {!loading &&
                                approvals.length > 0 &&
                                approvals.length < 15 &&
                                Array.from({ length: 15 - approvals.length }).map((_, index) => (
                                    <TableRow key={`empty-${index}`} sx={{ height: '53px' }}>
                                        {columns
                                            .filter((col) => col.visible)
                                            .map((col) => (
                                                <TableCell key={col.key} sx={{ border: 'none', padding: 0 }} />
                                            ))}
                                    </TableRow>
                                ))}
                        </BlockLoader>
                    </TableBody>
                </Table>
            </TableContainer>
        </PerfectScrollbar>
    );

    if (!title) {
        return (
            <Box
                sx={{
                    height: '32.6rem',
                    minHeight: '32.6rem',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative'
                }}
            >
                <Box
                    sx={{
                        position: 'absolute',
                        top: 8,
                        right: 8,
                        zIndex: 10
                    }}
                >
                    <ColumnVisibilityControl columns={columns} onColumnToggle={handleColumnToggle} />
                </Box>

                {tableContent}
            </Box>
        );
    }

    return (
        <MainCard
            title={title}
            content={false}
            secondary={<ColumnVisibilityControl columns={columns} onColumnToggle={handleColumnToggle} />}
        >
            {tableContent}
        </MainCard>
    );
}
