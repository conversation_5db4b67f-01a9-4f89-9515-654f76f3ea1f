export const getHeatMapColor = (value, total) => {
    const percentage = (value / total) * 100;
    if (percentage >= 8) return '#b3256e'; // 100% opacidad - >8%
    if (percentage >= 6) return '#b3256ecc'; // 80% opacidad  - 6-8%
    if (percentage >= 4) return '#b3256e99'; // 60% opacidad  - 4-6%
    if (percentage >= 2) return '#b3256e66'; // 40% opacidad  - 2-4%
    return '#b3256e33'; // 20% opacidad  - <2%
};

export const getHeatMapColors = () => [
    '#b3256e33', // 20% opacidad - menos de 2%
    '#b3256e66', // 40% opacidad - 2-4%
    '#b3256e99', // 60% opacidad - 4-6%
    '#b3256ecc', // 80% opacidad - 6-8%
    '#b3256e' // 100% opacidad - más de 8%
];

export function getSingleKey(obj) {
    const [key] = Object.keys(obj);
    return key;
}

export const loadUserData = () => {
    const userSaved = window.localStorage.getItem('user');
    const user = JSON.parse(userSaved);
    // setUserProfile(user);
    return user;
};

export const compressString = (text, maxLength = 30) => {
    const isTextLong = typeof text === 'string' && text.length > maxLength;
    return isTextLong ? `${text.slice(0, maxLength)}...` : text;
};

export const getRandomColor = () => {
    const letters = '0123456789ABCDEF';
    let color = '#';
    for (let i = 0; i < 6; i++) {
        color += letters[Math.floor(Math.random() * 16)];
    }
    return color;
};
