import { lazy } from 'react';

// project imports
import MainLayout from 'layout/MainLayout';
import Loadable from 'ui-component/Loadable';
import AuthGuard from 'utils/route-guard/AuthGuard';
import Permisions from 'ui-component/Permisions';
import {
    analyticsRoute,
    dashboardRoute,
    foodSalesRoute,
    linkProductsRoute,
    logisticRoute,
    paymentScheduleRoute,
    paymentScheduleRouteCreate,
    productionCostRoute,
    recipeRoute,
    recipeRouteCreate,
    repositionRoute,
    restaurantRoute,
    salesDashboardRoute,
    supplyStocksReportRoute,
    supplyTransformationRoute,
    supplyTransformationRouteCreate,
    transactionRoute,
    transactionRouteCreate,
    warehouseRoute,
    salesDetailsRoute,
    approvalsDashboardRoute
} from './routes';

// Main Modules
const MainModules = Loadable(lazy(() => import('views/pages/modules/index')));

// module: Activos Fijos
const AssetClassification = Loadable(lazy(() => import('views/asset/classification/AssetClassification')));
const AssetFixedAssets = Loadable(lazy(() => import('views/asset/fixed-assets/FixedAssets')));
const AssetDepreciationGroup = Loadable(lazy(() => import('views/asset/depreciation-group/DepreciationGroup')));
const AssetFixedAssetMovement = Loadable(lazy(() => import('views/asset/classification/fixed-asset-movement/FixedAssetMovement')));

const AssetProcess = Loadable(lazy(() => import('views/asset/process/AssetProcess')));
const AssetProcessDepreciation = Loadable(lazy(() => import('views/asset/process/depreciation')));
const AssetProcessDepreciationTable = Loadable(lazy(() => import('views/asset/process/depreciation/form/EnhancedTable')));

// Procedures
const DocumentaryProcedure = Loadable(lazy(() => import('views/procedures/documentary-procedures')));

// module: Financial
const Financial = Loadable(lazy(() => import('views/financial/Financial')));
const PaymentSchedule = Loadable(lazy(() => import('views/financial/paymentSchedule/PaymentSchedule')));
const PaymentScheduleCreate = Loadable(lazy(() => import('views/financial/paymentSchedule/PaymentScheduleCreate')));
const PaymentScheduleController = Loadable(lazy(() => import('views/financial/paymentSchedule/forms/PaymentScheduleController')));

const Transactions = Loadable(lazy(() => import('views/financial/transaction/Transactions')));
const TransactionsCreate = Loadable(lazy(() => import('views/financial/transaction/TransactionsCreate')));

const Restaurant = Loadable(lazy(() => import('views/restaurant/Restaurant')));
const FoodSales = Loadable(lazy(() => import('views/restaurant/foodSales/FoodSales')));

const Recipe = Loadable(lazy(() => import('views/restaurant/recipe/Recipe')));
const RecipeCreate = Loadable(lazy(() => import('views/restaurant/recipe/forms/RecipeForm')));
const RecipeController = Loadable(lazy(() => import('views/restaurant/recipe/RecipeController')));
const ProductionCost = Loadable(lazy(() => import('views/restaurant/productionCost/ProductionCost')));

// Logistic
const Logistic = Loadable(lazy(() => import('views/logistic/Logistic')));
const Reposition = Loadable(lazy(() => import('views/logistic/reposition/Reposition')));
const Warehouse = Loadable(lazy(() => import('views/warehouse/Warehouse')));
const Analytics = Loadable(lazy(() => import('views/analytics/Analytics')));

const SupplyProduction = Loadable(lazy(() => import('views/logistic/supply-transformation/SupplyTransformation')));
const SupplyProductionForm = Loadable(lazy(() => import('views/logistic/supply-transformation/forms/SupplyTransformationForm')));

const LinkProducts = Loadable(lazy(() => import('views/logistic/link-products/LinkProducts')));
const SupplyStocks = Loadable(lazy(() => import('views/logistic/reports/supply-stocks/SupplyStocks')));

// Commercial
const SalesDashboard = Loadable(lazy(() => import('views/commercial/salesDashboard/salesDashboard')));
const SalesDetails = Loadable(lazy(() => import('views/commercial/salesDetails/salesDetails')));

// Administration
const ApprovalDashboard = Loadable(lazy(() => import('views/administration/ApprovalsDashboard/ApprovalDashboard')));

// ==============================|| MAIN ROUTING ||============================== //

const MainRoutes = (permissionsPromise) => ({
    path: '/',
    element: (
        <AuthGuard>
            <MainLayout />
        </AuthGuard>
    ),
    children: [
        {
            path: '/',
            element: <MainModules />
        },
        {
            path: '/modules',
            element: <MainModules />
        },
        {
            path: '/:modules',
            element: <MainModules />
        },
        {
            path: '/asset/classification',
            element: <AssetClassification />
        },
        {
            path: '/asset/classification/fixed-asset',
            element: <AssetFixedAssets />
        },
        {
            path: '/asset/classification/depreciation-group',
            element: <AssetDepreciationGroup />
        },
        {
            path: '/asset/classification/fixed-asset-movement',
            element: <AssetFixedAssetMovement />
        },
        {
            path: '/procedures/documentary-procedures',
            element: <DocumentaryProcedure />
        },
        {
            path: '/asset/process',
            element: <AssetProcess />
        },
        {
            path: '/asset/process/depreciation',
            element: <AssetProcessDepreciation />
        },
        {
            path: '/asset/process/depreciation/table',
            element: <AssetProcessDepreciationTable />
        },
        {
            path: '/financial',
            element: <Financial />
        },
        {
            path: `${paymentScheduleRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <PaymentSchedule />
                </Permisions>
            )
        },
        {
            path: `${paymentScheduleRouteCreate}`,
            element: <PaymentScheduleCreate permissionsPromise={permissionsPromise} />
        },
        {
            path: `${paymentScheduleRoute}/:id`,
            element: <PaymentScheduleController permissionsPromise={permissionsPromise} />
        },
        {
            path: `${transactionRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <Transactions />
                </Permisions>
            )
        },
        {
            path: `${transactionRouteCreate}`,
            element: <TransactionsCreate permissionsPromise={permissionsPromise} />
        },
        {
            path: restaurantRoute,
            element: <Restaurant />
        },
        {
            path: `${recipeRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <Recipe />
                </Permisions>
            )
        },
        {
            path: `${recipeRouteCreate}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <RecipeCreate />
                </Permisions>
            )
        },
        {
            path: `${recipeRoute}/:id`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <RecipeController />
                </Permisions>
            )
        },
        {
            path: `${productionCostRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <ProductionCost />
                </Permisions>
            )
        },
        {
            path: `${analyticsRoute}`,
            element: <Analytics />
        },
        {
            path: `${foodSalesRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <FoodSales />
                </Permisions>
            )
        },
        {
            path: `${logisticRoute}`,
            element: <Logistic />
        },
        {
            path: `${repositionRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <Reposition />
                </Permisions>
            )
        },
        {
            path: `${salesDashboardRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <SalesDashboard />
                </Permisions>
            )
        },
        {
            path: `${salesDetailsRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <SalesDetails />
                </Permisions>
            )
        },
        {
            path: `${warehouseRoute}`,
            element: <Warehouse />
        },
        {
            path: `${supplyTransformationRoute}`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <SupplyProduction />
                </Permisions>
            )
        },
        {
            path: `${supplyTransformationRouteCreate}/:id`,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <SupplyProductionForm />
                </Permisions>
            )
        },
        {
            path: linkProductsRoute,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <LinkProducts />
                </Permisions>
            )
        },
        {
            path: supplyStocksReportRoute,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <SupplyStocks />
                </Permisions>
            )
        },
        {
            path: approvalsDashboardRoute,
            element: (
                <Permisions permissionsPromise={permissionsPromise}>
                    <ApprovalDashboard />
                </Permisions>
            )
        },
        {
            path: '*',
            element: <>NOT FOUND</>
        }
    ]
});

export default MainRoutes;
