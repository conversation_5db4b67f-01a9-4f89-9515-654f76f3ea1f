import React from 'react';
import { Tooltip, Typography } from '@mui/material';
import { formatQuantityWithEquivalence } from '../../utils/formatUtils';
import { UNIT_EQUIVALENCE } from 'models/Presentation';

/**
 * Componente para mostrar valores de stock/proyección formateados con equivalencias
 * Incluye tooltip con valor sin formatear
 */
const AnalysisFormattedStockCell = ({ value, tableMeta }) => {
    // Validar que value sea un número válido
    if (value === null || value === undefined || value === '' || Number.isNaN(Number(value))) {
        return <Typography>0.00</Typography>;
    }

    // Obtener datos necesarios de las columnas ocultas
    const rowData = tableMeta.rowData;
    const productId = rowData[tableMeta.columnIndex - tableMeta.columnIndex + 1]; // product_id está en índice 1
    const presentations = rowData[tableMeta.columnIndex - tableMeta.columnIndex + 2]; // presentations está en índice 2
    const equivalenceDefault = rowData[tableMeta.columnIndex - tableMeta.columnIndex + 3]; // equivalence_default está en índice 3

    // Validar que tenemos los datos necesarios
    const parsedEquivalenceDefault = parseFloat(equivalenceDefault);
    
    if (productId && presentations && parsedEquivalenceDefault) {
        // Crear objeto para formateo usando los datos directos
        const formattingData = {
            product_id: productId,
            presentations,
            equivalence_default: parsedEquivalenceDefault
        };

        // Usar la función global de formateo
        const formattedStock = formatQuantityWithEquivalence(value, formattingData);

        if (formattedStock?.formatted_text) {
            let baseMeasureName = 'GR';
            if (presentations) {
                const basePresentation = Object.values(presentations).find(p => parseFloat(p.equivalence) === 1);
                if (basePresentation?.measure_name) {
                    baseMeasureName = basePresentation.measure_name;
                } else {
                    const baseKey = UNIT_EQUIVALENCE;
                    if (presentations[baseKey]?.measure_name) {
                        baseMeasureName = presentations[baseKey].measure_name;
                    }
                }
            }

            const tooltipText = `${parseFloat(value).toFixed(2)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ cursor: 'help' }}>
                        {formattedStock.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    // Fallback: mostrar valor sin formatear
    return <Typography>{parseFloat(value).toFixed(2)}</Typography>;
};

export default AnalysisFormattedStockCell;
