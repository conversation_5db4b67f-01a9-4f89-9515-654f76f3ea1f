<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Ability
 * 
 * @property int $movement_id
 * @property bool $is_upgraded
 * @property bool $is_overloaded
 * @property bool $is_transformed
 * @property bool $is_paid
 * @property bool $is_dispatched
 * @property bool $has_credit
 * @property bool $edit_confirmed
 * @property bool $pay_confirmed
 * @property bool $apply_confirmed
 * @property bool $dispatch_confirmed
 * @property bool $upgrade_confirmed
 * @property bool $null_confirmed
 * @property bool $remove_confirmed
 * @property bool $is_viewable
 * @property bool $is_editable
 * @property bool $is_provisionable
 * @property bool $is_discardable
 * @property bool $is_upgradeable
 * @property bool $is_overloadable
 * @property bool $is_transformable
 * @property bool $is_creditable
 * @property bool $is_payable
 * @property bool $is_redeemable
 * @property bool $is_applicable
 * @property bool $is_schedulable
 * @property bool $is_dispatchable
 * @property bool $is_clonable
 * @property bool $is_nullable
 * @property bool $is_removable
 * @property bool $is_advancing
 * @property bool $is_loggable
 * @property bool $is_printable
 * @property bool $is_entry_modifiable
 * @property bool $edit_skip_check_debt
 * @property bool $edit_skip_check_later
 * @property bool $edit_no_income
 * @property bool $edit_omit_deactivation
 * @property bool $edit_allow_sale_below_cost
 * @property bool $edit_allow_dispatch_below_cost
 * @property bool $edit_allow_sale_below_min_price
 * @property int|null $confirm_id
 * 
 * @property Confirmation|null $confirmation
 * @property Movement $movement
 *
 * @package App\Models
 */
class Ability extends Model
{
	protected $table = 'ability';
	protected $primaryKey = 'movement_id';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'movement_id' => 'int',
		'is_upgraded' => 'bool',
		'is_overloaded' => 'bool',
		'is_transformed' => 'bool',
		'is_paid' => 'bool',
		'is_dispatched' => 'bool',
		'has_credit' => 'bool',
		'edit_confirmed' => 'bool',
		'pay_confirmed' => 'bool',
		'apply_confirmed' => 'bool',
		'dispatch_confirmed' => 'bool',
		'upgrade_confirmed' => 'bool',
		'null_confirmed' => 'bool',
		'remove_confirmed' => 'bool',
		'is_viewable' => 'bool',
		'is_editable' => 'bool',
		'is_provisionable' => 'bool',
		'is_discardable' => 'bool',
		'is_upgradeable' => 'bool',
		'is_overloadable' => 'bool',
		'is_transformable' => 'bool',
		'is_creditable' => 'bool',
		'is_payable' => 'bool',
		'is_redeemable' => 'bool',
		'is_applicable' => 'bool',
		'is_schedulable' => 'bool',
		'is_dispatchable' => 'bool',
		'is_clonable' => 'bool',
		'is_nullable' => 'bool',
		'is_removable' => 'bool',
		'is_advancing' => 'bool',
		'is_loggable' => 'bool',
		'is_printable' => 'bool',
		'is_entry_modifiable' => 'bool',
		'edit_skip_check_debt' => 'bool',
		'edit_skip_check_later' => 'bool',
		'edit_no_income' => 'bool',
		'edit_omit_deactivation' => 'bool',
		'edit_allow_sale_below_cost' => 'bool',
		'edit_allow_dispatch_below_cost' => 'bool',
		'edit_allow_sale_below_min_price' => 'bool',
		'confirm_id' => 'int'
	];

	protected $fillable = [
		'is_upgraded',
		'is_overloaded',
		'is_transformed',
		'is_paid',
		'is_dispatched',
		'has_credit',
		'edit_confirmed',
		'pay_confirmed',
		'apply_confirmed',
		'dispatch_confirmed',
		'upgrade_confirmed',
		'null_confirmed',
		'remove_confirmed',
		'is_viewable',
		'is_editable',
		'is_provisionable',
		'is_discardable',
		'is_upgradeable',
		'is_overloadable',
		'is_transformable',
		'is_creditable',
		'is_payable',
		'is_redeemable',
		'is_applicable',
		'is_schedulable',
		'is_dispatchable',
		'is_clonable',
		'is_nullable',
		'is_removable',
		'is_advancing',
		'is_loggable',
		'is_printable',
		'is_entry_modifiable',
		'edit_skip_check_debt',
		'edit_skip_check_later',
		'edit_no_income',
		'edit_omit_deactivation',
		'edit_allow_sale_below_cost',
		'edit_allow_dispatch_below_cost',
		'edit_allow_sale_below_min_price',
		'confirm_id'
	];

	public function confirmation()
	{
		return $this->belongsTo(Confirmation::class, 'confirm_id');
	}

	public function movement()
	{
		return $this->belongsTo(Movement::class);
	}
}
