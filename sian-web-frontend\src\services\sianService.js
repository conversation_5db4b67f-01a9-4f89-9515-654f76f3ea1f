import axios from 'utils/axios';
import { executePromise } from './service';
import { sianSendDtoEndpoint } from './apiEnpoints';

/**
 * Send DTO data to SIAN system
 * @param {Object} dtoData - The data to send to SIAN
 * @param {string} endpoint - The SIAN endpoint to send data to (e.g., 'pay', 'transformationOrder')
 * @param {string} username - SIAN username
 * @param {string} password - SIAN password
 * @param {string} sianDomain - SIAN domain URL
 * @returns {Promise} - Promise with the response from SIAN
 */
export const sendDtoToSianPromise = async (dtoData, endpoint, username, password, sianDomain) => {
    const payload = {
        data: dtoData,
        endpoint: endpoint,
        username: username,
        password: password,
        sian_domain: sianDomain
    };

    return executePromise(axios.post(sianSendDtoEndpoint, payload));
};

/**
 * Send payment data to SIAN
 * @param {Object} paymentData - Payment data DTO
 * @param {string} username - SIAN username
 * @param {string} password - SIAN password
 * @param {string} sianDomain - SIAN domain URL
 * @returns {Promise} - Promise with the response from SIAN
 */
export const sendPaymentToSianPromise = async (paymentData, username, password, sianDomain) => {
    return sendDtoToSianPromise(paymentData, 'pay', username, password, sianDomain);
};

/**
 * Send transformation order data to SIAN
 * @param {Object} transformationData - Transformation order data DTO
 * @param {string} username - SIAN username
 * @param {string} password - SIAN password
 * @param {string} sianDomain - SIAN domain URL
 * @returns {Promise} - Promise with the response from SIAN
 */
export const sendTransformationOrderToSianPromise = async (transformationData, username, password, sianDomain) => {
    return sendDtoToSianPromise(transformationData, 'transformationOrder', username, password, sianDomain);
};

/**
 * Generic function to send any DTO to a custom SIAN endpoint
 * @param {Object} data - The data to send
 * @param {string} customEndpoint - Custom SIAN endpoint
 * @param {Object} credentials - Object containing username, password, and sianDomain
 * @returns {Promise} - Promise with the response from SIAN
 */
export const sendCustomDtoToSianPromise = async (data, customEndpoint, credentials) => {
    const { username, password, sianDomain } = credentials;
    return sendDtoToSianPromise(data, customEndpoint, username, password, sianDomain);
};

/**
 * Example usage function - can be used as a reference
 * @param {Object} exampleData - Example DTO data
 * @returns {Promise} - Promise with the response
 */
export const sendExampleDtoPromise = async (exampleData) => {
    // Example credentials - these should come from your app's state/context
    const credentials = {
        username: 'your-sian-username',
        password: 'your-sian-password',
        sianDomain: 'https://your-sian-domain.com'
    };

    // Example of sending to a custom endpoint
    return sendCustomDtoToSianPromise(exampleData, 'your-custom-endpoint', credentials);
};
