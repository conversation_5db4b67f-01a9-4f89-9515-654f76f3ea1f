import { <PERSON>, Typo<PERSON>, Button, Menu, MenuItem, FormControlLabel, Checkbox, IconButton, Tooltip } from '@mui/material';
import { ViewColumn as ViewColumnIcon } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'store';
import { getRepositionDataByProduct, openRotationModal, setSelectedProduct, editToCart } from 'store/slices/reposition/reposition';
import useLoading from 'hooks/useLoading';
import { BlockLoader } from 'ui-component/loaders/loaders';
import RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';
import Grid from 'ui-component/grid/Grid';
import NestedGrid from 'ui-component/grid/NestedGrid';
import MainCard from 'ui-component/cards/MainCard';

import SimpleAnalysisNestedContent from './components/nested/SimpleAnalysisNestedContent';
import { analisysDerivedProducts, simplifiedDerivedProductColumns, simplifiedNonDerivedProductColumns } from './config/columnDefinitions';
import {
    processAnalisys,
    findMainProductWithDerivedProduct,
    findDerivedProductRecursively,
    isRowDataAvailable
} from './utils/dataProcessing';
import { formatQuantityWithEquivalence } from './utils/formatUtils';
import { formatNumberWithCommas } from 'utils/number';
import { UNIT_EQUIVALENCE } from 'models/Presentation';

const findProductRecursivelyHelper = (products, targetId) => {
    if (!products) return null;

    const foundProduct = products.find((product) => {
        if (product.product_id === targetId) {
            return true;
        }

        if (product.derivedProducts && product.derivedProducts.length > 0) {
            const found = findProductRecursivelyHelper(product.derivedProducts, targetId);
            if (found) return true;
        }
        return false;
    });

    if (foundProduct) {
        if (foundProduct.product_id === targetId) {
            return foundProduct;
        }
        return findProductRecursivelyHelper(foundProduct.derivedProducts, targetId);
    }

    return null;
};

const lastDerivedProductColumns = [
    {
        name: 'product_id',
        label: 'ID',
        options: {
            filter: true,
            sort: true,
            display: false
        }
    },
    {
        name: 'product_name',
        label: 'PRODUCTO',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { minWidth: '200px', width: 'auto' } }),
            setCellProps: () => ({ style: { minWidth: '200px', width: 'auto' } }),
            customBodyRender: (value) => (
                <Typography sx={{ whiteSpace: 'nowrap' }}>
                    <strong>{value}</strong>
                </Typography>
            )
        }
    },
    {
        name: 'measure_default',
        label: 'PRESENTACIÓN',
        options: {
            filter: true,
            sort: true,
            display: true,
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[0];
                return <PresentationCell value={value} productId={productId} />;
            }
        }
    },
    {
        name: 'unit_quantity_proyected',
        label: 'C.PROYECTADA',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { minWidth: '140px', width: 'auto' } }),
            setCellProps: () => ({ style: { minWidth: '140px', width: 'auto' } }),
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[0];
                return <DirectFormattedProjectionCell value={value} productId={productId} />;
            }
        }
    },
    {
        name: 'purchase_stock',
        label: 'STOCK EN TIENDAS',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[0];
                return <StockTiendasCell value={value} tableMeta={tableMeta} productId={productId} />;
            }
        }
    },
    {
        name: 'supplying_stock',
        label: 'STOCK A.PRINCIPAL',
        options: {
            filter: true,
            sort: true,
            display: false,
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[0];
                return <DirectFormattedStockCell value={value} productId={productId} />;
            }
        }
    },
    {
        name: 'to_transform',
        label: 'TRANSFORMAR',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[0];
                return <TransformarCell value={value} tableMeta={tableMeta} productId={productId} />;
            }
        }
    },
    {
        name: 'rep_pres_min',
        label: 'REPONER',
        options: {
            filter: true,
            sort: false,
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[0];
                return <NetoTransformarCell value={value} productId={productId} />;
            }
        }
    },
    {
        name: 'measure_name',
        label: 'PRES MIN',
        options: {
            filter: true,
            sort: true,
            display: false
        }
    }
];

const getDerivedProducts = (productData) => {
    if (!productData || !productData.derivedProducts) return [];

    return productData.derivedProducts.map((item, index) => ({
        ...item,
        pk: item.product_id,
        globalIndex: index
    }));
};

const NestedCard = ({ children, width = '80%' }) => (
    <Box sx={{ p: 1, backgroundColor: 'white', borderRadius: '1rem', border: '1px solid #e0e0e0', width }}>{children}</Box>
);

const DirectFormattedProjectionCell = ({ value, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => {
        if (!products) return null;

        const foundProduct = products.find((product) => {
            if (product.product_id === targetId) {
                return true;
            }

            if (product.derivedProducts && product.derivedProducts.length > 0) {
                const found = findProductRecursively(product.derivedProducts, targetId);
                if (found) return true;
            }
            return false;
        });

        if (foundProduct) {
            if (foundProduct.product_id === targetId) {
                return foundProduct;
            }

            return findProductRecursively(foundProduct.derivedProducts, targetId);
        }

        return null;
    };

    let productData = data?.find((item) => item.product_id === productId);

    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    if (productData && value > 0) {
        const formattedProjection = formatQuantityWithEquivalence(value, productData);

        if (formattedProjection?.formatted_text) {
            let baseMeasureName = 'GR';
            if (productData?.presentations) {
                const basePresentation = Object.values(productData.presentations).find((p) => parseFloat(p.equivalence) === 1);
                if (basePresentation?.measure_name) {
                    baseMeasureName = basePresentation.measure_name;
                } else {
                    const baseKey = '1.00';
                    if (productData.presentations[baseKey]?.measure_name) {
                        baseMeasureName = productData.presentations[baseKey].measure_name;
                    }
                }
            }

            const tooltipText = `${formatNumberWithCommas(value)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                        {formattedProjection.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    return <RightAlignedNumber value={value} />;
};

const DirectFormattedStockCell = ({ value, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);

    let productData = data?.find((item) => item.product_id === productId);

    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    if (productData && value > 0) {
        const formattedStock = formatQuantityWithEquivalence(value, productData);

        if (formattedStock?.formatted_text) {
            let baseMeasureName = 'GR';
            if (productData?.presentations) {
                const basePresentation = Object.values(productData.presentations).find((p) => parseFloat(p.equivalence) === 1);
                if (basePresentation?.measure_name) {
                    baseMeasureName = basePresentation.measure_name;
                } else {
                    const baseKey = '1.00';
                    if (productData.presentations[baseKey]?.measure_name) {
                        baseMeasureName = productData.presentations[baseKey].measure_name;
                    }
                }
            }

            const tooltipText = `${formatNumberWithCommas(value)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                        {formattedStock.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    return <RightAlignedNumber value={value} />;
};

const NetoCell = ({ value, tableMeta, showMeasureLabel = null }) => {
    const displayValue = formatNumberWithCommas(value);
    return <Typography sx={{ fontSize: '0.875rem', textAlign: 'right', color: value > 0 ? 'green' : 'red' }}>{displayValue}</Typography>;
};

export const DisplayUnitValueCell = ({ value, tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);
    let productData = data?.find((item) => item.product_id === productId);
    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    const displayValue = formatNumberWithCommas(value);
    const baseMeasureName = productData?.measure_name?.split('*')[0] || productData?.measure_default || '';

    return (
        <Typography sx={{ fontSize: '0.875rem', textAlign: 'right', color: value > 0 ? 'green' : 'red' }}>
            {displayValue} {baseMeasureName}
        </Typography>
    );
};

const SumaNetosAnalisisCell = ({ value, tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);
    let productData = data?.find((item) => item.product_id === productId);
    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    let calculatedSuma = 0;
    if (productData && productData.analisys && Array.isArray(productData.analisys)) {
        calculatedSuma = productData.analisys.reduce((sum, analysis) => {
            const storeStock = parseFloat(analysis.purchase_stock || 0);
            const storeProjection = parseFloat(analysis.unit_quantity_proyected || 0);
            const storeNeto = Math.max(0, storeProjection - storeStock);
            return sum + storeNeto;
        }, 0);
    }

    const finalValue = calculatedSuma > 0 ? calculatedSuma : value || 0;

    if (productData && finalValue > 0) {
        const formattedStock = formatQuantityWithEquivalence(finalValue, productData);

        if (formattedStock && formattedStock.formatted_text) {
            const baseMeasureName = productData.measure_name?.split('*')[0] || productData.measure_default || '';
            const tooltipText = `${formatNumberWithCommas(finalValue)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                        {formattedStock.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    return <RightAlignedNumber value={finalValue} />;
};

const TransformarCell = ({ value, tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);
    let productData = data?.find((item) => item.product_id === productId);
    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    // Calcular suma de netos del análisis
    let calculatedSuma = 0;
    if (productData && productData.analisys && Array.isArray(productData.analisys)) {
        calculatedSuma = productData.analisys.reduce((sum, analysis) => {
            const storeStock = parseFloat(analysis.purchase_stock || 0);
            const storeProjection = parseFloat(analysis.unit_quantity_proyected || 0);
            const storeNeto = Math.max(0, storeProjection - storeStock);
            return sum + storeNeto;
        }, 0);
    }

    // Para TRANSFORMAR: suma_netos_analisis - stock_almacen_principal
    const stockAlmacenPrincipal = parseFloat(productData?.supplying_stock || 0);
    const calculatedTransformar = Math.max(0, calculatedSuma - stockAlmacenPrincipal);

    // Si el valor calculado es mayor que 0, usarlo; sino usar el valor del backend
    const finalValue = calculatedTransformar > 0 ? calculatedTransformar : value || 0;

    // Formatear usando el mismo componente que otros stocks
    if (productData && finalValue > 0) {
        const formattedStock = formatQuantityWithEquivalence(finalValue, productData);

        if (formattedStock && formattedStock.formatted_text) {
            const baseMeasureName = productData.measure_name?.split('*')[0] || productData.measure_default || '';
            const tooltipText = `${formatNumberWithCommas(finalValue)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                        {formattedStock.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    return <RightAlignedNumber value={finalValue} />;
};

const NetoTransformarCell = ({ value, tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);
    let productData = data?.find((item) => item.product_id === productId);
    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    // Calcular suma de netos del análisis
    let calculatedSuma = 0;
    if (productData && productData.analisys && Array.isArray(productData.analisys)) {
        calculatedSuma = productData.analisys.reduce((sum, analysis) => {
            const storeStock = parseFloat(analysis.purchase_stock || 0);
            const storeProjection = parseFloat(analysis.unit_quantity_proyected || 0);
            const storeNeto = Math.max(0, storeProjection - storeStock);
            return sum + storeNeto;
        }, 0);
    }

    // Para TRANSFORMAR: suma_netos_analisis - stock_almacen_principal
    const stockAlmacenPrincipal = parseFloat(productData?.supplying_stock || 0);
    const calculatedTransformar = Math.max(0, calculatedSuma - stockAlmacenPrincipal);

    // Si el valor calculado es mayor que 0, usarlo; sino usar el valor del backend
    const finalValue = calculatedTransformar > 0 ? calculatedTransformar : value || 0;

    // Formatear usando el mismo componente que otros stocks
    if (productData) {
        const baseMeasureName = productData.measure_name?.split('*')[0] || productData.measure_default || '';
        const displayValue = formatNumberWithCommas(finalValue);
        return (
            <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                {displayValue} {baseMeasureName}
            </Typography>
        );
    }

    return <RightAlignedNumber value={finalValue} />;
};

const NetoRawMaterialCell = ({ value, tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    // Función para buscar el producto recursivamente
    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);

    // Buscar el producto en datos principales
    let productData = data?.find((item) => item.product_id === productId);
    let isMainProduct = !!productData;

    // Si no es producto principal, buscar en derivedProducts
    if (!productData) {
        data?.forEach((mainProduct) => {
            if (mainProduct.derivedProducts && mainProduct.derivedProducts.length > 0) {
                const foundDerived = mainProduct.derivedProducts.find((derived) => derived.product_id === productId);
                if (foundDerived) {
                    productData = foundDerived;
                    isMainProduct = false;
                }
            }
        });
    }

    let netoValue = 0;

    // Para productos derivados, usar el NETO ya calculado en el procesamiento
    if (!isMainProduct && productData?.calculated_neto !== undefined) {
        netoValue = productData.calculated_neto;
    }
    // Para productos derivados sin sub-derivados, usar suma de to_transform del análisis
    else if (productData && productData.analisys && Array.isArray(productData.analisys)) {
        netoValue = productData.analisys.reduce((sum, analysis) => sum + parseFloat(analysis.to_transform || 0), 0);
    }
    // Fallback: usar valor calculado o directo
    else {
        netoValue = productData?.calculated_neto !== undefined ? productData.calculated_neto : value || 0;
    }

    // Para NETO, mostrar el valor sin formatear con unidades
    const baseMeasureName = productData?.measure_name?.split('*')[0] || productData?.measure_default || '';
    const displayValue = parseFloat(netoValue).toFixed(2);

    const getColor = () => {
        if (netoValue > 0) return 'green';
        if (netoValue < 0) return 'red';
        return 'inherit';
    };

    return (
        <Typography
            sx={{
                fontSize: '0.875rem',
                textAlign: 'right',
                color: getColor()
            }}
        >
            {displayValue} {baseMeasureName}
        </Typography>
    );
};

const StockTiendasCell = ({ value, tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    // Función para buscar el producto recursivamente
    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);

    // Buscar el producto
    let productData = data?.find((item) => item.product_id === productId);
    if (!productData) {
        productData = findProductRecursively(data, productId);
    }

    // Calcular suma de stocks del análisis
    let calculatedStock = 0;
    if (productData && productData.analisys && Array.isArray(productData.analisys)) {
        calculatedStock = productData.analisys.reduce((sum, analysis) => {
            const storeStock = parseFloat(analysis.purchase_stock || 0);
            return sum + storeStock;
        }, 0);
    }

    // Si tenemos datos del análisis, usar el valor calculado; sino usar el valor del backend
    const finalValue = productData && productData.analisys ? calculatedStock : value || 0;

    // Formatear usando el mismo componente que otros stocks
    if (productData && finalValue > 0) {
        const formattedStock = formatQuantityWithEquivalence(finalValue, productData);

        if (formattedStock && formattedStock.formatted_text) {
            const baseMeasureName = productData.measure_name?.split('*')[0] || productData.measure_default || '';
            const tooltipText = `${formatNumberWithCommas(finalValue)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                        {formattedStock.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    return <RightAlignedNumber value={finalValue} />;
};

export const ProyeccionFinalMateriaPrimaCell = ({ tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    // Buscar la materia prima
    const materiaPrima = data?.find((item) => item.product_id === productId);
    if (!materiaPrima) {
        return <RightAlignedNumber value={0} />;
    }

    // Usar el valor ya calculado en el procesamiento de datos
    const proyeccionFinal = materiaPrima.proyeccion_final || 0;

    // Solo mostrar para materias primas que tienen productos derivados
    if (!materiaPrima.derivedProducts || materiaPrima.derivedProducts.length === 0 || proyeccionFinal === 0) {
        return <Typography sx={{ fontSize: '0.875rem', textAlign: 'right', color: 'grey.500' }}>-</Typography>;
    }

    return <RightAlignedNumber value={proyeccionFinal} />;
};

export const CReponerMateriaPrimaCell = ({ tableMeta, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    // Buscar la materia prima
    const materiaPrima = data?.find((item) => item.product_id === productId);
    if (!materiaPrima) {
        return <RightAlignedNumber value={0} />;
    }

    // Calcular suma de NETOS de productos derivados (sin multiplicar por waste_info)
    let sumaNetos = 0;
    if (materiaPrima.derivedProducts && materiaPrima.derivedProducts.length > 0) {
        materiaPrima.derivedProducts.forEach((derivedProduct) => {
            sumaNetos += parseFloat(derivedProduct.calculated_neto || 0);
        });
    }

    if (!materiaPrima.derivedProducts || materiaPrima.derivedProducts.length === 0 || sumaNetos === 0) {
        return <Typography sx={{ fontSize: '0.875rem', textAlign: 'right', color: 'grey.500' }}>-</Typography>;
    }

    return <RightAlignedNumber value={sumaNetos} />;
};

const CReponerDerivedProductCell = ({ value, tableMeta, productId, showMeasure = null }) => {
    const { data } = useSelector((state) => state.reposition);

    let productData = null;
    let cReponerValue = 0;

    productData = data?.find((item) => item.product_id === productId);

    if (productData) {
        cReponerValue = productData?.c_reponer || 0;
    } else {
        data?.forEach((mainProduct) => {
            if (mainProduct.derivedProducts && mainProduct.derivedProducts.length > 0) {
                const foundDerived = mainProduct.derivedProducts.find((derived) => derived.product_id === productId);
                if (foundDerived) {
                    productData = foundDerived;
                    cReponerValue = foundDerived.c_reponer || 0;
                }
            }
        });
    }

    if (!productData?.derivedProducts || productData.derivedProducts.length === 0 || cReponerValue === 0) {
        return <Typography sx={{ fontSize: '0.875rem', textAlign: 'right', color: 'grey.500' }}>-</Typography>;
    }

    if (showMeasure) {
        const baseMeasureName = productData?.measure_name?.split('*')[0] || productData?.measure_default || '';
        const displayValue = formatNumberWithCommas(cReponerValue);

        return (
            <Typography sx={{ fontSize: '0.875rem', textAlign: 'right' }}>
                {displayValue} {baseMeasureName}
            </Typography>
        );
    }

    return <RightAlignedNumber value={cReponerValue} />;
};

const useAutoCalculateCartQuantities = () => {
    const dispatch = useDispatch();
    const { data, cart } = useSelector((state) => state.reposition);

    useEffect(() => {
        if (!data || data.length === 0) return;

        const rawMaterials = data.filter((product) => !product.derivedProducts || product.derivedProducts.length === 0);

        rawMaterials.forEach((rawMaterial) => {
            let totalDerivedNeto = 0;

            data.forEach((mainProduct) => {
                if (mainProduct.derivedProducts && mainProduct.derivedProducts.length > 0) {
                    mainProduct.derivedProducts.forEach((derivedProduct) => {
                        if (derivedProduct.product_id === rawMaterial.product_id) {
                            let derivedNeto = 0;
                            if (derivedProduct.analisys && Array.isArray(derivedProduct.analisys)) {
                                derivedNeto = derivedProduct.analisys.reduce((sum, analysis) => {
                                    const storeStock = parseFloat(analysis.purchase_stock || 0);
                                    const storeProjection = parseFloat(analysis.unit_quantity_proyected || 0);
                                    const storeNeto = storeProjection - storeStock;
                                    return sum + storeNeto;
                                }, 0);
                            }
                            totalDerivedNeto += derivedNeto;
                        }
                    });
                }
            });

            const stockAlmacenPrincipal = parseFloat(rawMaterial.supplying_stock || 0);
            const cantidadAComprar = Math.max(0, totalDerivedNeto - stockAlmacenPrincipal);

            if (cantidadAComprar > 0) {
                const cartKey = `${rawMaterial.product_id}_auto`;

                // Verificar si ya existe en el cart
                if (!cart[cartKey]) {
                    const cartItem = {
                        pk: cartKey,
                        product_id: rawMaterial.product_id,
                        product_name: rawMaterial.product_name,
                        quantity_oc: cantidadAComprar,
                        equivalence: rawMaterial.equivalence_default || 1,
                        presentation: rawMaterial.presentations?.[rawMaterial.equivalence_default] || {},
                        unit_price: rawMaterial.unit_price || 0,
                        hasTouch: false, // Marcado como automático
                        isAutoCalculated: true // Flag para identificar items auto-calculados
                    };

                    dispatch(editToCart(cartKey, cartItem));
                }
                // Si existe pero la cantidad cambió, actualizarlo
                else if (cart[cartKey] && !cart[cartKey].hasTouch && Math.abs(cart[cartKey].quantity_oc - cantidadAComprar) > 0.01) {
                    dispatch(
                        editToCart(cartKey, {
                            quantity_oc: cantidadAComprar,
                            isAutoCalculated: true
                        })
                    );
                }
            }
            // Si no hay cantidad a comprar pero existe en el cart como auto-calculado, removerlo
            else if (cantidadAComprar <= 0) {
                const cartKey = `${rawMaterial.product_id}_auto`;
                if (cart[cartKey] && cart[cartKey].isAutoCalculated && !cart[cartKey].hasTouch) {
                    dispatch(editToCart(cartKey, { quantity_oc: 0 }));
                }
            }
        });
    }, [data, dispatch]); // No incluir cart en dependencies para evitar loops infinitos
};

const PresentationCell = ({ value, productId }) => {
    const { data } = useSelector((state) => state.reposition);

    const findProductRecursively = (products, targetId) => findProductRecursivelyHelper(products, targetId);

    let productData = data?.find((item) => item.product_id === productId);

    if (!productData) {
        productData = findProductRecursively(data, productId);
    }
    if (productData?.presentations && productData?.equivalence_default) {
        const presentations = productData.presentations;
        const equivalenceDefault = productData.equivalence_default;

        const equivalenceKey = parseFloat(equivalenceDefault).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        let targetPresentation = presentations[equivalenceKey];

        if (equivalenceDefault === UNIT_EQUIVALENCE) {
            return <Typography sx={{ fontSize: '0.875rem', textAlign: 'center' }}>{targetPresentation.measure_name}</Typography>;
        }
        if (!targetPresentation) {
            const simpleKey = parseFloat(equivalenceDefault).toFixed(2);
            targetPresentation = presentations[simpleKey];
        }

        if (targetPresentation) {
            const presentationName = `${targetPresentation.measure_name}`.split('*')[0].replace(/\.$/, '');
            const measureName = `${productData.measure_name}`;
            const equivalence = parseFloat(targetPresentation.equivalence);
            const displayText = `${presentationName} de ${equivalence.toLocaleString()} ${measureName}`;

            return <Typography sx={{ fontSize: '0.875rem', textAlign: 'center' }}>{displayText}</Typography>;
        }
    }

    return <Typography sx={{ fontSize: '0.875rem', textAlign: 'center' }}>{value || '-'}</Typography>;
};

// Componente para formatear stocks usando datos directamente del rowData
const AnalysisFormattedStockCell = ({ value, tableMeta }) => {
    // No retornar temprano para valores 0, necesitamos formatearlos también

    // Obtener datos directamente del objeto completo de la fila
    // Los datos están en tableMeta.tableData[tableMeta.rowIndex] según el log
    const fullRowData = tableMeta.tableData[tableMeta.rowIndex];

    if (!fullRowData) {
        return <RightAlignedNumber value={value} />;
    }

    // Verificar si tenemos los datos necesarios para formatear
    const productId = fullRowData.product_id;
    const presentations = fullRowData.presentations;
    const equivalenceDefault = fullRowData.equivalence_default;

    // Si no tenemos los datos necesarios, mostrar valor sin formatear
    if (!productId || !presentations || !equivalenceDefault) {
        return <RightAlignedNumber value={value} />;
    }

    const parsedEquivalenceDefault = parseFloat(equivalenceDefault);

    if (productId && presentations && parsedEquivalenceDefault) {
        // Crear objeto para formateo usando los datos directos
        const formattingData = {
            product_id: productId,
            presentations,
            equivalence_default: parsedEquivalenceDefault
        };

        // Usar la función global de formateo
        const formattedStock = formatQuantityWithEquivalence(value, formattingData);

        if (formattedStock?.formatted_text) {
            // Buscar la unidad base (equivalencia 1) para el tooltip
            let baseMeasureName = 'GR'; // fallback
            if (presentations) {
                const basePresentation = Object.values(presentations).find((p) => parseFloat(p.equivalence) === 1);
                if (basePresentation?.measure_name) {
                    baseMeasureName = basePresentation.measure_name;
                } else {
                    const baseKey = '1.00';
                    if (presentations[baseKey]?.measure_name) {
                        baseMeasureName = presentations[baseKey].measure_name;
                    }
                }
            }

            const tooltipText = `${formatNumberWithCommas(value)} ${baseMeasureName}`;

            return (
                <Tooltip title={tooltipText} arrow>
                    <Typography sx={{ textAlign: 'right', fontSize: '0.875rem', cursor: 'pointer' }}>
                        {formattedStock.formatted_text}
                    </Typography>
                </Tooltip>
            );
        }
    }

    return <RightAlignedNumber value={value} />;
};

const DerivedProductAnalysis = ({ row, columns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const derivedAnalysis = derivedProduct?.analisys ? processAnalisys(derivedProduct, storeData) : null;
    const hasProjectedColumn = columns.some((col) => col.name === 'unit_quantity_proyected');

    let analysisColumns = columns;
    if (!hasProjectedColumn && derivedAnalysis && derivedAnalysis.length > 0) {
        const firstRow = derivedAnalysis[0];
        if (firstRow.unit_quantity_proyected !== undefined) {
            analysisColumns = [...columns];
        }
    }

    if (!derivedAnalysis || derivedAnalysis.length === 0) {
        return (
            <Box sx={{ p: 2, textAlign: 'center', color: 'text.secondary' }}>
                <Typography variant="body2">No hay análisis disponible para este producto derivado</Typography>
            </Box>
        );
    }

    const totalStockUnitario = derivedAnalysis.reduce((sum, item) => sum + parseFloat(item.purchase_stock || 0), 0);

    const customFooter = () => {
        const baseMeasureName = derivedProduct?.measure_name?.split('*')[0] || derivedProduct?.measure_default || 'GR';

        return (
            <Box sx={{ p: 1, borderTop: 1, borderColor: 'divider' }}>
                <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                    TOTAL: {formatNumberWithCommas(totalStockUnitario)} {baseMeasureName}
                </Typography>
            </Box>
        );
    };

    return (
        <Grid
            columns={analysisColumns}
            data={derivedAnalysis}
            options={{
                search: false,
                download: false,
                print: false,
                sort: false,
                viewColumns: false,
                filter: false,
                pagination: false,
                selectableRows: 'none',
                toolbar: false,
                elevation: 0,
                customFooter
            }}
        />
    );
};

const DerivedProductNestedContent = ({ row, data, derivedAnalysisColumns, simpleAnalysisColumns, completeAnalysisColumns }) => {
    const productId = row[0];
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const hasSubDerived = derivedProduct?.derivedProducts && derivedProduct.derivedProducts.length > 0;

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 2,
                pb: 1,
                px: 2,
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '1rem',
                my: 1
            }}
        >
            <Box sx={{ width: hasSubDerived ? '25%' : '80%' }}>
                <MainCard>
                    <DerivedProductAnalysis row={row} columns={derivedAnalysisColumns} />
                </MainCard>
            </Box>
            {hasSubDerived && (
                <Box sx={{ width: '75%' }}>
                    <MainCard>
                        <SubDerivedProducts
                            row={row}
                            columns={lastDerivedProductColumns}
                            analysisColumns={derivedAnalysisColumns}
                            completeAnalysisColumns={derivedAnalysisColumns}
                            lastDerivedProductColumns={lastDerivedProductColumns}
                        />
                    </MainCard>
                </Box>
            )}
        </Box>
    );
};

const SubDerivedProducts = ({ row, columns, analysisColumns, completeAnalysisColumns }) => {
    const productId = row[0];
    const { data } = useSelector((state) => state.reposition);
    const mainProductData = findMainProductWithDerivedProduct(data, productId);
    const derivedProduct = findDerivedProductRecursively(mainProductData?.derivedProducts, productId);
    const rawSubDerivedProducts = derivedProduct?.derivedProducts || [];

    // Separar sub-derivados en dos grupos
    const subDerivedWithSubProducts = rawSubDerivedProducts.filter(
        (product) => product.derivedProducts && product.derivedProducts.length > 0
    );

    const subDerivedWithoutSubProducts = rawSubDerivedProducts.filter(
        (product) => !product.derivedProducts || product.derivedProducts.length === 0
    );

    if (rawSubDerivedProducts.length === 0) {
        return (
            <Box sx={{ p: 2, textAlign: 'center' }}>
                <Typography color="textSecondary">No hay sub-derivados disponibles</Typography>
            </Box>
        );
    }

    console.log({ columns, subDerivedWithoutSubProducts });

    return (
        <Box sx={{ width: '100%', height: 'fit-content', display: 'flex', flexDirection: 'column', gap: 2 }}>
            {subDerivedWithSubProducts.length > 0 && (
                <Box
                    sx={{
                        '& .MuiTable-root': {
                            width: '100% !important',
                            tableLayout: 'auto'
                        },
                        '& .MuiTableCell-root': {
                            padding: '4px 8px',
                            fontSize: '0.75rem'
                        }
                    }}
                >
                    <NestedGrid
                        columns={columns}
                        data={subDerivedWithSubProducts.map((item, index) => ({
                            ...item,
                            pk: item.product_id,
                            globalIndex: index,
                            // Agregar campos necesarios para formateo en columnas ocultas
                            product_id: item.product_id,
                            presentations: item.presentations,
                            equivalence_default: item.equivalence_default
                        }))}
                        title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Sub-Derivados (Con Sub-sub-derivados)</Typography>}
                        RenderNestedContent={(props) => (
                            <DerivedProductNestedContent
                                {...props}
                                data={data}
                                derivedAnalysisColumns={completeAnalysisColumns}
                                simpleAnalysisColumns={completeAnalysisColumns}
                                simplifiedDerivedProductColumns={columns}
                                lastDerivedProductColumns={lastDerivedProductColumns}
                            />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            pagination: false,
                            selectableRows: 'none',
                            toolbar: true,
                            elevation: 0,
                            responsive: 'vertical'
                        }}
                    />
                </Box>
            )}

            {subDerivedWithoutSubProducts.length > 0 && (
                <Box
                    sx={{
                        '& .MuiTable-root': {
                            width: '100% !important',
                            tableLayout: 'auto'
                        },
                        '& .MuiTableCell-root': {
                            padding: '4px 8px',
                            fontSize: '0.75rem'
                        }
                    }}
                >
                    <NestedGrid
                        columns={columns}
                        data={subDerivedWithoutSubProducts.map((item, index) => ({
                            ...item,
                            pk: item.product_id,
                            globalIndex: index + subDerivedWithSubProducts.length
                        }))}
                        title={<Typography sx={{ fontSize: '1rem', fontWeight: 'bold' }}>Sub-Derivados (Sin Sub-derivados)</Typography>}
                        RenderNestedContent={(props) => (
                            <SimpleAnalysisNestedContent
                                {...props}
                                data={data}
                                analisysDerivedProducts={[
                                    {
                                        name: 'pk',
                                        label: 'PK',
                                        options: {
                                            filter: false,
                                            sort: false,
                                            display: false
                                        }
                                    },
                                    {
                                        name: 'product_id',
                                        label: 'ID',
                                        options: {
                                            filter: false,
                                            sort: false,
                                            display: false
                                        }
                                    },
                                    {
                                        name: 'store_id',
                                        label: 'ID',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            display: false
                                        }
                                    },
                                    {
                                        name: 'store_name',
                                        label: 'TIENDA',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            customBodyRender: (value) => {
                                                // Quitar "Tienda" y todos los guiones rodeados de espacios
                                                const cleanName = value
                                                    ? value
                                                          .replace(/^Tienda\s*/i, '')
                                                          .replace(/\s*-\s*/g, ' ')
                                                          .trim()
                                                    : value;
                                                return <Typography sx={{ fontWeight: 'bold' }}>{cleanName}</Typography>;
                                            }
                                        }
                                    },
                                    {
                                        name: 'measure_default',
                                        label: 'PRESENTACIÓN',
                                        options: {
                                            filter: true,
                                            sort: false,
                                            display: true,
                                            customBodyRender: (value, tableMeta) => {
                                                const rowData = tableMeta.rowData;
                                                const productId = rowData[1];
                                                return <PresentationCell value={value} productId={productId} />;
                                            }
                                        }
                                    },
                                    {
                                        name: 'unit_quantity_proyected',
                                        label: 'C.PROYECTADA',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            customBodyRender: (value, tableMeta) => {
                                                const rowData = tableMeta.rowData;
                                                const productId = rowData[1];
                                                return <DirectFormattedProjectionCell value={value} productId={productId} />;
                                            }
                                        }
                                    },
                                    {
                                        name: 'purchase_stock',
                                        label: 'STOCK',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            display: true,
                                            customBodyRender: (value, tableMeta) => {
                                                const rowData = tableMeta.rowData;
                                                const productId = rowData[1];
                                                return <DirectFormattedStockCell value={value} productId={productId} />;
                                            }
                                        }
                                    },
                                    {
                                        name: 'neto',
                                        label: 'TRANSFORMAR',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            customBodyRender: (value, tableMeta) => {
                                                const rowData = tableMeta.rowData;
                                                const productId = rowData[1];
                                                return <DirectFormattedStockCell value={value} productId={productId} />;
                                            }
                                        }
                                    },
                                    {
                                        name: 'neto',
                                        label: 'REPONER',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            customBodyRender: (value, tableMeta) => {
                                                const rowData = tableMeta.rowData;
                                                const productId = rowData[1];
                                                return <DisplayUnitValueCell value={value} productId={productId} />;
                                            }
                                        }
                                    },
                                    {
                                        name: 'measure_name',
                                        label: 'PRES MIN',
                                        options: {
                                            filter: true,
                                            sort: true,
                                            display: false
                                        }
                                    }
                                ]}
                            />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            pagination: false,
                            selectableRows: 'none',
                            toolbar: true,
                            elevation: 0,
                            responsive: 'vertical'
                        }}
                    />
                    {subDerivedWithoutSubProducts.length > 0 && (
                        <Box
                            sx={{
                                p: 1,
                                borderTop: 1,
                                borderColor: 'divider',
                                borderBottomRightRadius: '1rem',
                                borderBottomLeftRadius: '1rem'
                            }}
                        >
                            <Typography variant="body2" sx={{ fontWeight: 'bold', textAlign: 'right' }}>
                                TOTAL:{' '}
                                {(() => {
                                    const totalReponer = subDerivedWithoutSubProducts.reduce((sum, item) => {
                                        // Replicar la lógica de NetoTransformarCell
                                        let calculatedSuma = 0;
                                        if (item && item.analisys && Array.isArray(item.analisys)) {
                                            calculatedSuma = item.analisys.reduce((analysisSum, analysis) => {
                                                const storeStock = parseFloat(analysis.purchase_stock || 0);
                                                const storeProjection = parseFloat(analysis.unit_quantity_proyected || 0);
                                                const storeNeto = Math.max(0, storeProjection - storeStock);
                                                return analysisSum + storeNeto;
                                            }, 0);
                                        }

                                        const stockAlmacenPrincipal = parseFloat(item?.supplying_stock || 0);
                                        const calculatedTransformar = Math.max(0, calculatedSuma - stockAlmacenPrincipal);
                                        const finalValue =
                                            calculatedTransformar > 0 ? calculatedTransformar : parseFloat(item.rep_pres_min || 0);

                                        console.log(`Item: ${item.product_name}, finalValue: ${finalValue}`);
                                        return sum + finalValue;
                                    }, 0);
                                    console.log(`Total calculado: ${totalReponer}`);
                                    const firstProduct = subDerivedWithoutSubProducts[0];
                                    const baseMeasureName =
                                        firstProduct?.measure_name?.split('*')[0] || firstProduct?.measure_default || 'GR';
                                    return `${formatNumberWithCommas(totalReponer)} ${baseMeasureName}`;
                                })()}
                            </Typography>
                        </Box>
                    )}
                </Box>
            )}
        </Box>
    );
};

const RawMaterialRotationDetail = ({ row, filters, supplyAnalisys, isFromProyection, merchandiseFoodData }) => {
    const dispatch = useDispatch();
    const { data } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);

    // Activar el cálculo automático de cantidades del cart
    useAutoCalculateCartQuantities();

    const productData = isFromProyection
        ? merchandiseFoodData.find((item) => item.product_id === row[0])
        : data.find((item) => item.product_id === row[0]);

    const derivedProducts = getDerivedProducts(productData);
    const derivedWithSubProducts = derivedProducts.filter((product) => product.derivedProducts && product.derivedProducts.length > 0);
    const derivedWithoutSubProducts = derivedProducts.filter((product) => !product.derivedProducts || product.derivedProducts.length === 0);

    const displayProducts = (() => {
        if (derivedProducts.length > 0) {
            return derivedProducts;
        }
        if (productData) {
            return [
                {
                    ...productData,
                    pk: productData.product_id || productData.pk,
                    globalIndex: 0
                }
            ];
        }
        return [];
    })();

    const [repositionProduct, setRepositionProduct] = useState(supplyAnalisys || []);
    const [isAsync] = useState(!supplyAnalisys);
    const [loading, startLoading, endLoading] = useLoading(isAsync);

    const openModal = () => dispatch(openRotationModal());
    const setSelected = (data) => dispatch(setSelectedProduct(data));

    const reload = () => {
        if (isAsync) {
            startLoading();
            dispatch(getRepositionDataByProduct(row[0], { ...filters, mode: isFromProyection ? 'Merc2' : filters.mode }, storeData)).then(
                (data) => {
                    setRepositionProduct(data);
                    endLoading();
                }
            );
        }
    };

    useEffect(() => {
        reload();
    }, []);

    const getRowDataSafely = (pk) => {
        const mainProduct = repositionProduct.find((item) => item.pk === pk);
        if (mainProduct) {
            return mainProduct;
        }

        const findInDerivedProducts = (products) => {
            if (!products) return null;

            const foundProduct = products.find((product) => {
                if (product.pk === pk) {
                    return true;
                }

                if (product.derivedProducts && product.derivedProducts.length > 0) {
                    const found = findInDerivedProducts(product.derivedProducts);
                    if (found) return true;
                }
                return false;
            });

            if (foundProduct) {
                if (foundProduct.pk === pk) {
                    return foundProduct;
                }
                return findInDerivedProducts(foundProduct.derivedProducts);
            }

            return null;
        };

        const foundMainProduct = repositionProduct.find((mainProduct) => {
            if (mainProduct.derivedProducts && mainProduct.derivedProducts.length > 0) {
                const found = findInDerivedProducts(mainProduct.derivedProducts);
                return found !== null;
            }
            return false;
        });

        if (foundMainProduct) {
            return findInDerivedProducts(foundMainProduct.derivedProducts);
        }

        return {};
    };

    // Funciones y definiciones de columnas movidas a archivos modulares

    const simplifiedDerivedProductColumns = [
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '200px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '200px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => (
                    <Typography sx={{ whiteSpace: 'nowrap' }}>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'measure_default',
            label: 'PRESENTACIÓN',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <PresentationCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'c_reponer',
            label: 'C.REPONER',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '120px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '120px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <CReponerDerivedProductCell value={value} tableMeta={tableMeta} productId={productId} showMeasure />;
                }
            }
        },
        {
            name: 'waste_info',
            label: 'MERMA %',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => {
                    const wasteInfo = value;

                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {
                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;
                    }

                    const percentage = parseFloat(wasteInfo.waste_percentage_total);

                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;
                }
            }
        },
        {
            name: 'gross_amount',
            label: 'REPONER + MERMA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DisplayUnitValueCell value={value} tableMeta={tableMeta} productId={productId} showMeasure />;
                }
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK TIENDAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DisplayUnitValueCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'suma_netos_analisis',
            label: 'SUMA REPONER',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <SumaNetosAnalisisCell value={value} tableMeta={tableMeta} productId={productId} />;
                }
            }
        },
        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DisplayUnitValueCell value={value} productId={productId} />;
                }
            }
        },

        {
            name: 'calculated_neto',
            label: 'REPONER',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DisplayUnitValueCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'measure_name',
            label: 'PRES MIN',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        }
    ];

    // Simplified columns for non-derived products (without waste info)
    const simplifiedNonDerivedProductColumns = [
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
                setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '350px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '350px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => (
                    <Typography sx={{ whiteSpace: 'nowrap' }}>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'measure_default',
            label: 'PRESENTACIÓN',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <PresentationCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DirectFormattedProjectionCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK EN TIENDAS',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <StockTiendasCell value={value} tableMeta={tableMeta} productId={productId} />;
                }
            }
        },

        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DirectFormattedStockCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'neto',
            label: 'REPONER',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <NetoRawMaterialCell value={value} tableMeta={tableMeta} productId={productId} />;
                }
            }
        },
        {
            name: 'measure_name',
            label: 'PRES MIN',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        }
    ];

    // Complete analysis columns for products WITHOUT sub-derived products (all columns)
    const derivedAnalysisColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'product_id',
            label: 'PRODUCT_ID',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'presentations',
            label: 'PRESENTATIONS',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'equivalence_default',
            label: 'EQUIVALENCE_DEFAULT',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'store_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'store_name',
            label: 'TIENDA',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => {
                    const cleanName = value
                        ? value
                              .replace(/^Tienda\s*/i, '')
                              .replace(/\s*-\s*/g, ' ')
                              .trim()
                        : value;
                    return <Typography sx={{ fontWeight: 'bold' }}>{cleanName}</Typography>;
                }
            }
        },
        {
            name: 'measure_default',
            label: 'PRESENTACIÓN',
            options: {
                filter: true,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[1]; // product_id está en columna 1
                    return <PresentationCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => <AnalysisFormattedStockCell value={value} tableMeta={tableMeta} />
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value, tableMeta) => <AnalysisFormattedStockCell value={value} tableMeta={tableMeta} />
            }
        },
        {
            name: 'transformar',
            label: 'TRANSFORMAR',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <DirectFormattedStockCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'neto',
            label: 'REPONER',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[0];
                    return <NetoCell value={value} productId={productId} />;
                }
            }
        },
        {
            name: 'pres_min',
            label: 'PRES MIN',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        }
    ];

    return (
        <Box sx={{ display: 'flex', gap: 1, flexDirection: 'row', width: '100%', justifyContent: 'center' }}>
            <NestedCard
                width="90%"
                sx={{
                    '& .MuiTable-root': {
                        width: '100% !important',
                        tableLayout: 'fixed'
                    },
                    '& .MuiTableCell-root': {
                        padding: '8px 16px'
                    }
                }}
            >
                {/* Grid para productos derivados CON sub-derivados */}
                {derivedWithSubProducts.length > 0 && (
                    <NestedGrid
                        columns={simplifiedDerivedProductColumns}
                        data={derivedWithSubProducts.map((item, index) => ({
                            ...item,
                            pk: item.product_id,
                            globalIndex: index
                        }))}
                        title={
                            <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Productos Derivados (Con Sub-derivados)</Typography>
                        }
                        RenderNestedContent={(props) => (
                            <DerivedProductNestedContent
                                {...props}
                                data={data}
                                derivedAnalysisColumns={analisysDerivedProducts}
                                simpleAnalysisColumns={analisysDerivedProducts}
                                completeAnalysisColumns={derivedAnalysisColumns}
                                simplifiedDerivedProductColumns={simplifiedDerivedProductColumns}
                            />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            pagination: false,
                            toolbar: false
                        }}
                    />
                )}

                {/* Grid para productos derivados SIN sub-derivados */}
                {derivedWithoutSubProducts.length > 0 && (
                    <NestedGrid
                        columns={simplifiedNonDerivedProductColumns}
                        data={derivedWithoutSubProducts.map((item, index) => ({
                            ...item,
                            pk: item.product_id,
                            globalIndex: index + derivedWithSubProducts.length
                        }))}
                        title={
                            <Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Productos Derivados (Sin Sub-derivados)</Typography>
                        }
                        RenderNestedContent={(props) => (
                            <SimpleAnalysisNestedContent {...props} data={data} analisysDerivedProducts={derivedAnalysisColumns} />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            pagination: false,
                            toolbar: false
                        }}
                    />
                )}

                {/* Mostrar producto principal si no hay derivados */}
                {derivedProducts.length === 0 && (
                    <NestedGrid
                        columns={simplifiedNonDerivedProductColumns}
                        data={displayProducts}
                        title={<Typography sx={{ fontSize: '1.2rem', fontWeight: 'bold' }}>Producto Principal</Typography>}
                        RenderNestedContent={(props) => (
                            <SimpleAnalysisNestedContent {...props} data={data} analisysDerivedProducts={derivedAnalysisColumns} />
                        )}
                        options={{
                            search: false,
                            download: false,
                            print: false,
                            sort: false,
                            viewColumns: true,
                            filter: false,
                            filterType: 'multiselect',
                            responsive: 'vertical',
                            fixedHeader: true,
                            fixedSelectColumn: true,
                            jumpToPage: false,
                            resizableColumns: false,
                            draggableColumns: {
                                enabled: true
                            },
                            serverSide: true,
                            selectableRows: 'none',
                            pagination: false,
                            toolbar: false
                        }}
                    />
                )}
            </NestedCard>

            {loading && <BlockLoader />}
        </Box>
    );
};

export default RawMaterialRotationDetail;
