/**
 * Función para formatear cantidades con equivalencias
 * @param {number} quantity - Cantidad a formatear
 * @param {object} productData - Datos del producto con presentations y equivalence_default
 * @returns {object|null} Objeto con formatted_text, main_quantity y main_unit
 */
export const formatQuantityWithEquivalence = (quantity, productData) => {
    if (!productData || quantity === null || quantity === undefined) return null;

    const equivalenceDefault = productData.equivalence_default || 1;

    let targetMeasureName = productData.measure_name;
    let unitMeasureName = 'GR'; // fallback por defecto

    // Buscar la unidad base (equivalencia 1) en presentations
    if (productData.presentations) {
        const basePresentation = Object.values(productData.presentations).find((p) => parseFloat(p.equivalence) === 1);
        if (basePresentation?.measure_name) {
            unitMeasureName = basePresentation.measure_name;
        } else {
            // Si no se encuentra, intentar buscar por key "1.00"
            const baseKey = '1.00';
            if (productData.presentations[baseKey]?.measure_name) {
                unitMeasureName = productData.presentations[baseKey].measure_name;
            }
        }
    }

    if (productData.presentations && equivalenceDefault) {
        const equivalenceKey = parseFloat(equivalenceDefault).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        let targetPresentation = productData.presentations[equivalenceKey];

        if (!targetPresentation) {
            const simpleKey = parseFloat(equivalenceDefault).toFixed(2);
            targetPresentation = productData.presentations[simpleKey];
        }

        if (targetPresentation?.measure_name) {
            // Extraer solo la parte del label antes del * (ej: "UNI.*200" -> "UNI")
            const fullMeasureName = targetPresentation.measure_name;
            targetMeasureName = fullMeasureName.split('*')[0].replace(/\.$/, '') || fullMeasureName;
        }
    }

    if (equivalenceDefault <= 1) {
        return {
            formatted_text: `${parseFloat(quantity).toFixed(2)} ${targetMeasureName}`,
            main_quantity: quantity,
            main_unit: targetMeasureName
        };
    }

    const mainQuantity = Math.floor(quantity / equivalenceDefault);
    const remainder = quantity % equivalenceDefault;

    let formattedText = '';
    if (mainQuantity > 0) {
        formattedText = `${mainQuantity} ${targetMeasureName}`;
    }

    if (remainder > 0) {
        if (mainQuantity > 0) {
            formattedText += ' + ';
        }
        formattedText += `${remainder.toFixed(2)} ${unitMeasureName}`;
    }

    if (mainQuantity === 0 && remainder === 0) {
        formattedText = `0 ${targetMeasureName}`;
    }

    return {
        formatted_text: formattedText,
        main_quantity: mainQuantity,
        main_unit: targetMeasureName
    };
};
