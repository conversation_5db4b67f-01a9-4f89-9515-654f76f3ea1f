const EXCEL = '/excel';
const PDF = '/pdf';

const api = '/api';
const v1 = '/V1';
const apiV1 = `${api}${v1}`;
export const permisionsEndpoint = `${apiV1}/page/permissions`;
export const exchangeEndpoint = `${apiV1}/exchange-rate`;
export const merchandiseEndpoint = `${apiV1}/logistic/merchandise`;
export const recipeEndpoint = `${apiV1}/restaurant/recipe`;

export const businesUnitEndpoint = `${apiV1}/businessUnit`;
export const storeEndpoint = `${apiV1}/store`;
export const projectEndpoint = `${apiV1}/project`;
export const divisionEndpoint = `${apiV1}/division`;
export const lineEndpoint = `${apiV1}/line`;
export const sublineEndpoint = `${apiV1}/subline`;
export const categoryEndpoint = `${apiV1}/category`;
export const markEndpoint = `${apiV1}/mark`;
export const providerEndpoint = `${apiV1}/provider`;
export const logisticEndpoint = `${apiV1}/logistic`;
export const financialEndpoint = `${apiV1}/financial`;
export const warehouseEndpoint = `${apiV1}/warehouse`;
export const serverHourEndpoint = `${apiV1}/hour`;
export const administrationEndpoint = `${apiV1}/administration`;

export const warehouseItemsEndpoint = `${warehouseEndpoint}/items`;
export const financialDocumentsEndpoint = `${financialEndpoint}/documents`;
export const cashboxEndpoint = `${financialEndpoint}/cashbox`;
export const financialDocumentsPersonEndpoint = `${financialDocumentsEndpoint}/person`;
export const requestPaymentPersonsEndpoint = `${financialDocumentsPersonEndpoint}/request-payment`;
export const bussinessPartnersEndpoint = `${financialDocumentsPersonEndpoint}/business-partner`;

export const feePayEndpoint = `${financialDocumentsEndpoint}/feePay`;

export const repositionEndpoint = `${logisticEndpoint}/reposition`;
export const rotationEndpoint = `${logisticEndpoint}/rotation`;

export const repositionByProductEndpoint = `${repositionEndpoint}/product`;
export const exportRepositionEndpoint = `${repositionEndpoint}/export`;

export const exportOcExcelRepositionEndpoint = `${repositionEndpoint}/oc/export${EXCEL}`;
export const exportOtaExcelRepositionEndpoint = `${repositionEndpoint}/ota/export${EXCEL}`;
export const exportOcPdfRepositionEndpoint = `${repositionEndpoint}/oc/export${PDF}`;
export const exportOtaPdfRepositionEndpoint = `${repositionEndpoint}/ota/export${PDF}`;

export const productionCostEndpoint = '/api/V1/restaurant/production-cost';
export const productionCostConceptEndpoint = `${productionCostEndpoint}/concept`;

export const foodSalesEndpoint = '/api/V1/restaurant/food-sale';
export const foodSalesReportEndpoint = `${foodSalesEndpoint}/report`;

export const supplyTransformationEndpoint = `${apiV1}/logistic/supply-transformation`;
export const supplyTransformationDocumentsEndpoint = `${supplyTransformationEndpoint}/documents`;
export const getDocumentToTransformationEndpoint = (id) => `${supplyTransformationDocumentsEndpoint}/${id}`;

export const linkProductsEndpoint = `${apiV1}/logistic/link-products`;
export const itemsLinkProductsEndpoint = `${linkProductsEndpoint}/items`;
export const parentItemsLinkProductsEndpoint = `${itemsLinkProductsEndpoint}/parents`;
export const childrendItemsLinkProductsEndpoint = `${itemsLinkProductsEndpoint}/childrens`;
export const getlinkProductByIDEndpoint = (id) => `${linkProductsEndpoint}/${id}`;

export const movementsEndpoint = `${apiV1}/movements`;
export const getMovementsReferencesEndpoint = (id) => `${movementsEndpoint}/references/${id}`;

export const reportSupplyStocksEndpoint = `${apiV1}/logistic/reports/supply-stocks`;

export const foodMerchandiseEndpoint = `${merchandiseEndpoint}/food`;
export const supplyMerchandiseEndpoint = `${merchandiseEndpoint}/supply`;
export const merchandisePresentationsEndpoint = `${merchandiseEndpoint}/presentations`;

export const salesDashboardEndpoint = `${apiV1}/commercial/sales-dashboard`;
export const salesDashboardGoalsEndpoint = `${apiV1}/commercial/progress-goals`;

export const administrationGroupsEndpoint = `${administrationEndpoint}/group`;
