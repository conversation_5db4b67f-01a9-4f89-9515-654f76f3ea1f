import React, { useEffect, useState, useCallback, useMemo } from 'react';
import {
    Alert,
    Box,
    Button,
    MenuItem,
    Modal,
    TextField,
    FormControl,
    InputLabel,
    Select,
    ListItemText,
    OutlinedInput
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import { parseDataToSearchString, parseDateToLocaleString2 } from 'utils/dates';
import { DateRangePicker } from 'mui-daterange-picker';
import { definedRanges } from 'ui-component/extended/DateRangeSelector';
import esLocale from 'date-fns/locale/es';
import useModal from 'hooks/useModal';
import useFilters from 'hooks/useFilters';
import { STATE_CANCELED, STATE_CLOSED, STATE_OPEN, STATE_PARTIAL_OPEN } from 'store/slices/payment-schedule/paymentSchedule';

function CustomDateRangeSelector({ open, handleOpen, handleClose, toggle, dateRange, setDateRange, label, name }) {
    const [pickerKey, setPickerKey] = useState(Date.now());
    const [dateText, setDateText] = useState('');

    useEffect(() => {
        if (dateRange !== null) {
            const foundLabel = definedRanges.find(
                (range) =>
                    range.startDate.getTime() === dateRange.startDate.getTime() && range.endDate.getTime() === dateRange.endDate.getTime()
            );

            if (foundLabel) {
                setDateText(foundLabel.label);
            } else {
                setDateText(`${parseDateToLocaleString2(dateRange.startDate)} - ${parseDateToLocaleString2(dateRange.endDate)} `);
            }
        } else {
            setDateText('');
        }
    }, [dateRange]);

    const handleClear = useCallback(() => {
        setDateRange(name, null);
        setPickerKey(Date.now());
    }, [setDateRange, name]);

    const handleDateChange = useCallback(
        (range) => {
            setDateRange(name, range);
        },
        [setDateRange, name]
    );

    return (
        <>
            <TextField
                className="input-label-date-selector"
                onClick={handleOpen}
                value={dateText}
                label={label}
                InputProps={{ readOnly: true }}
                onKeyDown={(event) => {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        const form = event.target.closest('form');
                        if (form) {
                            form.requestSubmit();
                        }
                    }
                }}
            />
            <Modal open={open} onClose={handleClose}>
                <div
                    style={{
                        display: 'flex',
                        flexDirection: 'column',
                        justifyContent: 'center',
                        alignItems: 'center',
                        width: '100%',
                        height: '100%',
                        p: 2,
                        boxSizing: 'border-box',
                        bgcolor: 'rgba(0,0,0,0.3)'
                    }}
                >
                    <div
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            width: { xs: '100%', sm: '80%', md: '50%' },
                            bgcolor: 'background.paper',
                            borderRadius: 2,
                            boxShadow: 24,
                            p: 2
                        }}
                    >
                        <DateRangePicker
                            locale={esLocale}
                            definedRanges={definedRanges}
                            initialDateRange={dateRange}
                            open={open}
                            toggle={toggle}
                            key={pickerKey}
                            onChange={handleDateChange}
                        />
                        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', sm: 'row' }, gap: 1, mt: 2 }}>
                            <Button variant="contained" onClick={handleClear} fullWidth>
                                Limpiar
                            </Button>
                            <Button variant="contained" color="success" onClick={handleClose} fullWidth>
                                Cerrar
                            </Button>
                        </Box>
                    </div>
                </div>
            </Modal>
        </>
    );
}

export default function PaymentScheduleFilter({ setFilters, handleSearch, disable = false }) {
    const [open, handleOpen, handleClose, toggle] = useModal();
    const processFilters = useCallback((filters) => {
        const processedFilters = {};

        if (filters.dateRangeEmision) {
            processedFilters.startDateEmission = parseDataToSearchString(filters.dateRangeEmision.startDate);
            processedFilters.endDateEmission = parseDataToSearchString(filters.dateRangeEmision.endDate);
        }

        if (filters.status && filters.status.length >= 0) {
            processedFilters.status = filters.status;
        }

        return processedFilters;
    }, []);

    const [dataFilters, setValue] = useFilters(
        {
            dateRangeEmision: null,
            status: [STATE_OPEN, STATE_PARTIAL_OPEN]
        },
        (filters) => setFilters(processFilters(filters))
    );

    const statusOptions = useMemo(
        () => [
            { value: STATE_OPEN, label: 'ACTIVO' },
            { value: STATE_PARTIAL_OPEN, label: 'PARCIALMENTE ACTIVO' },
            { value: STATE_CLOSED, label: 'PAGADO' },
            { value: STATE_CANCELED, label: 'ELIMINADO' }
        ],
        []
    );

    const handleSubmit = useCallback(
        (event) => {
            event.preventDefault();
            handleSearch();
        },
        [handleSearch]
    );

    const handleStatusChange = useCallback(
        (event) => {
            setValue('status', event.target.value);
        },
        [setValue]
    );

    const handleSelectKeyDown = useCallback(
        (event) => {
            if (event.key === 'Enter') {
                event.preventDefault();
                handleSearch();
            }
        },
        [handleSearch]
    );



    return (
        <form onSubmit={handleSubmit}>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    flexDirection: { xs: 'column', sm: 'row' },
                    paddingBottom: 0
                }}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexWrap: 'wrap',
                        flexDirection: { xs: 'column', sm: 'row' },
                        width: '100%',
                        gap: 1
                    }}
                >
                    <CustomDateRangeSelector
                        open={open}
                        handleOpen={handleOpen}
                        handleClose={handleClose}
                        toggle={toggle}
                        dateRange={dataFilters.dateRangeEmision}
                        label="Fecha de Emisión"
                        setDateRange={setValue}
                        name="dateRangeEmision"
                    />
                    <FormControl
                        sx={{
                            width: { xs: '100%', sm: '15rem', md: '18rem' },
                            flexShrink: 0
                        }}
                        variant="outlined"
                        fullWidth
                    >
                        <InputLabel id="status-label">Estado</InputLabel>
                        <Select
                            labelId="status-label"
                            id="status-select"
                            multiple
                            value={dataFilters.status}
                            onChange={handleStatusChange}
                            onKeyDown={handleSelectKeyDown}
                            input={<OutlinedInput label="Estado" />}
                            label="Estado"
                            renderValue={(selected) =>
                                selected
                                    .map((value) => {
                                        const option = statusOptions.find((opt) => opt.value === value);
                                        return option ? option.label : value;
                                    })
                                    .join(', ')
                            }
                        >
                            {statusOptions.map((option) => (
                                <MenuItem key={option.value} value={option.value}>
                                    <ListItemText primary={option.label} />
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Box>
                <Button
                    type="submit"
                    variant="contained"
                    color="success"
                    startIcon={<SearchIcon />}
                    disabled={disable}
                    sx={{
                        width: { xs: '100%', sm: 'auto' },
                        mt: { xs: 1, sm: 0 }
                    }}
                >
                    Buscar
                </Button>
            </Box>
        </form>
    );
}
