/* eslint-disable */
import React, { useEffect, useCallback } from 'react';
import {
    TextField,
    Button,
    Slider,
    Grid,
    Tooltip,
    FormControl,
    InputLabel,
    Select,
    MenuItem,
    Typography,
    Box,
    FormLabel,
    FormControlLabel,
    Switch,
    Stack,
    Checkbox,
    ListItemText
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { RestartAlt } from '@mui/icons-material';
import dayjs from 'dayjs';
import SearchIcon from '@mui/icons-material/Search';
import 'dayjs/locale/es';
import ToggleButton from 'ui-component/inputs/ToggleButton';
import { GROUP_DIVISION, GROUP_LINE, GROUP_PRODUCT, GROUP_SUBLINE, PRODUCT_TYPE_MERCHANDISE, PRODUCT_TYPE_FOOD } from 'utils/product_types';
import { ClasificationFilter } from 'ui-component/filters/ClasificationFilter';
import { CategoryFilter } from 'ui-component/filters/CategoryFilter';
import useModal from 'hooks/useModal';
import useLoading from 'hooks/useLoading';
import { useDispatch } from 'store';
import { clearSlice as clearDivisionSlice } from 'store/slices/division/division';
import { clearSlice as clearSublineSlice } from 'store/slices/subline/subline';
import { clearSlice as clearLineSlice } from 'store/slices/line/line';

dayjs.locale('es-pe');

export default function FormControls({
    startDate,
    setStartDate,
    endDate,
    setEndDate,
    limit,
    setLimit,
    selectedStores,
    setSelectedStores,
    projectionFactor,
    handleProjectionChange,
    resetProjection,
    fetchSalesData,
    isLoading,
    stores,
    productType,
    setProductType,
    division,
    setDivision,
    line,
    setLine,
    subline,
    setSubline,
    showProyection,
    openProyection,
    closeProyection,
    displayValue,
    setDisplayValue,
    productIds,
    group,
    setGroup,
    setProductIds,
    orderBy,
    setOrderBy,
    onlyTerrace,
    setOnlyTerrace
}) {
    const dispatch = useDispatch();

    const clearMerchandiseClasification = useCallback(() => {
        setDivision([]);
        setLine([]);
        setSubline([]);
    }, [setDivision, setLine, setSubline]);

    const clearSlices = useCallback(() => {
        dispatch(clearSublineSlice());
        dispatch(clearLineSlice());
        dispatch(clearDivisionSlice());
    }, [dispatch]);

    const [showIdFilter, openIdFilter, closeIdFilter] = useModal(false);

    const [allowDivision, showDivision, hideDivision] = useLoading(true);
    const [allowLine, showLine, hideLine] = useLoading(true);
    const [allowSubline, showSubline, hideSubline] = useLoading(true);

    const setFilterClasification = (name, value) => {
        switch (name) {
            case 'division':
                setDivision([...value]);
                break;
            case 'line':
                setLine([...value]);
                break;
            case 'subline':
                setSubline([...value]);
                break;
        }
    };

    const handleGroup = (value) => {
        switch (value) {
            case GROUP_DIVISION:
                hideDivision();
                hideLine();
                hideSubline();
                break;
            case GROUP_LINE:
                showDivision();
                hideLine();
                hideSubline();
                break;
            case GROUP_SUBLINE:
                showDivision();
                showLine();
                hideSubline();
                break;
            default:
                showDivision();
                showLine();
                showSubline();
                break;
        }
        clearMerchandiseClasification();
        setGroup(value);
    };

    useEffect(() => {
        if (showIdFilter === false) {
            setProductIds('');
        }
    }, [showIdFilter]);

    useEffect(() => {
        showDivision();
        showLine();
        showSubline();
        clearMerchandiseClasification();
        clearSlices();
        setGroup(GROUP_PRODUCT);
    }, [productType]);

    const handleChange = useCallback((event) => {
        const value = event.target.value;

        if (value.includes('all')) {
            if (selectedStores.length === stores.length) {
                return;
            } else {
                setSelectedStores(stores.map((store) => store.store_id));
            }
        } else {
            if (value.length === 0) {
                return;
            }
            setSelectedStores(value);
        }
    }, [selectedStores.length, stores, setSelectedStores]);

    const isAllSelected = selectedStores.length === stores.length;

    const handleSubmit = useCallback((event) => {
        event.preventDefault();
        fetchSalesData();
    }, [fetchSalesData]);

    const handleLimitChange = useCallback((e) => setLimit(e.target.value), [setLimit]);
    const handleOrderByChange = useCallback(({ target: { value } }) => setOrderBy(value), [setOrderBy]);
    const handleProductIdsChange = useCallback((e) => setProductIds(e.target.value), [setProductIds]);
    const handleProductTypeChange = useCallback((value) => setProductType(value), [setProductType]);

    const handleProjectionToggle = useCallback(() => {
        showProyection ? closeProyection() : openProyection();
    }, [showProyection, closeProyection, openProyection]);

    const handleIdFilterToggle = useCallback(() => {
        showIdFilter ? closeIdFilter() : openIdFilter();
    }, [showIdFilter, closeIdFilter, openIdFilter]);

    const handleDisplayValueToggle = useCallback(() => {
        setDisplayValue(!displayValue);
    }, [displayValue, setDisplayValue]);

    const handleOnlyTerraceToggle = useCallback(() => {
        setOnlyTerrace(onlyTerrace === 1 ? 0 : 1);
    }, [onlyTerrace, setOnlyTerrace]);

    return (
        <form onSubmit={handleSubmit}>
            <Box sx={{ diplay: 'flex', flexDirection: 'row', gap: 2 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3 }}>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={12} md={2}>
                            <ToggleButton
                                label="Tipo de Producto"
                                items={[
                                    { label: 'Food', value: PRODUCT_TYPE_FOOD },
                                    { label: 'Market', value: PRODUCT_TYPE_MERCHANDISE }
                                ]}
                                value={productType}
                                setValue={handleProductTypeChange}
                            />
                        </Grid>
                        <Grid item xs={12} sm={12} md={9}>
                            <ToggleButton
                                label="Agrupado por"
                                items={
                                    productType === PRODUCT_TYPE_FOOD
                                        ? [
                                              { label: 'Linea', value: GROUP_LINE },
                                              { label: 'Sublinea', value: GROUP_SUBLINE },
                                              { label: 'Producto', value: GROUP_PRODUCT }
                                          ]
                                        : [
                                              { label: 'Division', value: GROUP_DIVISION },
                                              { label: 'Linea', value: GROUP_LINE },
                                              { label: 'Sublinea', value: GROUP_SUBLINE },
                                              { label: 'Producto', value: GROUP_PRODUCT }
                                          ]
                                }
                                value={group}
                                setValue={(value) => handleGroup(value)}
                            />
                        </Grid>
                        <Grid item xs={12} sm={12} md={1}>
                            <Button
                                type="submit"
                                variant="contained"
                                color="success"
                                disabled={isLoading}
                                sx={{ height: '100%' }}
                                fullWidth
                            >
                                {isLoading ? 'Cargando...' : <SearchIcon />}
                            </Button>
                        </Grid>
                    </Grid>

                    <Box>
                        <Box sx={{ pb: 1 }}>
                            <FormLabel>Clasificación</FormLabel>
                        </Box>
                        {productType === PRODUCT_TYPE_MERCHANDISE ? (
                            <ClasificationFilter
                                setValue={setFilterClasification}
                                dataFilters={{ division, line, subline }}
                                showDivision={allowDivision}
                                showLine={allowLine}
                                showSubline={allowSubline}
                                grid
                            />
                        ) : (
                            <CategoryFilter
                                setValue={setFilterClasification}
                                dataFilters={{ line, subline }}
                                showLine={allowLine}
                                showSubline={allowSubline}
                                grid
                            />
                        )}
                    </Box>
                    <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={3}>
                            <DatePicker
                                label="Fecha Inicio"
                                value={startDate}
                                onChange={(newValue) => setStartDate(newValue)}
                                renderInput={(params) => <TextField {...params} fullWidth />}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <DatePicker
                                label="Fecha Fin"
                                value={endDate}
                                onChange={(newValue) => setEndDate(newValue)}
                                renderInput={(params) => <TextField {...params} fullWidth />}
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={1}>
                            <TextField
                                label="Top"
                                type="number"
                                value={limit}
                                onChange={handleLimitChange}
                                InputProps={{ inputProps: { min: 1 } }}
                                fullWidth
                            />
                        </Grid>
                        <Grid item xs={12} sm={6} md={2}>
                            <FormControl fullWidth>
                                <InputLabel>Ordenar por</InputLabel>
                                <Select value={orderBy} label="Tienda" onChange={handleOrderByChange}>
                                    <MenuItem value={1}>Cantidad</MenuItem>
                                    <MenuItem value={0}>Monto</MenuItem>
                                </Select>
                            </FormControl>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                            <FormControl fullWidth>
                                <InputLabel>Tienda</InputLabel>
                                <Select
                                    multiple
                                    value={selectedStores}
                                    label="Tienda"
                                    onChange={handleChange}
                                    renderValue={(selected) => {
                                        if (selected.length === stores.length) return 'Todas las tiendas';
                                        const selectedNames = stores
                                            .filter((store) => selected.includes(store.store_id))
                                            .map((store) => store.store_name);
                                        return selectedNames.join(', ');
                                    }}
                                >
                                    <MenuItem value="all">
                                        <Checkbox checked={isAllSelected} />
                                        <ListItemText primary="Todas las tiendas" />
                                    </MenuItem>
                                    {stores.map((store) => (
                                        <MenuItem key={store.store_id} value={store.store_id}>
                                            <Checkbox checked={selectedStores.includes(store.store_id)} />
                                            <ListItemText primary={store.store_name} />
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid item xs={12}>
                            <Grid container spacing={2}>
                                <Grid item xs={12} sm={6} md={2}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={showProyection}
                                                onChange={handleProjectionToggle}
                                            />
                                        }
                                        label="Proyectar"
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={2}>
                                    <FormControlLabel
                                        control={
                                            <Switch
                                                checked={showIdFilter}
                                                onChange={handleIdFilterToggle}
                                            />
                                        }
                                        label="Filtro de ID's"
                                    />
                                </Grid>
                                <Grid item xs={12} sm={6} md={2}>
                                    <Stack direction="row" spacing={1} sx={{ alignItems: 'center' }}>
                                        <Typography>Cantidad</Typography>
                                        <Switch
                                            checked={displayValue}
                                            onChange={handleDisplayValueToggle}
                                        />
                                        <Typography>Monto</Typography>
                                    </Stack>
                                </Grid>
                                <Grid item xs={12} sm={6} md={2}>
                                    <Stack direction="row" spacing={1} sx={{ alignItems: 'center' }}>
                                        <Switch
                                            checked={onlyTerrace === 1}
                                            onChange={handleOnlyTerraceToggle}
                                        />
                                        <Typography>Solo Terraza</Typography>
                                    </Stack>
                                </Grid>
                                <Grid item xs={12} sm={6} md={10}>
                                    {showIdFilter ? (
                                        <TextField
                                            label="ID's"
                                            type="text"
                                            value={productIds}
                                            onChange={handleProductIdsChange}
                                            fullWidth
                                        />
                                    ) : null}
                                </Grid>
                            </Grid>
                        </Grid>

                        <Grid item xs={12}>
                            {showProyection ? (
                                <>
                                    <Box sx={{ mt: 2, mb: 1 }}>
                                        <Typography variant="subtitle1" gutterBottom>
                                            Factor de Proyección: {projectionFactor}%
                                        </Typography>
                                    </Box>

                                    <Grid sx={{ my: 2 }} container spacing={3} alignItems="center">
                                        <Grid item xs>
                                            <Slider
                                                value={projectionFactor}
                                                onChange={handleProjectionChange}
                                                aria-labelledby="projection-factor-slider"
                                                valueLabelDisplay="auto"
                                                step={1}
                                                marks={[
                                                    { value: 0, label: '0%' },
                                                    { value: 50, label: '50%' },
                                                    { value: 100, label: '100%' },
                                                    { value: 150, label: '150%' },
                                                    { value: 200, label: '200%' }
                                                ]}
                                                min={0}
                                                max={200}
                                                sx={{
                                                    '& .MuiSlider-valueLabel': {
                                                        backgroundColor: 'primary.main'
                                                    }
                                                }}
                                            />
                                        </Grid>
                                        <Grid item sx={{ ml: 2 }}>
                                            <Tooltip title="Restablecer Proyección">
                                                <Button variant="outlined" onClick={resetProjection} startIcon={<RestartAlt />}>
                                                    Restablecer
                                                </Button>
                                            </Tooltip>
                                        </Grid>
                                    </Grid>
                                </>
                            ) : null}
                        </Grid>
                    </Grid>
                </Box>
            </Box>
        </form>
    );
}
