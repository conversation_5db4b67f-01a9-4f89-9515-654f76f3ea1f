/* eslint-disable */
import React, { useState, useEffect, useRef } from 'react';
import {
    Box,
    Button,
    ButtonGroup,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Typography,
    ToggleButton,
    ToggleButtonGroup,
    Menu,
    MenuItem,
    TextField,
    IconButton,
    Autocomplete,
    CircularProgress,
    Backdrop,
    Tooltip as MuiTooltip,
    Accordion,
    AccordionSummary,
    AccordionDetails
} from '@mui/material';
import { PieChart, Pie, Cell, BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Tooltip, Legend } from 'recharts';
import axios from 'axios';
import { getSalesDashboardPromise, getSalesDashboardGoalsPromise } from 'services/salesDashboard';
import { ParticipacionesSection } from './stores/ParticipacionesSection';
import { ParticipacionesTitle } from './stores/ParticipacionesTitle';
import { StyledTableCell } from './stores/StyledTableCell';
import { StyledTableRow } from './stores/StyledTableRow';
import { TotalRow } from './stores/TotalRow';
import { DivisionRow } from './stores/DivisionRow';
import { LineDetailsTable } from './stores/LineDetailsTable';
import { ChartsContainer } from './stores/ChartsContainer';
import { ChartContainer } from './stores/ChartContainer';
import { FiltersWrapper } from './stores/FiltersWrapper';
import { ChartTitle } from './stores/ChartTitle';
import { FilterGroup } from './stores/FilterGroup';
import { FilterChip } from './stores/FilterChip';
import { CompactDatePicker } from './stores/CompactDatePicker';
import { TimeSelect } from './stores/TimeSelect';
import { StoreSelect } from './stores/StoreSelect';
import { HeatMapLegend } from './stores/HeatMapLegend';
import { getHeatMapColor, getHeatMapColors } from './stores/functions';
import { COLORS } from './stores/colors';
import { es } from 'date-fns/locale';
import FilterListIcon from '@mui/icons-material/FilterList';
import DownloadIcon from '@mui/icons-material/Download';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';
import UnfoldLessIcon from '@mui/icons-material/UnfoldLess';
import * as XLSX from 'xlsx';
import AIChat from './AIChat';
import { format } from 'date-fns';
import CalendarViewDayIcon from '@mui/icons-material/CalendarViewDay';
import CalendarViewWeekIcon from '@mui/icons-material/CalendarViewWeek';

const StoresTab = () => {
    const [salesData, setSalesData] = useState([]);
    const [goalsData, setGoalsData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [selectedStore, setSelectedStore] = useState('all');
    const [selectedLineNames, setSelectedLineNames] = useState([]);
    const [selectedRealLineNames, setSelectedRealLineNames] = useState([]);
    const [dateRange, setDateRange] = useState([null, null]);
    const [startTime, setStartTime] = useState('00:00');
    const [endTime, setEndTime] = useState('23:59');
    const [availableDates, setAvailableDates] = useState([]);
    const [apiMode, setApiMode] = useState(0);
    const [apiModeGoals, setApiModeGoals] = useState(0);
    const [viewMode, setViewMode] = useState('daily');
    const [filterAnchorEl, setFilterAnchorEl] = useState(null);
    const [availableLineNames, setAvailableLineNames] = useState([]);
    const [availableRealLineNames, setAvailableRealLineNames] = useState([]);

    // Estados para acordeones
    const [expandedDivisions, setExpandedDivisions] = useState({});
    const [expandedLines, setExpandedLines] = useState({});
    // Referencia para almacenar la estructura de datos procesada sin recalcular
    const processedDataRef = useRef(null);

    const hourChartRef = useRef(null);
    const divisionChartRef = useRef(null);
    const storeChartRef = useRef(null);
    const dayChartRef = useRef(null);

    useEffect(() => {
        const fetchGoalsData = async () => {
            setLoading(true);
            try {
                let response;
                if (apiModeGoals === 0) {
                    response = await getSalesDashboardGoalsPromise();
                    setGoalsData(response.data);
                }
            } catch (error) {
                console.error('Error fetching goals data:', error);
                if (apiModeGoals === 0) {
                    setApiModeGoals(1);
                }
            }
        };

        fetchGoalsData();
    }, [apiModeGoals]);

    useEffect(() => {
        const fetchSalesData = async () => {
            try {
                let response;
                let arrayData = [];
                if (apiMode === 0) {
                    response = await getSalesDashboardPromise();
                    console.log('Sales data from Api Enviroment:', response.data);
                    arrayData = response.data;
                    setSalesData(response.data);
                } else {
                    response = await axios.get('https://supermercadosmia.siansystem.com/admin/apiSian/pbi/sales');
                    console.log('Sales data from Prod:', response.data.data);
                    arrayData = response.data.data;
                    setSalesData(response.data.data);
                }
                // Procesar fechas únicas
                const dates = [...new Set(arrayData.map((sale) => sale.emission_date))].sort();
                setAvailableDates(dates);

                // Procesar divisiones únicas
                const lines = [...new Set(arrayData.map((sale) => sale.division_name))].sort();
                setAvailableLineNames(lines);

                // Procesar líneas reales únicas
                const realLines = [...new Set(arrayData.map((sale) => sale.line_name))].sort();
                setAvailableRealLineNames(realLines);

                // Establecer fecha inicial y final
                const today = new Date();
                today.setHours(12, 0, 0, 0);
                const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
                firstDayOfMonth.setHours(12, 0, 0, 0);
                setDateRange([firstDayOfMonth, today]);
            } catch (error) {
                console.error('Error fetching sales data:', error);
                if (apiMode === 0) {
                    setApiMode(1);
                }
            } finally {
                setTimeout(() => {
                    setLoading(false);
                }, 500);
            }
        };

        if (goalsData.length > 0) {
            fetchSalesData();
        }
    }, [apiMode, goalsData]);

    const handleFilterClick = (event) => {
        setFilterAnchorEl(event.currentTarget);
    };

    const handleFilterClose = () => {
        setFilterAnchorEl(null);
    };

    const handleDateRangePreset = (preset) => {
        const today = new Date();
        let start = new Date();
        let end = new Date();

        switch (preset) {
            case 'today':
                start = today;
                end = today;
                break;
            case 'yesterday':
                start.setDate(today.getDate() - 1);
                end = start;
                break;
            case 'last7':
                start.setDate(today.getDate() - 7);
                end = today;
                break;
            case 'last30':
                start.setDate(today.getDate() - 30);
                end = today;
                break;
            default:
                break;
        }

        setDateRange([start, end]);
        handleFilterClose();
    };

    const handleTimeChange = (type, event) => {
        const newValue = event.target.value;
        if (type === 'start') {
            setStartTime(newValue);
        } else {
            setEndTime(newValue);
        }

        // Convertir la hora para el filtrado
        const [hours, minutes] = newValue.split(':').map(Number);
        const timeValue = hours + minutes / 60;

        // Actualizar el filtrado aquí
    };

    const handleLineNameChange = (event, newValue) => {
        setSelectedLineNames(newValue);
    };

    const handleRealLineNameChange = (event, newValue) => {
        setSelectedRealLineNames(newValue);
    };

    const getFilteredData = () => {
        if (!dateRange[0] || !dateRange[1]) return [];

        return salesData.filter((sale) => {
            // Convertir la fecha de emisión a fecha local
            const saleDate = new Date(sale.emission_date + 'T00:00:00');
            const saleHour = new Date(sale.register_date).getHours();

            // Crear fechas de inicio y fin del día
            const startDate = new Date(dateRange[0]);
            startDate.setHours(0, 0, 0, 0);

            const endDate = new Date(dateRange[1]);
            endDate.setHours(23, 59, 59, 999);

            const storeMatch = selectedStore === 'all' || sale.store_name === selectedStore;
            const lineMatch = selectedLineNames.length === 0 || selectedLineNames.includes(sale.division_name);
            const realLineMatch = selectedRealLineNames.length === 0 || selectedRealLineNames.includes(sale.line_name);
            const timeMatch = saleHour >= parseInt(startTime.split(':')[0]) && saleHour <= parseInt(endTime.split(':')[0]);
            const dateMatch = saleDate >= startDate && saleDate <= endDate;

            return storeMatch && dateMatch && timeMatch && lineMatch && realLineMatch;
        });
    };

    const getFilteredDataWithoutStore = () => {
        if (!dateRange[0] || !dateRange[1]) return [];

        return salesData.filter((sale) => {
            // Convertir la fecha de emisión a fecha local
            const saleDate = new Date(sale.emission_date + 'T00:00:00');
            const saleHour = new Date(sale.register_date).getHours();

            // Crear fechas de inicio y fin del día
            const startDate = new Date(dateRange[0]);
            startDate.setHours(0, 0, 0, 0);

            const endDate = new Date(dateRange[1]);
            endDate.setHours(23, 59, 59, 999);

            const lineMatch = selectedLineNames.length === 0 || selectedLineNames.includes(sale.division_name);
            const realLineMatch = selectedRealLineNames.length === 0 || selectedRealLineNames.includes(sale.line_name);
            const timeMatch = saleHour >= parseInt(startTime.split(':')[0]) && saleHour <= parseInt(endTime.split(':')[0]);
            const dateMatch = saleDate >= startDate && saleDate <= endDate;

            return dateMatch && timeMatch && lineMatch && realLineMatch;
        });
    };

    // Función auxiliar para obtener el número de semana
    const getWeekNumber = (date) => {
        const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
        const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
        return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
    };

    const processData = () => {
        const filteredData = getFilteredData();
        const allFilteredData = getFilteredDataWithoutStore();
        const salesByStore = {};
        const allSalesByStore = {};
        const salesByLine = {};
        const salesByRealLine = {};
        const salesByHour = {};
        const salesByDay = {};
        const salesByWeek = {};
        // Nuevo objeto para mapear ventas por tienda y línea
        const salesByStoreAndLine = {};
        let totalSales = 0;

        // Obtener la fecha actual en formato YYYY-MM-DD
        const today = new Date().toISOString().split('T')[0];

        // Función para obtener número de semana ISO
        const getISOWeekNumber = (date) => {
            const d = new Date(date);
            d.setHours(0, 0, 0, 0);
            // Jueves de la semana actual
            d.setDate(d.getDate() + 4 - (d.getDay() || 7));
            // Primer día del año
            const yearStart = new Date(d.getFullYear(), 0, 1);
            // Calcular semana ISO
            return Math.ceil(((d - yearStart) / 86400000 + 1) / 7);
        };

        // Procesar datos filtrados para las métricas principales
        filteredData.forEach((sale) => {
            const saleDate = sale.emission_date;
            const saleTotal = parseFloat(sale.total);
            totalSales += saleTotal;

            // Procesar ventas por línea
            if (!salesByLine[sale.division_name]) {
                salesByLine[sale.division_name] = {
                    total: 0,
                    realLines: {}
                };
            }
            salesByLine[sale.division_name].total += saleTotal;

            if (!salesByLine[sale.division_name].realLines[sale.line_name]) {
                salesByLine[sale.division_name].realLines[sale.line_name] = {
                    total: 0,
                    sublines: {}
                };
            }
            salesByLine[sale.division_name].realLines[sale.line_name].total += saleTotal;

            // Agregar sublineas
            if (!salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name]) {
                salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name] = 0;
            }
            salesByLine[sale.division_name].realLines[sale.line_name].sublines[sale.subline_name] += saleTotal;

            // Crear estructura para ventas por tienda y línea
            if (!salesByStoreAndLine[sale.store_name]) {
                salesByStoreAndLine[sale.store_name] = {};
            }
            if (!salesByStoreAndLine[sale.store_name][sale.division_name]) {
                salesByStoreAndLine[sale.store_name][sale.division_name] = 0;
            }
            salesByStoreAndLine[sale.store_name][sale.division_name] += saleTotal;

            if (!salesByStore[sale.store_name]) {
                salesByStore[sale.store_name] = {
                    name: sale.store_name,
                    total: 0,
                    todaySales: 0
                };
            }
            salesByStore[sale.store_name].total += saleTotal;

            if (saleDate === today) {
                salesByStore[sale.store_name].todaySales += saleTotal;
            }

            const hour = new Date(sale.register_date).getHours();
            const hourKey = `${hour.toString().padStart(2, '0')}:00`;
            if (!salesByHour[hourKey]) {
                salesByHour[hourKey] = 0;
            }
            salesByHour[hourKey] += saleTotal;

            // Agregar ventas por día - Normalizamos el formato de fecha
            const formattedDate = saleDate.split('T')[0]; // Aseguramos formato YYYY-MM-DD
            if (!salesByDay[formattedDate]) {
                salesByDay[formattedDate] = 0;
            }
            salesByDay[formattedDate] += saleTotal;

            // Calcular la semana de la venta usando el estándar ISO 8601
            const dateParts = formattedDate.split('-');
            const year = parseInt(dateParts[0]);
            const month = parseInt(dateParts[1]) - 1; // Meses en JS son 0-11
            const day = parseInt(dateParts[2]);

            // Crear fecha con hora fija para evitar problemas de zona horaria
            const date = new Date(year, month, day, 12, 0, 0);
            const weekNumber = getISOWeekNumber(date);
            const yearWeek = `${year}-W${weekNumber.toString().padStart(2, '0')}`;

            if (!salesByWeek[yearWeek]) {
                salesByWeek[yearWeek] = 0;
            }
            salesByWeek[yearWeek] += saleTotal;
        });

        // Procesar datos sin filtro de tienda para el gráfico
        allFilteredData.forEach((sale) => {
            const saleDate = sale.emission_date;
            const saleTotal = parseFloat(sale.total);

            if (!allSalesByStore[sale.store_name]) {
                allSalesByStore[sale.store_name] = {
                    name: sale.store_name,
                    total: 0,
                    todaySales: 0
                };
            }
            allSalesByStore[sale.store_name].total += saleTotal;

            if (saleDate === today) {
                allSalesByStore[sale.store_name].todaySales += saleTotal;
            }
        });

        // Convertir y ordenar las Línea
        const sortedLines = Object.entries(salesByLine)
            .map(([name, data]) => ({
                name,
                value: data.total,
                percentage: (data.total / totalSales) * 100,
                realLines: Object.entries(data.realLines)
                    .map(([realName, lineData]) => ({
                        name: realName,
                        value: lineData.total,
                        percentage: (lineData.total / totalSales) * 100,
                        sublines: Object.entries(lineData.sublines)
                            .map(([sublineName, value]) => ({
                                name: sublineName,
                                value,
                                percentage: (value / totalSales) * 100
                            }))
                            .sort((a, b) => b.value - a.value)
                    }))
                    .sort((a, b) => b.value - a.value)
            }))
            .sort((a, b) => b.value - a.value);

        // Crear estructura detallada de tiendas con sus ventas por línea
        const storesArray = Object.values(salesByStore).map((store) => {
            const storeGoals = goalsData.find((g) => g.store_name === store.name) || {};
            const storeData = {
                name: store.name,
                total: store.total,
                todaySales: store.todaySales,
                monthlyGoal: parseFloat(storeGoals.period_goal),
                dailyGoal: parseFloat(storeGoals.today_goal),
                monthlyProgress: ((store.total / parseFloat(storeGoals.period_goal)) * 100).toFixed(2),
                dailyProgress: ((store.todaySales / parseFloat(storeGoals.today_goal)) * 100).toFixed(2)
            };

            // Agregar ventas por línea para esta tienda
            const storeLineData = salesByStoreAndLine[store.name] || {};
            // Agregar propiedades por cada línea directamente al objeto tienda
            sortedLines.forEach((lineInfo) => {
                const lineName = lineInfo.name;
                storeData[lineName] = storeLineData[lineName] || 0;
            });

            return storeData;
        });

        const allStoresArray = Object.values(allSalesByStore).map((store) => {
            const storeGoals = goalsData.find((g) => g.store_name === store.name) || {};
            return {
                name: store.name,
                total: store.total,
                todaySales: store.todaySales,
                monthlyGoal: parseFloat(storeGoals.period_goal),
                dailyGoal: parseFloat(storeGoals.today_goal),
                monthlyProgress: ((store.total / parseFloat(storeGoals.period_goal)) * 100).toFixed(2),
                dailyProgress: ((store.todaySales / parseFloat(storeGoals.today_goal)) * 100).toFixed(2)
            };
        });

        // Crear estructura para ventas totales por línea
        const linesSummary = Object.keys(salesByLine).map((lineName) => {
            let total = 0;
            Object.values(salesByStoreAndLine).forEach((storeLines) => {
                total += storeLines[lineName] || 0;
            });
            return {
                name: lineName,
                value: total
            };
        });
        return {
            stores: storesArray,
            allStores: allStoresArray,
            lines: sortedLines.slice(0, 4),
            allLines: sortedLines,
            linesByStore: salesByStoreAndLine,
            linesSummary: linesSummary,
            hours: Object.entries(salesByHour)
                .map(([hour, value]) => ({
                    hour,
                    value,
                    formattedValue: formatCurrency(value)
                }))
                .sort((a, b) => {
                    const hourA = parseInt(a.hour.split(':')[0]);
                    const hourB = parseInt(a.hour.split(':')[0]);
                    return hourA - hourB;
                }),
            days: Object.entries(salesByDay)
                .map(([day, value]) => ({
                    day,
                    value,
                    formattedValue: formatCurrency(value)
                }))
                .sort((a, b) => {
                    // Ordenar por fecha usando componentes individuales para evitar problemas de timezone
                    const [yearA, monthA, dayA] = a.day.split('-').map((num) => parseInt(num));
                    const [yearB, monthB, dayB] = b.day.split('-').map((num) => parseInt(num));

                    if (yearA !== yearB) return yearA - yearB;
                    if (monthA !== monthB) return monthA - monthB;
                    return dayA - dayB;
                }),
            weeks: Object.entries(salesByWeek)
                .map(([yearWeek, value]) => {
                    const [year, weekPart] = yearWeek.split('-W');
                    const weekNum = parseInt(weekPart);

                    // Calculamos la fecha del primer día de la semana (lunes)
                    const firstDayOfWeek = new Date(parseInt(year), 0, 1);
                    // Ajustamos al lunes
                    const dayOfWeek = firstDayOfWeek.getDay() || 7;
                    firstDayOfWeek.setDate(firstDayOfWeek.getDate() + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek));

                    // Calculamos el último día (domingo)
                    const lastDayOfWeek = new Date(firstDayOfWeek);
                    lastDayOfWeek.setDate(firstDayOfWeek.getDate() + 6);

                    // Formatos de fecha
                    const formatDate = (d) => {
                        return d.toISOString().split('T')[0];
                    };

                    return {
                        week: weekNum.toString(),
                        year: year,
                        yearWeek: yearWeek,
                        value,
                        firstDay: formatDate(firstDayOfWeek),
                        lastDay: formatDate(lastDayOfWeek),
                        formattedValue: formatCurrency(value)
                    };
                })
                .sort((a, b) => {
                    if (a.year !== b.year) return parseInt(a.year) - parseInt(b.year);
                    return parseInt(a.week) - parseInt(b.week);
                })
        };
    };

    const getProcessedData = () => {
        if (!processedDataRef.current) {
            processedDataRef.current = processData();
        }
        return processedDataRef.current;
    };

    // Función optimizada para detectar cambios que requieren recalcular los datos
    useEffect(() => {
        // Cuando cambian los datos o filtros, invalidamos la caché
        processedDataRef.current = null;
    }, [salesData, selectedStore, selectedLineNames, selectedRealLineNames, dateRange, startTime, endTime]);

    const formatCurrency = (value) => {
        return new Intl.NumberFormat('es-PE', {
            style: 'currency',
            currency: 'PEN',
            minimumFractionDigits: 2
        }).format(value);
    };

    const formatDate = (dateStr) => {
        const date = new Date(dateStr + 'T00:00:00');
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        return `${day}/${month}`;
    };

    const CustomTooltip = ({ active, payload, label }) => {
        if (active && payload && payload.length) {
            return (
                <Box
                    sx={{
                        backgroundColor: 'white',
                        padding: '10px',
                        border: '1px solid #ccc',
                        borderRadius: '4px'
                    }}
                >
                    <Typography variant="subtitle2">{payload[0].name}</Typography>
                    <Typography variant="body2" color="textSecondary">
                        {formatCurrency(payload[0].value)}
                    </Typography>
                    <Typography variant="body2" color="primary">
                        {payload[0].payload.percentage?.toFixed(2)}%
                    </Typography>
                </Box>
            );
        }
        return null;
    };

    const downloadChart = (chartRef, title) => {
        if (chartRef.current) {
            const svgElement = chartRef.current.container.children[0];
            const svgData = new XMLSerializer().serializeToString(svgElement);
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            // Configurar el tamaño del canvas basado en el SVG
            const boundingBox = svgElement.getBoundingClientRect();
            canvas.width = boundingBox.width;
            canvas.height = boundingBox.height;

            img.onload = () => {
                // Fondo blanco
                ctx.fillStyle = '#FFFFFF';
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // Dibujar el gráfico
                ctx.drawImage(img, 0, 0);

                // Convertir a PNG y descargar
                const pngFile = canvas.toDataURL('image/png');
                const downloadLink = document.createElement('a');
                downloadLink.download = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.png`;
                downloadLink.href = pngFile;
                downloadLink.click();
            };

            img.src = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgData)));
        }
    };

    const exportLinesSummaryToExcel = () => {
        if (!getProcessedData().stores || !getProcessedData().allLines) return;

        const workbook = XLSX.utils.book_new();

        // Preparar los encabezados
        const headers = ['Tiendas', ...getProcessedData().allLines.map((line) => line.name), 'Total'];

        // Preparar los datos de las tiendas
        const storeData = getProcessedData().stores.map((store) => {
            const row = {
                Tiendas: store.name
            };

            // Añadir cada valor de línea para esta tienda
            getProcessedData().allLines.forEach((line) => {
                row[line.name] = store[line.name] || 0;
            });

            // Añadir el total de la tienda
            row['Total'] = store.total || 0;

            return row;
        });

        // Añadir fila de totales
        const totalRow = {
            Tiendas: 'Total General'
        };

        // Añadir totales por línea
        getProcessedData().allLines.forEach((line) => {
            totalRow[line.name] = line.value || 0;
        });

        // Añadir total general
        totalRow['Total'] = getProcessedData().stores.reduce((sum, store) => sum + (store.total || 0), 0);

        // Combinar los datos
        const exportData = [...storeData, totalRow];

        // Crear hoja de trabajo
        const worksheet = XLSX.utils.json_to_sheet(exportData, { header: headers });

        // Formatear las celdas como moneda y ajustar el ancho de las columnas
        const range = XLSX.utils.decode_range(worksheet['!ref']);

        // Establecer anchos de columna para asegurar que los valores sean visibles
        const columnWidths = [];
        for (let i = 0; i <= range.e.c; i++) {
            columnWidths[i] = { width: 15 }; // Ancho estándar para todas las columnas
        }
        worksheet['!cols'] = columnWidths;

        // Aplicar formato de moneda
        for (let C = 1; C <= range.e.c; C++) {
            for (let R = 1; R <= range.e.r + 1; R++) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });
                if (worksheet[cellAddress] && typeof worksheet[cellAddress].v === 'number') {
                    worksheet[cellAddress].z = '"S/"#,##0.00';
                }
            }
        }

        XLSX.utils.book_append_sheet(workbook, worksheet, 'Ventas_Por_Tienda_Linea');
        XLSX.writeFile(workbook, 'ventas_por_tienda_linea.xlsx');
    };

    const exportLinesTableToExcel = () => {
        const data = [];
        getProcessedData().allLines.forEach((line) => {
            // Add division row
            data.push({
                División: line.name,
                Línea: '',
                Sublínea: '',
                'Total S/.': line.value,
                Porcentaje: `${line.percentage.toFixed(2)}%`
            });

            // Add real lines with their sublines
            line.realLines.forEach((realLine) => {
                // Add line row
                data.push({
                    División: line.name,
                    Línea: realLine.name,
                    Sublínea: '',
                    'Total S/.': realLine.value,
                    Porcentaje: `${realLine.percentage.toFixed(2)}%`
                });

                // Add sublines
                realLine.sublines.forEach((subline) => {
                    data.push({
                        División: line.name,
                        Línea: realLine.name,
                        Sublínea: subline.name,
                        'Total S/.': subline.value,
                        Porcentaje: `${subline.percentage.toFixed(2)}%`
                    });
                });
            });

            // Add division total
            data.push({
                División: `Total ${line.name}`,
                Línea: '',
                Sublínea: '',
                'Total S/.': line.value,
                Porcentaje: `${line.percentage.toFixed(2)}%`
            });
        });

        // Add grand total
        data.push({
            División: 'Total General',
            Línea: '',
            Sublínea: '',
            'Total S/.': getProcessedData().allLines.reduce((sum, line) => sum + line.value, 0),
            Porcentaje: '100.00%'
        });

        const ws = XLSX.utils.json_to_sheet(data);
        const wb = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(wb, ws, 'Ventas por División');
        XLSX.writeFile(wb, 'ventas_por_division.xlsx');
    };

    const isMonday = (dateStr) => {
        // Asegurarnos de que la fecha esté en formato YYYY-MM-DD
        const [year, month, day] = dateStr.split('-').map((num) => parseInt(num, 10));
        // En JavaScript, los meses van de 0-11, por eso restamos 1 al mes
        const date = new Date(year, month - 1, day);
        return date.getDay() === 1; // 0 es domingo, 1 es lunes
    };

    const isSunday = (dateStr) => {
        // Asegurarnos de que la fecha esté en formato YYYY-MM-DD
        const [year, month, day] = dateStr.split('-').map((num) => parseInt(num, 10));
        // En JavaScript, los meses van de 0-11, por eso restamos 1 al mes
        const date = new Date(year, month - 1, day);
        return date.getDay() === 0; // 0 es domingo
    };

    const getMixedViewData = () => {
        const days = [...getProcessedData().days];
        const mixedData = [];
        let i = 0;

        while (i < days.length) {
            // Si el día actual es lunes y hay suficientes días para una semana completa
            // y el último día es domingo (semana completa)
            if (isMonday(days[i].day) && i + 6 < days.length && isSunday(days[i + 6].day)) {
                // Crear una entrada semanal completa
                const weekStartDate = days[i].day;
                const weekEndDate = days[i + 6].day;
                const weekTotal = days.slice(i, i + 7).reduce((sum, day) => sum + day.value, 0);

                mixedData.push({
                    type: 'complete_week',
                    day: `${weekStartDate} - ${weekEndDate}`,
                    value: weekTotal,
                    formattedValue: formatCurrency(weekTotal)
                });

                // Avanzar al siguiente día después de la semana
                i += 7;
            } else {
                // Buscar días consecutivos que no forman una semana completa
                let j = i;
                // Avanzar hasta encontrar el inicio de una semana completa o el final del array
                while (j < days.length && !(isMonday(days[j].day) && j + 6 < days.length && isSunday(days[j + 6].day))) {
                    j++;
                }

                // Si encontramos al menos un día para agrupar
                if (j > i) {
                    const incompleteWeekStartDate = days[i].day;
                    const incompleteWeekEndDate = days[j - 1].day;
                    const incompleteWeekTotal = days.slice(i, j).reduce((sum, day) => sum + day.value, 0);

                    mixedData.push({
                        type: 'incomplete_week',
                        day: `${incompleteWeekStartDate} - ${incompleteWeekEndDate}`,
                        value: incompleteWeekTotal,
                        formattedValue: formatCurrency(incompleteWeekTotal)
                    });

                    // Avanzar después del grupo de días
                    i = j;
                } else {
                    // Este caso no debería ocurrir, pero lo mantenemos por seguridad
                    i++;
                }
            }
        }

        return mixedData;
    };

    const handleViewChange = (event, newView) => {
        if (newView !== null) {
            setViewMode(newView);
        }
    };

    const handleStoreClick = (storeName) => {
        setSelectedStore(storeName);
    };

    // Simplificamos las funciones para mejor manejo
    const toggleAllLines = (divisionName, e) => {
        // Detener la propagación para evitar clic en acordeón
        if (e) {
            e.stopPropagation();
            e.preventDefault();
        }

        // Obtenemos la lista de líneas una sola vez
        const processedData = getProcessedData();
        const division = processedData.allLines.find((line) => line.name === divisionName);

        if (!division || !division.realLines || division.realLines.length === 0) return;

        // Verificamos si al menos una línea está abierta
        const lineKeys = division.realLines.map((realLine) => `${divisionName}-${realLine.name}`);
        const anyLineOpen = lineKeys.some((key) => expandedLines[key]);

        // Creamos un nuevo objeto de estado directamente con los valores deseados
        const newExpandedLines = { ...expandedLines };

        lineKeys.forEach((key) => {
            newExpandedLines[key] = !anyLineOpen;
        });

        // Si vamos a expandir, aseguramos que la división esté abierta
        if (!anyLineOpen) {
            setExpandedDivisions((prev) => ({
                ...prev,
                [divisionName]: true
            }));
        }

        // Actualización única del estado
        setExpandedLines(newExpandedLines);
    };

    // Manejador para expandir/colapsar la división principal
    const handleDivisionExpand = (divisionName, isExpanded) => {
        setExpandedDivisions((prev) => ({
            ...prev,
            [divisionName]: isExpanded
        }));

        // Si se está colapsando la división, cerramos todas sus líneas
        if (!isExpanded) {
            const processedData = getProcessedData();
            const division = processedData.allLines.find((line) => line.name === divisionName);

            if (division && division.realLines) {
                const newExpandedLines = { ...expandedLines };

                division.realLines.forEach((realLine) => {
                    const lineKey = `${divisionName}-${realLine.name}`;
                    newExpandedLines[lineKey] = false;
                });

                setExpandedLines(newExpandedLines);
            }
        }
    };

    // Manejador para la expansión individual de líneas
    const handleLineExpand = (lineKey, isExpanded) => {
        setExpandedLines((prev) => ({
            ...prev,
            [lineKey]: isExpanded
        }));
    };

    // Función optimizada para verificar si alguna línea está abierta
    const isAnyLineExpanded = (divisionName) => {
        const processedData = getProcessedData();
        const division = processedData.allLines.find((line) => line.name === divisionName);

        if (!division || !division.realLines) return false;

        // Verificamos más eficientemente usando some() en lugar de un bucle
        return division.realLines.some((realLine) => expandedLines[`${divisionName}-${realLine.name}`] === true);
    };

    if (loading) {
        return (
            <Backdrop
                sx={{
                    color: '#fff',
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    backgroundColor: 'rgba(255, 255, 255, 0.8)'
                }}
                open={loading}
            >
                <Box
                    sx={{
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        gap: 2
                    }}
                >
                    <CircularProgress
                        size={60}
                        thickness={4}
                        sx={{
                            color: '#b3256e'
                        }}
                    />
                    <Typography
                        variant="h6"
                        sx={{
                            color: '#b3256e',
                            fontWeight: 'bold'
                        }}
                    >
                        Cargando datos...
                    </Typography>
                </Box>
            </Backdrop>
        );
    }

    // Calculamos los datos procesados una sola vez
    const processedData = getProcessedData();
    const stores = [...new Set(salesData.map((sale) => sale.store_name))];

    return (
        <Box sx={{ padding: '20px' }}>
            <ParticipacionesSection>
                <ParticipacionesTitle>Filtros</ParticipacionesTitle>
                <FiltersWrapper>
                    <FilterGroup>
                        <CompactDatePicker
                                label="Fecha inicial"
                                value={dateRange[0]}
                                onChange={(newValue) => {
                                    // Prevenir valores nulos o inválidos
                                    if (newValue && !isNaN(newValue.getTime())) {
                                        const adjusted = new Date(newValue);
                                        adjusted.setHours(12, 0, 0, 0);
                                        setDateRange([adjusted, dateRange[1]]);
                                    }
                                }}
                                renderInput={(params) => <TextField {...params} size="small" />}
                                inputFormat="dd/MM/yyyy"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#b3256e'
                                        }
                                    },
                                    '& .MuiInputLabel-root.Mui-focused': {
                                        color: '#b3256e'
                                    }
                                }}
                            />
                            <CompactDatePicker
                                label="Fecha final"
                                value={dateRange[1]}
                                onChange={(newValue) => {
                                    // Prevenir valores nulos o inválidos
                                    if (newValue && !isNaN(newValue.getTime())) {
                                        const adjusted = new Date(newValue);
                                        adjusted.setHours(12, 0, 0, 0);
                                        setDateRange([dateRange[0], adjusted]);
                                    }
                                }}
                                renderInput={(params) => <TextField {...params} size="small" />}
                                inputFormat="dd/MM/yyyy"
                                sx={{
                                    '& .MuiOutlinedInput-root': {
                                        '&.Mui-focused fieldset': {
                                            borderColor: '#b3256e'
                                        }
                                    },
                                    '& .MuiInputLabel-root.Mui-focused': {
                                        color: '#b3256e'
                                    }
                                }}
                            />
                        <IconButton
                            size="small"
                            onClick={handleFilterClick}
                            sx={{
                                border: '1px solid #e0e0e0',
                                borderRadius: '18px',
                                padding: '8px'
                            }}
                        >
                            <FilterListIcon fontSize="small" />
                        </IconButton>
                    </FilterGroup>

                    <FilterGroup>
                        <TimeSelect
                            type="time"
                            value={startTime}
                            onChange={(e) => handleTimeChange('start', e)}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                            label="Desde"
                        />
                        <TimeSelect
                            type="time"
                            value={endTime}
                            onChange={(e) => handleTimeChange('end', e)}
                            size="small"
                            InputLabelProps={{ shrink: true }}
                            label="Hasta"
                        />
                    </FilterGroup>

                    <FilterGroup sx={{ flex: 1 }}>
                        <Autocomplete
                            multiple
                            size="small"
                            value={selectedLineNames}
                            onChange={handleLineNameChange}
                            options={availableLineNames}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    placeholder="Divisiones"
                                    size="small"
                                    sx={{
                                        minWidth: 200,
                                        '& .MuiOutlinedInput-root': {
                                            borderRadius: '18px',
                                            height: '36px'
                                        }
                                    }}
                                />
                            )}
                            renderTags={(selected, getTagProps) =>
                                selected.map((option, index) => (
                                    <FilterChip
                                        {...getTagProps({ index })}
                                        key={option}
                                        label={option}
                                        selected
                                        onDelete={getTagProps({ index }).onDelete}
                                    />
                                ))
                            }
                        />
                    </FilterGroup>

                    <FilterGroup sx={{ flex: 1 }}>
                        <Autocomplete
                            multiple
                            size="small"
                            value={selectedRealLineNames}
                            onChange={handleRealLineNameChange}
                            options={availableRealLineNames}
                            renderInput={(params) => (
                                <TextField
                                    {...params}
                                    placeholder="Líneas"
                                    size="small"
                                    sx={{
                                        minWidth: 200,
                                        '& .MuiOutlinedInput-root': {
                                            borderRadius: '18px',
                                            height: '36px'
                                        }
                                    }}
                                />
                            )}
                            renderTags={(selected, getTagProps) =>
                                selected.map((option, index) => (
                                    <FilterChip
                                        {...getTagProps({ index })}
                                        key={option}
                                        label={option}
                                        selected
                                        onDelete={getTagProps({ index }).onDelete}
                                    />
                                ))
                            }
                        />
                    </FilterGroup>
                </FiltersWrapper>

                <Menu
                    anchorEl={filterAnchorEl}
                    open={Boolean(filterAnchorEl)}
                    onClose={handleFilterClose}
                    PaperProps={{
                        sx: {
                            mt: 1,
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            borderRadius: '8px',
                            '& .MuiMenuItem-root': {
                                fontSize: '0.875rem',
                                padding: '8px 16px',
                                '&:hover': {
                                    backgroundColor: '#b3256e10'
                                }
                            }
                        }
                    }}
                >
                    <MenuItem onClick={() => handleDateRangePreset('today')}>Hoy</MenuItem>
                    <MenuItem onClick={() => handleDateRangePreset('yesterday')}>Ayer</MenuItem>
                    <MenuItem onClick={() => handleDateRangePreset('last7')}>Últimos 7 días</MenuItem>
                    <MenuItem onClick={() => handleDateRangePreset('last30')}>Últimos 30 días</MenuItem>
                </Menu>

                <StoreSelect
                    value={selectedStore}
                    exclusive
                    onChange={(event, newStore) => setSelectedStore(newStore || 'all')}
                    aria-label="store selection"
                >
                    <ToggleButton value="all">Todas</ToggleButton>
                    {stores.map((store) => (
                        <ToggleButton key={store} value={store}>
                            {store}
                        </ToggleButton>
                    ))}
                    <AIChat
                        salesAdvanceData={processedData.stores}
                        salesHoursData={processedData.hours}
                        salesDivisionData={processedData.lines}
                        salesStoreData={processedData.allStores}
                        salesLinesData={processedData.allLines}
                    />
                </StoreSelect>
            </ParticipacionesSection>

            <ParticipacionesSection>
                <h3>Ventas Por División</h3>
                <TableContainer
                    component={Paper}
                    sx={{ marginBottom: '20px', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)' }}
                >
                    <Table>
                        <TableHead>
                            <TableRow>
                                <StyledTableCell className="header">Tiendas</StyledTableCell>
                                {processedData.allLines &&
                                    processedData.allLines.map((line) => (
                                        <StyledTableCell key={line.name} className="header" align="right">
                                            {line.name}
                                        </StyledTableCell>
                                    ))}
                                <StyledTableCell className="header" align="right">
                                    Total
                                </StyledTableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {processedData.stores &&
                                processedData.stores.map((store) => (
                                    <StyledTableRow key={store.name}>
                                        <StyledTableCell>{store.name}</StyledTableCell>
                                        {processedData.allLines &&
                                            processedData.allLines.map((line) => (
                                                <StyledTableCell key={`${store.name}-${line.name}`} align="right">
                                                    {formatCurrency(store[line.name] || 0)}
                                                </StyledTableCell>
                                            ))}
                                        <StyledTableCell align="right">{formatCurrency(store.total || 0)}</StyledTableCell>
                                    </StyledTableRow>
                                ))}
                            <TotalRow>
                                <StyledTableCell>Total General</StyledTableCell>
                                {processedData.allLines &&
                                    processedData.allLines.map((line) => (
                                        <StyledTableCell key={`total-${line.name}`} align="right">
                                            {formatCurrency(line.value || 0)}
                                        </StyledTableCell>
                                    ))}
                                <StyledTableCell align="right">
                                    {formatCurrency(
                                        processedData.stores ? processedData.stores.reduce((sum, store) => sum + (store.total || 0), 0) : 0
                                    )}
                                </StyledTableCell>
                            </TotalRow>
                        </TableBody>
                    </Table>
                </TableContainer>
                <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', alignItems: 'center', gap: 2 }}>
                    <MuiTooltip title="Exportar a Excel">
                        <IconButton onClick={exportLinesSummaryToExcel} size="small">
                            <DownloadIcon />
                        </IconButton>
                    </MuiTooltip>
                </Box>
            </ParticipacionesSection>

            <ParticipacionesSection>
                <ParticipacionesTitle>Participaciones</ParticipacionesTitle>

                <ChartContainer>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <ChartTitle>Participación de Ventas por {viewMode === 'daily' ? 'Día' : 'Semana/Día'}</ChartTitle>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <ToggleButtonGroup
                                value={viewMode}
                                exclusive
                                onChange={handleViewChange}
                                size="small"
                                sx={{
                                    mr: 2,
                                    '& .MuiToggleButton-root': {
                                        '&.Mui-selected': {
                                            backgroundColor: '#b94b84',
                                            color: 'white',
                                            '&:hover': {
                                                backgroundColor: '#a03c71'
                                            }
                                        }
                                    }
                                }}
                            >
                                <ToggleButton value="daily" aria-label="vista diaria">
                                    <MuiTooltip title="Vista Diaria">
                                        <Box
                                            sx={{
                                                fontSize: '12px',
                                                fontWeight: 'medium',
                                                color: viewMode === 'daily' ? 'white' : 'inherit'
                                            }}
                                        >
                                            Días
                                        </Box>
                                    </MuiTooltip>
                                </ToggleButton>
                                <ToggleButton value="mixed" aria-label="vista mixta">
                                    <MuiTooltip title="Vista Mixta (Semanas Completas e Incompletas)">
                                        <Box
                                            sx={{
                                                fontSize: '12px',
                                                fontWeight: 'medium',
                                                color: viewMode === 'mixed' ? 'white' : 'inherit'
                                            }}
                                        >
                                            Semanas
                                        </Box>
                                    </MuiTooltip>
                                </ToggleButton>
                            </ToggleButtonGroup>
                            <MuiTooltip title="Descargar gráfico">
                                <IconButton
                                    size="small"
                                    onClick={() =>
                                        downloadChart(
                                            dayChartRef,
                                            viewMode === 'daily' ? 'Participación_Ventas_por_Día' : 'Participación_Ventas_Mixta'
                                        )
                                    }
                                >
                                    <DownloadIcon fontSize="small" />
                                </IconButton>
                            </MuiTooltip>
                        </Box>
                    </Box>
                    <ResponsiveContainer width="100%" height={400}>
                        <BarChart
                            ref={dayChartRef}
                            data={viewMode === 'daily' ? processedData.days : getMixedViewData()}
                            barSize={60}
                            maxBarSize={60}
                            margin={{ top: 30, right: 30, left: 20, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                                dataKey="day"
                                tickFormatter={(value) => {
                                    // Si estamos en modo mixto y el valor contiene un guion, es un rango de semana
                                    if (viewMode === 'mixed' && value.includes(' - ')) {
                                        // Extraer las fechas de inicio y fin
                                        const [startDate, endDate] = value.split(' - ');
                                        // Formatear ambas fechas
                                        return `${formatDate(startDate)} - ${formatDate(endDate)}`;
                                    }
                                    // Para días individuales, usar el formatDate normal
                                    return formatDate(value);
                                }}
                                angle={-45}
                                textAnchor="end"
                                height={80}
                            />
                            <YAxis tickFormatter={(value) => `S/ ${(value / 1000).toFixed(1)}K`} tick={{ fontSize: 11 }} />
                            <Tooltip
                                formatter={(value, name) => [formatCurrency(value), 'Venta']}
                                labelFormatter={(label) => {
                                    if (viewMode === 'day') {
                                        try {
                                            // Verificar si el label es un formato de fecha válido
                                            if (typeof label === 'string' && label.includes('-') && label.split('-').length === 3) {
                                                const parts = label.split('-');
                                                const year = parseInt(parts[0]);
                                                const month = parseInt(parts[1]) - 1;
                                                const day = parseInt(parts[2]);

                                                if (!isNaN(year) && !isNaN(month) && !isNaN(day)) {
                                                    const date = new Date(year, month, day, 12, 0, 0);
                                                    return format(date, 'yyyy-MM-dd');
                                                }
                                            }
                                            return String(label);
                                        } catch (error) {
                                            console.error('Error formatting date:', error);
                                            return String(label);
                                        }
                                    } else {
                                        try {
                                            // Validar que el número de semana esté en rango (1-53)
                                            const weekNum = parseInt(label);
                                            if (isNaN(weekNum) || weekNum < 1 || weekNum > 53) {
                                                return `Semana ${label}`;
                                            }

                                            const today = new Date();
                                            const year = today.getFullYear();

                                            // Calcula el primer día del año
                                            const firstDayOfYear = new Date(year, 0, 1);

                                            // Ajusta al primer día de la semana (lunes=1, domingo=0)
                                            const dayOfWeek = firstDayOfYear.getDay() || 7;
                                            const firstWeekday = new Date(firstDayOfYear);
                                            firstWeekday.setDate(
                                                firstDayOfYear.getDate() + (dayOfWeek <= 4 ? 1 - dayOfWeek : 8 - dayOfWeek)
                                            );

                                            // Calcula la primera fecha de la semana solicitada
                                            const firstDate = new Date(firstWeekday);
                                            firstDate.setDate(firstWeekday.getDate() + (weekNum - 1) * 7);

                                            // Calcula el último día (domingo)
                                            const lastDate = new Date(firstDate);
                                            lastDate.setDate(firstDate.getDate() + 6);

                                            return `Semana ${label}: ${format(firstDate, 'dd/MM')} - ${format(lastDate, 'dd/MM')}`;
                                        } catch (error) {
                                            console.error('Error formatting week:', error);
                                            return `Semana ${label}`;
                                        }
                                    }
                                }}
                                contentStyle={{ fontSize: 12 }}
                            />
                            <Bar
                                dataKey="value"
                                fill="#b3256e"
                                label={{
                                    position: 'top',
                                    formatter: (value, entry, index) => {
                                        // Calcula la suma total
                                        const totalSum = (viewMode === 'day' ? processedData.days : processedData.weeks).reduce(
                                            (acc, curr) => acc + curr.value,
                                            0
                                        );

                                        // Calcula el porcentaje sin redondear para uso interno
                                        const exactPercentage = (value / totalSum) * 100;

                                        // Para evitar que los porcentajes sumen más de 100%, ajusta el último elemento
                                        const isLastItem =
                                            index === (viewMode === 'day' ? processedData.days.length - 1 : processedData.weeks.length - 1);

                                        if (isLastItem) {
                                            // Suma de todos los porcentajes excepto el último
                                            const otherPercentages = (viewMode === 'day' ? processedData.days : processedData.weeks)
                                                .slice(0, -1)
                                                .reduce((acc, curr) => acc + Math.round((curr.value / totalSum) * 1000) / 10, 0);

                                            // El último porcentaje es el complemento para llegar a 100%
                                            return `${(100 - otherPercentages).toFixed(1)}%`;
                                        }

                                        // Para los demás elementos, redondea normalmente
                                        return `${Math.round(exactPercentage * 10) / 10}%`;
                                    },
                                    fontSize: 11
                                }}
                            >
                                {(viewMode === 'daily' ? processedData.days : getMixedViewData()).map((entry, index) => (
                                    <Cell key={`cell-${index}`} fill={entry.type === 'complete_week' ? '#4a148c' : '#b3256e'} />
                                ))}
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                </ChartContainer>

                <ChartContainer>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <ChartTitle>Participación de Ventas por Hora</ChartTitle>
                        <MuiTooltip title="Descargar gráfico">
                            <IconButton size="small" onClick={() => downloadChart(hourChartRef, 'Participación_Ventas_por_Hora')}>
                                <DownloadIcon fontSize="small" />
                            </IconButton>
                        </MuiTooltip>
                    </Box>
                    <ResponsiveContainer width="100%" height={300}>
                        <BarChart
                            ref={hourChartRef}
                            data={processedData.hours.map((hour) => {
                                const total = processedData.hours.reduce((acc, curr) => acc + curr.value, 0);
                                const percentage = (hour.value / total) * 100;
                                return {
                                    ...hour,
                                    percentage,
                                    fillColor: getHeatMapColor(hour.value, total)
                                };
                            })}
                            layout="vertical"
                            margin={{ top: 5, right: 30, left: 40, bottom: 5 }}
                        >
                            <CartesianGrid strokeDasharray="3 3" horizontal={false} />
                            <XAxis type="number" tickFormatter={(value) => `S/ ${(value / 1000).toFixed(1)}K`} />
                            <YAxis dataKey="hour" type="category" width={60} tick={{ fill: '#666' }} />
                            <Tooltip
                                formatter={(value, name, props) => {
                                    const percentage = (value / processedData.hours.reduce((acc, curr) => acc + curr.value, 0)) * 100;
                                    return [`${formatCurrency(value)} (${percentage.toFixed(1)}%)`, 'Venta'];
                                }}
                                labelFormatter={(label) => `Hora: ${label}`}
                                contentStyle={{
                                    backgroundColor: '#fff',
                                    border: '1px solid #e0e0e0',
                                    borderRadius: '4px',
                                    padding: '8px'
                                }}
                            />
                            <Bar
                                dataKey="value"
                                name="Ventas"
                                fill="#b3256e"
                                background={{ fill: '#f5f5f5' }}
                                label={{
                                    position: 'right',
                                    content: ({ value }) => `S/ ${(value / 1000).toFixed(1)}K`,
                                    fontSize: 11
                                }}
                            >
                                {processedData.hours.map((entry, index) => {
                                    const total = processedData.hours.reduce((acc, curr) => acc + curr.value, 0);
                                    return <Cell key={`cell-${index}`} fill={getHeatMapColor(entry.value, total)} />;
                                })}
                            </Bar>
                        </BarChart>
                    </ResponsiveContainer>
                    <HeatMapLegend>
                        <Typography variant="caption" sx={{ color: '#666', fontWeight: 'bold' }}>
                            &lt;2%
                        </Typography>
                        {getHeatMapColors().map((color, index) => (
                            <Box key={index} className="color-box" sx={{ backgroundColor: color }} />
                        ))}
                        <Typography variant="caption" sx={{ color: '#666', fontWeight: 'bold' }}>
                            &gt;8%
                        </Typography>
                    </HeatMapLegend>
                </ChartContainer>

                <ChartsContainer>
                    <ChartContainer>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                            <ChartTitle>Participación de Ventas por División</ChartTitle>
                            <MuiTooltip title="Descargar gráfico">
                                <IconButton
                                    size="small"
                                    onClick={() => downloadChart(divisionChartRef, 'Participación_Ventas_por_División')}
                                >
                                    <DownloadIcon fontSize="small" />
                                </IconButton>
                            </MuiTooltip>
                        </Box>
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart ref={divisionChartRef}>
                                <Pie
                                    data={processedData.lines}
                                    dataKey="value"
                                    nameKey="name"
                                    cx="50%"
                                    cy="50%"
                                    outerRadius={80}
                                    label={({ cx, cy, midAngle, innerRadius, outerRadius, value, percentage }) => {
                                        const RADIAN = Math.PI / 180;
                                        const radius = outerRadius + 25;
                                        const x = cx + radius * Math.cos(-midAngle * RADIAN);
                                        const y = cy + radius * Math.sin(-midAngle * RADIAN);
                                        return (
                                            <g>
                                                <text
                                                    x={x}
                                                    y={y}
                                                    fill="#666"
                                                    textAnchor={x > cx ? 'start' : 'end'}
                                                    dominantBaseline="central"
                                                >
                                                    {`${percentage?.toFixed(1)}%`}
                                                </text>
                                                <text
                                                    x={x}
                                                    y={y + 15}
                                                    fill="#666"
                                                    textAnchor={x > cx ? 'start' : 'end'}
                                                    dominantBaseline="central"
                                                    style={{ fontSize: '0.8em' }}
                                                >
                                                    {formatCurrency(value)}
                                                </text>
                                            </g>
                                        );
                                    }}
                                >
                                    {processedData.lines.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                    ))}
                                </Pie>
                                <Tooltip content={<CustomTooltip />} />
                                <Legend
                                    verticalAlign="bottom"
                                    height={36}
                                    formatter={(value, entry) => <span style={{ color: '#666', marginRight: '10px' }}>{value}</span>}
                                />
                            </PieChart>
                        </ResponsiveContainer>
                    </ChartContainer>

                    <ChartContainer>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                            <ChartTitle>Participación de Ventas por Tienda</ChartTitle>
                            <MuiTooltip title="Descargar gráfico">
                                <IconButton size="small" onClick={() => downloadChart(storeChartRef, 'Participación_Ventas_por_Tienda')}>
                                    <DownloadIcon fontSize="small" />
                                </IconButton>
                            </MuiTooltip>
                        </Box>
                        <ResponsiveContainer width="100%" height={400}>
                            <BarChart
                                ref={storeChartRef}
                                data={processedData.allStores}
                                barSize={60}
                                maxBarSize={60}
                                margin={{ top: 30, right: 30, left: 20, bottom: 5 }}
                            >
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis
                                    dataKey="name"
                                    tickFormatter={(value) => {
                                        return value
                                            .split(' ')
                                            .map((word) => word.charAt(0))
                                            .join('')
                                            .toUpperCase();
                                    }}
                                />{' '}
                                <YAxis tickFormatter={(value) => `S/ ${(value / 1000).toFixed(1)}K`} />
                                <Tooltip
                                    formatter={(value, name) => [formatCurrency(value), 'Venta']}
                                    labelFormatter={(label) => `${label}`}
                                />
                                <Bar
                                    dataKey="total"
                                    fill="#b3256e"
                                    label={{
                                        position: 'top',
                                        formatter: (value) => {
                                            const totalSum = processedData.allStores.reduce((acc, curr) => acc + curr.total, 0);
                                            return `${((value / totalSum) * 100).toFixed(1)}%`;
                                        }
                                    }}
                                >
                                    {processedData.allStores.map((entry, index) => (
                                        <Cell
                                            key={`cell-${index}`}
                                            fill="#b3256e"
                                            opacity={selectedStore === 'all' || selectedStore === entry.name ? 1 : 0.3}
                                            cursor="pointer"
                                            onClick={() => handleStoreClick(entry.name)}
                                        />
                                    ))}
                                </Bar>
                            </BarChart>
                        </ResponsiveContainer>
                    </ChartContainer>
                </ChartsContainer>
                <Box>
                    <ChartTitle>Detalle de Ventas por División, Líneas y Sublíneas</ChartTitle>
                    <Box sx={{ mb: 1 }}>
                        {processedData.allLines.map((line) => (
                            <Accordion
                                key={line.name}
                                expanded={expandedDivisions[line.name] || false}
                                onChange={(_, isExpanded) => handleDivisionExpand(line.name, isExpanded)}
                            >
                                <AccordionSummary
                                    expandIcon={<ExpandMoreIcon sx={{ color: 'white' }} />}
                                    aria-controls={`${line.name}-content`}
                                    id={`${line.name}-header`}
                                    sx={{
                                        bgcolor: '#9e187d',
                                        color: 'white',
                                        '&.Mui-expanded': {
                                            minHeight: '48px'
                                        },
                                        '& .MuiAccordionSummary-content.Mui-expanded': {
                                            margin: '12px 0'
                                        }
                                    }}
                                >
                                    <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%', alignItems: 'center' }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                            <Typography fontWeight="bold" color="white">
                                                {line.name}
                                            </Typography>
                                        </Box>
                                        <Typography variant="body2" color="white">
                                            {formatCurrency(line.value)} ({line.percentage.toFixed(2)}%)
                                        </Typography>
                                    </Box>
                                </AccordionSummary>
                                <AccordionDetails>
                                    <Box sx={{ mb: 2 }}>
                                        <Box
                                            sx={{
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 2,
                                                mb: 2,
                                                p: 1,
                                                borderBottom: '1px solid rgba(158, 24, 125, 0.1)'
                                            }}
                                        >
                                            <Typography
                                                component="span"
                                                onClick={(e) => {
                                                    if (!isAnyLineExpanded(line.name)) {
                                                        toggleAllLines(line.name, e);
                                                    }
                                                }}
                                                sx={{
                                                    color: '#2D58E4FF',
                                                    cursor: 'pointer',
                                                    textDecoration: 'underline'
                                                }}
                                            >
                                                Expandir todo
                                            </Typography>
                                            <Typography
                                                component="span"
                                                onClick={(e) => {
                                                    if (isAnyLineExpanded(line.name)) {
                                                        toggleAllLines(line.name, e);
                                                    }
                                                }}
                                                sx={{
                                                    color: '#2D58E4FF',
                                                    cursor: 'pointer',
                                                    textDecoration: 'underline'
                                                }}
                                            >
                                                Contraer todo
                                            </Typography>
                                        </Box>
                                        {line.realLines.map((realLine) => {
                                            const lineKey = `${line.name}-${realLine.name}`;
                                            const isExpanded = expandedLines[lineKey] || false;

                                            return (
                                                <Accordion
                                                    key={lineKey}
                                                    expanded={isExpanded}
                                                    onChange={(_, isLineExpanded) => handleLineExpand(lineKey, isLineExpanded)}
                                                >
                                                    <AccordionSummary
                                                        expandIcon={<ExpandMoreIcon sx={{ color: 'white' }} />}
                                                        aria-controls={`${lineKey}-content`}
                                                        id={`${lineKey}-header`}
                                                        sx={{
                                                            bgcolor: '#8A848AFF',
                                                            color: 'white',
                                                            '&.Mui-expanded': {
                                                                minHeight: '48px'
                                                            },
                                                            '& .MuiAccordionSummary-content.Mui-expanded': {
                                                                margin: '12px 0'
                                                            }
                                                        }}
                                                    >
                                                        <Box
                                                            sx={{
                                                                display: 'flex',
                                                                justifyContent: 'space-between',
                                                                width: '100%',
                                                                alignItems: 'center'
                                                            }}
                                                        >
                                                            <Typography fontWeight="bold" color="white">
                                                                {realLine.name}
                                                            </Typography>
                                                            <Typography variant="body2" color="white">
                                                                {formatCurrency(realLine.value)} ({realLine.percentage.toFixed(2)}%)
                                                            </Typography>
                                                        </Box>
                                                    </AccordionSummary>
                                                    <AccordionDetails>
                                                        <TableContainer component={Paper} sx={{ mb: 2 }}>
                                                            <Table size="small">
                                                                <TableHead>
                                                                    <TableRow>
                                                                        <StyledTableCell>Sublínea</StyledTableCell>
                                                                        <StyledTableCell align="right">Total S/.</StyledTableCell>
                                                                        <StyledTableCell align="right">Porcentaje</StyledTableCell>
                                                                    </TableRow>
                                                                </TableHead>
                                                                <TableBody>
                                                                    {realLine.sublines.map((subline) => (
                                                                        <StyledTableRow
                                                                            key={`${line.name}-${realLine.name}-${subline.name}`}
                                                                        >
                                                                            <TableCell>{subline.name}</TableCell>
                                                                            <TableCell align="right">
                                                                                {formatCurrency(subline.value)}
                                                                            </TableCell>
                                                                            <TableCell align="right">
                                                                                {subline.percentage.toFixed(2)}%
                                                                            </TableCell>
                                                                        </StyledTableRow>
                                                                    ))}
                                                                </TableBody>
                                                            </Table>
                                                        </TableContainer>
                                                    </AccordionDetails>
                                                </Accordion>
                                            );
                                        })}
                                    </Box>
                                </AccordionDetails>
                            </Accordion>
                        ))}
                        <Box sx={{ mt: 2, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', fontWeight: 'bold' }}>
                                <Typography variant="subtitle1">Total General</Typography>
                                <Box sx={{ display: 'flex', gap: 4 }}>
                                    <Typography variant="subtitle1">
                                        {formatCurrency(processedData.allLines.reduce((sum, line) => sum + line.value, 0))}
                                    </Typography>
                                    <Typography variant="subtitle1">100.00%</Typography>
                                </Box>
                            </Box>
                        </Box>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                        <MuiTooltip title="Exportar a Excel">
                            <IconButton onClick={exportLinesTableToExcel} size="small">
                                <DownloadIcon />
                            </IconButton>
                        </MuiTooltip>
                    </Box>
                </Box>
            </ParticipacionesSection>
        </Box>
    );
};
export default StoresTab;
