import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export const TODAY_LABEL = 'Hoy';
export const YESTERDAY_LABEL = 'Ayer';
export const WEEK_LABEL = 'Esta Semana';
export const MONTH_LABEL = 'Este Mes';
export const DATE_RANGE_LABEL = 'Rango de Fechas';

export const TODAY_VALUE = 'today';
export const YESTERDAY_VALUE = 'yesterday';
export const WEEK_VALUE = 'week';
export const MONTH_VALUE = 'month';
export const DATE_RANGE_VALUE = 'dateRange';

const MONTH_NAMES = [
    'Enero',
    'Febrero',
    'Marzo',
    'Abril',
    'Mayo',
    'Junio',
    'Julio',
    'Agosto',
    'Septiembre',
    'Octubre',
    'Noviembre',
    'Diciembre'
];

function capitalize(word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
}

function capitalizeDayAndMonth(text) {
    const parts = text.split(' ');

    if (parts.length >= 4) {
        parts[0] = `${capitalize(parts[0].replace(',', ''))},`;
        parts[3] = capitalize(parts[3]);
    } else if (parts.length >= 2) {
        parts[0] = capitalize(parts[0]);
    }

    return parts.join(' ');
}

export function getDatesOfNextNDaysOfWeek(daysOfWeek, nWeeks) {
    const resultDates = [];
    const currentDate = new Date();
    const weeksCounter = nWeeks;

    currentDate.setDate(currentDate.getDate() + 1);

    for (let i = 0; i < weeksCounter * 7; i++) {
        const nextDate = new Date(currentDate);
        nextDate.setDate(currentDate.getDate() + i);

        if (daysOfWeek.includes(nextDate.getDay())) {
            resultDates.push(nextDate);
        }
    }

    return resultDates;
}

export const getDatesForDetailDates = (details) => {
    const datesOfDetails = [];
    details.forEach((detail) =>
        detail.dates.forEach(
            (date) =>
                !datesOfDetails.some((existingDate) => existingDate.getTime() === date.date.getTime()) && datesOfDetails.push(date.date)
        )
    );
    return datesOfDetails.sort((a, b) => a.getTime() - b.getTime());
};

export function uniqueDates(dateArray) {
    const uniqueDates = [];
    dateArray.forEach((date) => {
        const exists = uniqueDates.some((uniqueDate) => uniqueDate.getTime() === date.getTime());
        if (!exists) {
            uniqueDates.push(date);
        }
    });
    return uniqueDates.sort((a, b) => a.getTime() - b.getTime());
}

export function parseDateToLocaleString(date) {
    return date.toLocaleDateString('es-PE', { day: '2-digit', month: '2-digit', year: 'numeric', weekday: 'long' });
}

export function parseDateToLocaleString2(date) {
    return date.toLocaleDateString('es-PE', { day: '2-digit', month: '2-digit', year: 'numeric' });
}

export function parseDateToLargeString(date = new Date()) {
    return date.toLocaleDateString('es-PE', { day: 'numeric', month: 'long', year: 'numeric', weekday: 'long' });
}

export function parseDataToSearchString(date) {
    return format(date, 'yyyy-MM-dd');
}

export function parseDataToForm(date) {
    return format(date, 'dd/MM/yyyy');
}

export function parseDateToApi(date) {
    return date.toISOString().slice(0, 10);
}

export function parseInputToApiFormat(stringDate) {
    const arrayDate = stringDate.split('-');
    const parsedDate = `${arrayDate[2]}/${arrayDate[1]}/${arrayDate[0]}`;
    return parsedDate;
}

export function parseStringDateToDateTime(stringDate) {
    return new Date(stringDate.replace(' ', 'T'));
}

export function getDifferenceInDays(date1, date2) {
    const normalizedDate1 = new Date(date1.getFullYear(), date1.getMonth(), date1.getDate());
    const normalizedDate2 = new Date(date2.getFullYear(), date2.getMonth(), date2.getDate());

    const time1 = normalizedDate1.getTime();
    const time2 = normalizedDate2.getTime();

    const differenceInMillis = time2 - time1;
    const differenceInDays = Math.floor(differenceInMillis / (1000 * 60 * 60 * 24));

    return differenceInDays;
}

export function parseStringDateToDate(stringDate) {
    const [year, month, day] = stringDate.split('-').map(Number);
    const dateSelected = new Date(year, month - 1, day);
    dateSelected.setHours(0, 0, 0, 0);
    return dateSelected;
}

export function subtractDays(date, days) {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() - days);
    return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
}

export function addDays(date, days) {
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + days);
    return new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate());
}

export function getTodayDate() {
    const date = new Date();
    date.setHours(0, 0, 0, 0);
    return date;
}
export function getTodayDateTime() {
    return new Date();
}

export function getCurrentPeriodString() {
    const date = new Date();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    return `${month}-${year}`;
}

export function formatPeriodLabel(month, year) {
    const monthName = MONTH_NAMES[month - 1];
    return `${monthName} ${year}`;
}

export function generatePeriods(startMonth, startYear, endDate = new Date()) {
    const periods = [];
    let month = startMonth;
    let year = startYear;

    while (year < endDate.getFullYear() || (year === endDate.getFullYear() && month <= endDate.getMonth() + 1)) {
        periods.push({ month, year });
        month += 1;
        if (month > 12) {
            month = 1;
            year += 1;
        }
    }

    return periods;
}

export function getDaysInMonth(month, year) {
    const numericMonth = parseInt(month, 10) - 1;
    const numericYear = parseInt(year, 10);

    if (Number.isNaN(numericMonth) || Number.isNaN(numericYear) || numericMonth < 0 || numericMonth > 11) {
        throw new Error('Mes o año no válidos');
    }

    return new Date(numericYear, numericMonth + 1, 0).getDate();
}

export const parseStringtoDateWithHour = (value) => `${format(new Date(value), "dd'/'MM'/'yyyy, hh:mm a", { locale: es })}`;

export function getFirstDayOfCurrentMonth() {
    const today = getTodayDate();
    return new Date(today.getFullYear(), today.getMonth(), 1);
}

export function getFirstDayOfGivenMonth(date) {
    return new Date(date.getFullYear(), date.getMonth(), 1);
}

export function formatFriendlyDate(date, { showDay = true, showYear = true } = {}) {
    const options = {
        ...(showDay && { weekday: 'long' }),
        day: 'numeric',
        month: 'long'
    };

    return capitalizeDayAndMonth(date.toLocaleDateString('es-ES', options));
}

export function formatFriendlyDateTime(date, { showDay = true, showYear = true, showSecond = true, upperCase = true }) {
    const formatted = date
        .toLocaleString('es-PE', {
            timeZone: 'America/Lima',
            ...(showDay && { weekday: 'long' }),
            day: 'numeric',
            month: 'long',
            ...(showYear && { year: 'numeric' }),
            hour: '2-digit',
            minute: '2-digit',
            ...(showSecond && { second: '2-digit' }),
            hour12: true
        })
        .replace(',', '');

    return upperCase ? formatted.toUpperCase() : formatted;
}

export function isFullMonth(startDate, endDate) {
    const firstDay = new Date(startDate.getFullYear(), startDate.getMonth(), 1);
    const lastDay = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);

    return startDate.getTime() === firstDay.getTime() && endDate.getTime() === lastDay.getTime();
}

export function getYesterdayDate() {
    const yesterday = getTodayDate();
    yesterday.setDate(yesterday.getDate() - 1);
    return yesterday;
}

export function isSameDate(dateRange) {
    const d1 = dateRange[0];
    const d2 = dateRange[1];
    return d1.getFullYear() === d2.getFullYear() && d1.getMonth() === d2.getMonth() && d1.getDate() === d2.getDate();
}

export function isSameMonth(date1, date2) {
    if (!(date1 instanceof Date) || !(date2 instanceof Date)) return false;

    return date1.getFullYear() === date2.getFullYear() && date1.getMonth() === date2.getMonth();
}

export function convertToDateFromString(dateString) {
    const formattedDate = dateString.replace(' ', 'T');
    return new Date(formattedDate);
}

export function formatDateForAPI(dateString) {
    const [datePart, timePart] = dateString.split(' ');
    const [day, month, year] = datePart.split('-');
    return `20${year}-${month}-${day} ${timePart}`;
}
export function formatFriendlyPeriod(date) {
    return capitalizeDayAndMonth(
        date
            .toLocaleDateString('es-ES', {
                month: 'long',
                year: 'numeric'
            })
            .toString()
    );
}

export function getWeekNumber(date) {
    const tempDate = new Date(date.getTime());
    tempDate.setHours(0, 0, 0, 0);
    tempDate.setDate(tempDate.getDate() + 3 - ((tempDate.getDay() + 6) % 7));
    const week1 = new Date(tempDate.getFullYear(), 0, 4);
    return 1 + Math.round(((tempDate.getTime() - week1.getTime()) / 86400000 - 3 + ((week1.getDay() + 6) % 7)) / 7);
}

export function formatFriendlyWeek(date) {
    const weekNumber = getWeekNumber(date);
    const year = date.getFullYear();
    return `Semana ${weekNumber} de ${year}`;
}

export function formatFriendlyDateNoYear(date) {
    return capitalizeDayAndMonth(
        date.toLocaleDateString('es-ES', {
            weekday: 'long',
            day: 'numeric',
            month: 'long'
        })
    );
}

export function getCurrentDatesOfWeek() {
    const today = getTodayDate();
    const dayOfWeek = today.getDay();

    const diffToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek;

    const monday = new Date(today);
    monday.setDate(today.getDate() + diffToMonday);

    const weekDates = Array.from({ length: 7 }, (_, i) => {
        const date = new Date(monday);
        date.setDate(monday.getDate() + i);
        return date;
    });

    return weekDates;
}

export function getCurrentMonthRange() {
    const today = getTodayDate();

    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    return {
        firstDay,
        lastDay
    };
}

export function getTodayDateRange() {
    return [getTodayDate(), getTodayDate()];
}

export function getYesterdayDateRange() {
    return [getYesterdayDate(), getYesterdayDate()];
}

export function getCurrentWeekDateRange() {
    const weekDates = getCurrentDatesOfWeek();
    return [weekDates[0], weekDates[6]];
}

export function getCurrentMonthDateRange() {
    const { firstDay, lastDay } = getCurrentMonthRange();
    return [firstDay, lastDay];
}

export function getDateAndTime(date) {
    const dateStr = date.toLocaleDateString('es-PE', {
        day: 'numeric',
        month: 'long'
    });

    const timeStr = date.toLocaleTimeString('es-PE', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
    });
    return { date: dateStr, time: timeStr };
}

export const TODAY_DATE_RANGE = [...getTodayDateRange()];
export const YESTERDAY_DATE_RANGE = [...getYesterdayDateRange()];
export const WEEK_DATE_RANGE = [...getCurrentWeekDateRange()];
export const MONTH_DATE_RANGE = [...getCurrentMonthDateRange()];
