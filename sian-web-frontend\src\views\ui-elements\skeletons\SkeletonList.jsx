import { List, ListItemButton, ListItemText, Divider, Skeleton, Stack } from '@mui/material';

export default function SkeletonList({
    items = 10,
    columns = [
        { variant: 'circular', width: 30, height: 30 },
        { variant: 'text', width: '70%', height: 20 }
    ]
}) {
    return (
        <List
            component="nav"
            aria-label="main mailbox folders"
            sx={{
                '& svg': {
                    width: 32,
                    my: -0.75,
                    ml: -0.75,
                    mr: 0.75
                }
            }}
        >
            {Array.from({ length: items }).map((_, index) => (
                <div key={index}>
                    <ListItemButton>
                        <ListItemText
                            primary={
                                <Stack direction="row" justifyContent="space-between" alignItems="center" width="100%">
                                    {columns.map((col, i) => (
                                        <Skeleton
                                            key={i}
                                            variant={col.variant || 'text'}
                                            width={col.width || '100%'}
                                            height={col.height || 20}
                                            sx={col.sx}
                                        />
                                    ))}
                                </Stack>
                            }
                        />
                    </ListItemButton>
                    <Divider />
                </div>
            ))}
        </List>
    );
}
