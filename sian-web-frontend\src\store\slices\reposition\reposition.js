import { dispatch } from '../../index';
import { createSlice } from '@reduxjs/toolkit';
import { CLASIFICATION, PROVIDER } from 'ui-component/filters/RepositionFilter';
import {
    exportOcToExcel,
    exportOtaToExcel,
    getRepositionByProductPromise,
    getRepositionBySupplyPromise,
    getRepositionPromise,
    getRotationPromise,
    getSupplysPromise
} from 'services/repositionService';
import { getDifferenceInDays, parseStringDateToDate } from 'utils/dates';
import { isArray } from 'lodash';
import { round } from 'utils/number';
import { FOOD_VALUE, MERCHANDISE_FOOD_VALUE, PROYECTION, RAW_MATERIAL } from 'models/Reposition';
import { UNIT_EQUIVALENCE } from 'models/Presentation';
import { processRawMaterialData } from '../../../views/logistic/reposition/others/utils/dataProcessing';

const initialState = {
    error: null,
    code: null,
    message: '',
    data: [],
    merchandiseFoodData: [],
    supplyData: [],
    formData: {},
    formSupplyData: {},
    selectedEntries: [],
    cart: {},
    links: {},
    meta: {},
    filters: {},
    loading: false,
    page: 1,
    rotationData: {},
    exportLoading: false,
    rotationLoading: false,
    pageSize: 50,
    totalRecords: 0,
    totalPages: 0,
    selected: {},
    isOpenRotation: false,
    loadingProyection: false
};

const slice = createSlice({
    name: 'reposition',
    initialState,
    reducers: {
        hasError(state, action) {
            state.error = action.payload;
        },

        getDataSuccess(state, action) {
            state.data = [...action.payload];
        },

        getMerchandiseFoodDataSuccess(state, action) {
            state.merchandiseFoodData = [...action.payload];
        },

        getSupplyDataSuccess(state, action) {
            state.supplyData = [...action.payload];
        },

        getFormDataSuccess(state, action) {
            state.formData = { ...action.payload };
        },

        startLoading(state) {
            state.loading = true;
        },

        endLoading(state) {
            state.loading = false;
        },

        startRotationLoading(state) {
            state.rotationLoading = true;
        },

        endRotationLoading(state) {
            state.rotationLoading = false;
        },

        startExportLoading(state) {
            state.exportLoading = true;
        },

        endExportLoading(state) {
            state.exportLoading = false;
        },

        setPage(state, action) {
            state.page = action.payload;
        },

        setPageSize(state, action) {
            state.pageSize = action.payload;
            state.page = 1;
        },

        setTotals(state, action) {
            state.totalRecords = action.payload.totalRecords;
            state.totalPages = action.payload.totalPages;
        },

        setFormSupplyData(state, action) {
            state.formSupplyData = action.payload;
        },

        updateData(state, action) {
            const { pk, newData } = action.payload;
            const index = state.data.findIndex((item) => item.product_id === pk);
            if (index > -1) {
                state.data[index] = { ...state.data[index], ...newData };
            }
        },

        updateFormData(state, action) {
            const { pk, newData } = { ...action.payload };
            const object = state.formData[pk];
            if (object) {
                state.formData[pk] = { ...object, ...newData };
            } else {
                state.formData[pk] = { pk, ...newData };
            }
        },

        updateFormSupplyData(state, action) {
            const { pk, newData } = { ...action.payload };
            const object = state.formSupplyData[pk];
            if (object) {
                state.formSupplyData[pk] = { ...object, ...newData };
            } else {
                state.formSupplyData[pk] = { pk, ...newData };
            }
        },

        removeFormSupplyDataItem(state, action) {
            const pk = action.payload;
            if (state.formSupplyData[pk]) {
                const { [pk]: _, ...remainingFormSupplyData } = state.formSupplyData;
                state.formSupplyData = remainingFormSupplyData;
            }
        },

        updateSomeFormData(state, action) {
            const { pks, newData } = { ...action.payload };

            pks.forEach((pk) => {
                const object = state.formData[pk];

                if (object) {
                    state.formData[pk] = { ...object, ...newData };
                } else {
                    state.formData[pk] = { pk, ...newData };
                }
            });
        },

        setCart(state, action) {
            state.cart = action.payload;
        },

        addToCart(state, action) {
            const item = action.payload;
            if (item.pk && !state.cart[item.pk]) {
                state.cart[item.pk] = item;
            }
        },

        removeFromCart(state, action) {
            const pk = action.payload;
            if (state.cart[pk]) {
                delete state.cart[pk];
            }
        },

        editToCart(state, action) {
            const { pk, updatedData } = action.payload;

            if (state.cart[pk]) {
                state.cart[pk] = { ...state.cart[pk], ...updatedData };
            }
        },

        editSomeToCart(state, action) {
            const { pks, updatedData } = action.payload;

            pks.forEach((pk) => {
                if (state.cart[pk]) {
                    state.cart[pk] = { ...state.cart[pk], ...updatedData };
                }
            });
        },

        clearCart(state) {
            state.cart = {};
        },

        setRotationData(state, action) {
            state.rotationData = { ...action.payload };
        },

        setFilters(state, action) {
            state.filters = { ...action.payload };
        },

        openRotationModal(state) {
            state.isOpenRotation = true;
        },

        closeRotationModal(state) {
            state.isOpenRotation = false;
        },

        setSelected(state, action) {
            state.selected = { ...action.payload };
        },

        addSelectedEntries(state, action) {
            const payload = action.payload;

            if (Array.isArray(payload)) {
                state.selectedEntries = [...state.selectedEntries, ...payload];
            } else {
                state.selectedEntries = [...state.selectedEntries, payload];
            }
        },

        removeSelectedEntry(state, action) {
            const entryToRemove = action.payload;
            state.selectedEntries = state.selectedEntries.filter((entry) => entry !== entryToRemove);
        },

        clearSelectedEntries(state) {
            state.selectedEntries = [];
        },

        startProyectionLoading(state) {
            state.loadingProyection = true;
        },

        endProyectionLoading(state) {
            state.loadingProyection = false;
        }
    }
});

export default slice.reducer;

export const setCart = (data) => async () => dispatch(slice.actions.setCart(data));
export const setSupplyCart = (data) => async () => dispatch(slice.actions.setFormSupplyData(data));

export const addSelectedEntries = (newSelectedEntries) => async () => {
    dispatch(slice.actions.addSelectedEntries(newSelectedEntries));
};

export const removeSelectedEntry = (entryToRemove) => async () => {
    dispatch(slice.actions.removeSelectedEntry(entryToRemove));
};

export const clearSelectedEntries = () => async () => {
    dispatch(slice.actions.clearSelectedEntries());
};

export const clearCart = () => async () => {
    dispatch(slice.actions.clearCart());
};

export const clearSlice = () => async () => {
    dispatch(slice.actions.setTotals({ totalRecords: 0, totalPages: 0 }));
    dispatch(slice.actions.getDataSuccess([]));
    dispatch(slice.actions.setPage(1));
    dispatch(slice.actions.setPageSize(20));
};

export const getRepositionData = (pageSize, page, filters) => async (d, getState) => {
    if (filters.mode === MERCHANDISE_FOOD_VALUE) {
        dispatch(slice.actions.startProyectionLoading());
    } else {
        dispatch(slice.actions.startLoading());
    }

    const { cart } = getState().reposition;

    const productEquivalenceMap = Object.keys(cart).reduce((acc, key) => {
        const { product_id, equivalence } = cart[key];
        acc[product_id] = equivalence;
        return acc;
    }, {});

    try {
        const rawFilters = { ...filters, daysToOrder: getDifferenceInDays(new Date(), parseStringDateToDate(filters.dateToOrder)) };
        const filterType = rawFilters.filterType;

        delete rawFilters.filterType;
        delete rawFilters.dateToOrder;

        if (filterType === CLASIFICATION) {
            delete rawFilters.provider;
        }

        if (filterType === PROVIDER) {
            delete rawFilters.division;
            delete rawFilters.line;
            delete rawFilters.subline;
        }

        if (rawFilters.mode !== MERCHANDISE_FOOD_VALUE) {
            dispatch(slice.actions.getDataSuccess([]));
        } else {
            dispatch(slice.actions.getMerchandiseFoodDataSuccess([]));
        }
        clearSlice();

        const data = await getRepositionPromise({ ...rawFilters, store: 0, pageSize, page });

        if (data?.success === true) {
            const listData = data.data.map((item, index) => {
                const globalIndex = (page - 1) * pageSize + index;
                const pk = item.product_id;
                const equivalence =
                    filters.mode === FOOD_VALUE ? UNIT_EQUIVALENCE : productEquivalenceMap[item.product_id] || item.equivalence_default;
                const presentation =
                    filters.mode === FOOD_VALUE
                        ? item.presentation
                        : item.presentations[equivalence] || item.presentations[item.equivalence_default];

                const mainQuantity =
                    parseFloat(item.unit_quantity_proyected) - (parseFloat(item.purchase_stock) + parseFloat(item.supplying_stock));
                const itemData = {
                    ...item,
                    rowIndex: index,
                    globalIndex,
                    presentation,
                    presentations: item.presentations,
                    equivalence,
                    default_equivalence: item.equivalence_default,
                    quantity: parseFloat(item.unit_quantity_order).toFixed(4),
                    unit_quantity: parseFloat(item.unit_quantity_order),
                    unit_price: parseFloat(item.unit_price),
                    rotation_indicator: item.rotation_indicator,
                    default_unit_price: parseFloat(item.unit_price),
                    provider: item.provider,
                    pk,
                    id: pk
                };

                if (item.analisys && isArray(item.analisys) && rawFilters.mode === FOOD_VALUE) {
                    item.analisys.forEach((storeAnalisys) => {
                        const requirementQuantity = storeAnalisys.unit_quantity_proyected - parseFloat(storeAnalisys.purchase_stock);
                        if (requirementQuantity > 0) {
                            const pk = `${storeAnalisys.product_id}-${storeAnalisys.store_id}`;
                            const newData = {
                                quantity_ota: requirementQuantity,
                                hasTouch: true,
                                product_id: storeAnalisys.product_id,
                                product_name: storeAnalisys.product_name,
                                store_id: storeAnalisys.store_id,
                                store_name: storeAnalisys.store_name,
                                equivalence: UNIT_EQUIVALENCE,
                                unit_price: parseFloat(item.unit_price),
                                presentation
                            };
                            dispatch(slice.actions.updateFormSupplyData({ pk, newData }));
                        }
                    });
                }

                if (mainQuantity > 0 && rawFilters.mode === FOOD_VALUE) {
                    dispatch(
                        slice.actions.updateFormSupplyData({
                            pk: item.product_id,
                            newData: {
                                product_id: item.product_id,
                                product_name: item.product_name,
                                quantity_oc: round(mainQuantity),
                                provider: item.provider ?? 'SIN PROVEEDOR',
                                provider_id: item.provider_id ?? '0000',
                                provider_number: item.provider_number ?? '**********',
                                equivalence: UNIT_EQUIVALENCE,
                                presentation: item.presentation,
                                unit_price: item.unit_price
                            }
                        })
                    );
                }

                return itemData;
            });

            if (rawFilters.mode === MERCHANDISE_FOOD_VALUE) {
                dispatch(slice.actions.getMerchandiseFoodDataSuccess(listData));
            } else {
                // Procesar datos para RAW_MATERIAL mode agregando proyección final calculada
                let processedData = listData;
                if (rawFilters.mode === FOOD_VALUE && rawFilters.foodMode === RAW_MATERIAL) {
                    processedData = processRawMaterialData(
                        listData,
                        (payload) => dispatch(slice.actions.updateFormSupplyData(payload)),
                        (pk) => dispatch(slice.actions.removeFormSupplyDataItem(pk))
                    );
                }

                dispatch(slice.actions.getDataSuccess(processedData));
                dispatch(slice.actions.setTotals({ totalRecords: data.pagination.totalRecords, totalPages: data.pagination.totalPages }));
            }
        }
    } catch (error) {
        console.error({ error });
        dispatch(slice.actions.hasError(error));
    } finally {
        if (filters.mode === MERCHANDISE_FOOD_VALUE) {
            dispatch(slice.actions.endProyectionLoading());
        } else {
            dispatch(slice.actions.endLoading());
        }
    }
};

export const getRepositionDataByProduct =
    (product, filters, stores = []) =>
    async (d, getState) => {
        try {
            const paramns = {
                daysToOrder: getDifferenceInDays(new Date(), parseStringDateToDate(filters.dateToOrder)),
                ...filters,
                product,
                store: 0
            };

            const { data } = await getRepositionByProductPromise({ ...paramns, pageSize: 100, page: 1 });
            const { data: productsData } = getState().reposition;

            if (data?.success === true) {
                const productData = productsData.find((item) => item.product_id === product);

                const listData = data.data.map((item, index) => {
                    const pk = `${item.product_id}-${item.store_id}`;
                    return {
                        ...item,
                        rowIndex: index,
                        quantity: parseFloat(item.unit_quantity_order).toFixed(4),
                        unit_quantity: parseFloat(item.unit_quantity_order),
                        unit_price: parseFloat(item.unit_price),
                        rotation_indicator: item.rotation_indicator,
                        default_unit_price: parseFloat(item.unit_price),
                        provider: item.provider,
                        pk
                    };
                });

                stores.forEach((store) => {
                    const existingItem = listData.find((item) => item.store_id === store.store_id);

                    if (!existingItem) {
                        listData.push({
                            notAvailable: true,
                            indicator_calculation_id: 4,
                            store_id: store.store_id,
                            store_name: store.store_name,
                            product_id: product,
                            product_name: productData.product_name,
                            measure_name: productData.measure_name,
                            equivalence_default: productData.equivalence_default,
                            measure_default: productData.measure_default,
                            provider_id: productData.provider_id,
                            provider_number: productData.provider_number,
                            provider: productData.provider,
                            warehouse_id: store.warehouse_id,
                            unit_price: productData.unit_price,
                            expires: 0,
                            vcto_alert: '-',
                            rotation_scale: '-',
                            rotation_value: 0,
                            rotation_indicator: 'NR(0)',
                            rotation_color: '',
                            obsolete: 0,
                            obsolete_indicator: '-',
                            pareto_percentage_sale: '0.0',
                            pareto_percentage_utility: '0.0',
                            stock: '0.00',
                            to_enter: '0.00',
                            to_dispatch: '0.00',
                            purchase_stock: '0.00',
                            average_quantity: '0.00',
                            average_diary: '0.0000',
                            inventory_days: 0,
                            break_value: 0,
                            break_scale: '-',
                            min_stock: 0,
                            reposition_stock: 0,
                            unit_quantity_order: 0,
                            rowIndex: 0,
                            quantity: '0',
                            unit_quantity: 0,
                            default_unit_price: productData.default_unit_price,
                            pk: `${product}-${store.store_name}`
                        });
                    }
                });

                listData.sort((a, b) => a.warehouse_id - b.warehouse_id);

                return listData;
            }
        } catch (error) {
            dispatch(slice.actions.hasError(error));
        } finally {
            dispatch(slice.actions.endLoading());
        }
        return [];
    };

export const getRepositionDataBySupply =
    (product, stores = [], daysMinStock, daysOfReposition) =>
    async (d, getState) => {
        try {
            const paramns = {
                product,
                store: 0,
                daysMinStock,
                daysOfReposition
            };

            const { data } = await getRepositionBySupplyPromise({ ...paramns, pageSize: 100, page: 1 });
            const { data: productsData } = getState().reposition;

            if (data?.success === true) {
                const productData = productsData.find((item) => item.product_id === product);

                const listData = data.data.map((item, index) => {
                    const pk = `${item.product_id}-${item.store_id}`;
                    return {
                        ...item,
                        rowIndex: index,
                        quantity: parseFloat(item.unit_quantity_order).toFixed(4),
                        unit_quantity: parseFloat(item.unit_quantity_order),
                        unit_price: parseFloat(item.unit_price),
                        rotation_indicator: item.rotation_indicator,
                        default_unit_price: parseFloat(item.unit_price),
                        provider: item.provider,
                        pk
                    };
                });

                stores.forEach((store) => {
                    const existingItem = listData.find((item) => item.store_id === store.store_id);

                    if (!existingItem) {
                        listData.push({
                            notAvailable: true,
                            indicator_calculation_id: 4,
                            store_id: store.store_id,
                            store_name: store.store_name,
                            product_id: product,
                            product_name: productData.product_name,
                            measure_name: productData.measure_name,
                            equivalence_default: productData.equivalence_default,
                            measure_default: productData.measure_default,
                            provider_id: productData.provider_id,
                            provider_number: productData.provider_number,
                            provider: productData.provider,
                            warehouse_id: store.warehouse_id,
                            unit_price: productData.unit_price,
                            expires: 0,
                            vcto_alert: '-',
                            rotation_scale: '-',
                            rotation_value: 0,
                            rotation_indicator: 'NR(0)',
                            rotation_color: '',
                            obsolete: 0,
                            obsolete_indicator: '-',
                            pareto_percentage_sale: '0.0',
                            pareto_percentage_utility: '0.0',
                            stock: '0.00',
                            to_enter: '0.00',
                            to_dispatch: '0.00',
                            purchase_stock: '0.00',
                            average_quantity: '0.00',
                            average_diary: '0.0000',
                            inventory_days: 0,
                            break_value: 0,
                            break_scale: '-',
                            min_stock: 0,
                            reposition_stock: 0,
                            unit_quantity_order: 0,
                            rowIndex: 0,
                            quantity: '0',
                            unit_quantity: 0,
                            default_unit_price: productData.default_unit_price,
                            pk: `${product}-${store.store_name}`
                        });
                    }
                });

                listData.sort((a, b) => a.warehouse_id - b.warehouse_id);

                return listData;
            }
        } catch (error) {
            dispatch(slice.actions.hasError(error));
        } finally {
            dispatch(slice.actions.endLoading());
        }
        return [];
    };

export const getRotationData = (product_id, store_id) => async () => {
    dispatch(slice.actions.startRotationLoading());
    try {
        const data = await getRotationPromise(product_id, store_id);

        if (data?.success === true) {
            const { values } = data.data;

            const dataRotation = {
                limit: parseFloat(values[0].limit),
                data: values.map((item) => ({ ...item, Cantidad: parseFloat(item.quantity) }))
            };
            dispatch(slice.actions.setRotationData(dataRotation));
        } else {
            dispatch(slice.actions.setRotationData({ limit: 0, data: [] }));
        }
    } catch (error) {
        dispatch(slice.actions.hasError(error));
    } finally {
        dispatch(slice.actions.endRotationLoading());
    }
};

export const exportOcData =
    (data, filename = null) =>
    async (dispatch) => {
        try {
            dispatch(slice.actions.startExportLoading());

            await exportOcToExcel(data, filename);
        } catch (error) {
            console.error('Export Error:', error);
            dispatch(slice.actions.hasError(error));
        } finally {
            dispatch(slice.actions.endExportLoading());
        }
    };

export const exportOtaData =
    (data, filename = null) =>
    async (dispatch) => {
        try {
            dispatch(slice.actions.startExportLoading());

            await exportOtaToExcel(data, filename);
        } catch (error) {
            console.error('Export Error:', error);
            dispatch(slice.actions.hasError(error));
        } finally {
            dispatch(slice.actions.endExportLoading());
        }
    };

export const exportAllData =
    (ocData = [], otaData = []) =>
    async () => {
        try {
            const promises = [];

            ocData.forEach((providerData) => {
                promises.push(
                    exportOcToExcel(providerData.products, `OC ${providerData.provider_name} ${new Date().toLocaleDateString()}`)
                );
            });

            otaData.forEach((storeData) => {
                promises.push(exportOtaToExcel(storeData.products, `OTA ${storeData.store_name} ${new Date().toLocaleDateString()}`));
            });

            await Promise.allSettled(promises);
        } catch (error) {
            console.error(error);
        }
    };

export const updateDataItem = (pk, newData) => async () => {
    dispatch(slice.actions.updateData({ pk, newData }));
};

export const updateFormDataItem = (pk, newData) => async () => {
    dispatch(slice.actions.updateFormData({ pk, newData }));
};

export const updateFormSupplyDataItem = (pk, newData) => async () => {
    dispatch(slice.actions.updateFormSupplyData({ pk, newData }));
};

export const removeFormSupplyDataItem = (pk) => async () => {
    dispatch(slice.actions.removeFormSupplyDataItem(pk));
};

export const addToCart = (item) => async () => {
    dispatch(slice.actions.addToCart(item));
};

export const removeFromCart = (rowIndex) => async () => {
    dispatch(slice.actions.removeFromCart(rowIndex));
};

export const editToCart = (pk, updatedData) => async () => {
    dispatch(slice.actions.editToCart({ pk, updatedData }));
};

export const setNewPage = (value) => async () => {
    dispatch(slice.actions.setPage(Number(value)));
};

export const setNewPageSize = (value) => async () => {
    dispatch(slice.actions.setPageSize(Number(value)));
};

export const updateFilters = (data) => async () => {
    dispatch(slice.actions.setFilters(data));
};

export const updateFormDataItems =
    (pks = [], newData) =>
    async () => {
        dispatch(slice.actions.updateFormData({ pks, newData }));
    };

export const updateCartItems =
    (pks = [], newData) =>
    async () => {
        dispatch(slice.actions.editSomeToCart({ pks, newData }));
    };

export const openRotationModal = () => async () => dispatch(slice.actions.openRotationModal());
export const closeRotationModal = () => async () => dispatch(slice.actions.closeRotationModal());
export const setSelectedProduct = (selected) => async () => dispatch(slice.actions.setSelected(selected));

export const getSupplysData = () => async (d, getState) => {
    dispatch(slice.actions.startLoading());

    const { cart } = getState().reposition;

    const productEquivalenceMap = Object.keys(cart).reduce((acc, key) => {
        const { product_id, equivalence } = cart[key];
        acc[product_id] = equivalence;
        return acc;
    }, {});

    try {
        dispatch(slice.actions.getSupplyDataSuccess([]));
        clearSlice();

        const data = await getSupplysPromise(cart);
        console.log({ data });

        if (data?.success === true) {
            const listData = Object.values(data.data).map((item, index) => {
                const pk = item.product_id;
                const equivalence = productEquivalenceMap[item.product_id];
                const presentation = item.presentations[equivalence] || item.presentations[item.equivalence_default];
                return {
                    ...item,
                    rowIndex: index,
                    globalIndex: index,
                    presentation,
                    presentations: item.presentations,
                    equivalence: equivalence || item.equivalence_default,
                    default_equivalence: item.equivalence_default,
                    quantity: parseFloat(item.quantity).toFixed(4),
                    unit_quantity: parseFloat(item.unit_quantity_order),
                    unit_price: parseFloat(item.unit_price),
                    rotation_indicator: item.rotation_indicator,
                    default_unit_price: parseFloat(item.unit_price),
                    provider: item.provider,
                    pk,
                    stock_supplying: Math.floor(Math.random() * (100 - 10 + 1)) + 10,
                    id: pk
                };
            });
            dispatch(slice.actions.getSupplyDataSuccess(listData));
        }
    } catch (error) {
        dispatch(slice.actions.hasError(error));
    } finally {
        dispatch(slice.actions.endLoading());
    }
};
