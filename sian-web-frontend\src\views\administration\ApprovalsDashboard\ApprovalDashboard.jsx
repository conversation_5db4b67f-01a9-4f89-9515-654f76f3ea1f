// material-ui
import { Grid, Tabs, Tab, Box } from '@mui/material';
import MainCard from 'ui-component/cards/MainCard';

// project imports
import GroupsTableCard from './GroupsTableCard';
import ApprovalTableCard from './ApprovalTableCard';
import { gridSpacing } from 'store/constant';

// assets
import Controls from './Controls';
import { getTodayDateRange, TODAY_VALUE } from 'utils/dates';
import { useState } from 'react';
import { getSingleKey } from 'views/commercial/salesDashboard/components/stores/functions';
import ApprovalChart from './ApprovalChart';
import TableViewIcon from '@mui/icons-material/TableView';
import BarChartIcon from '@mui/icons-material/BarChart';
import {
    ADMINISTRATOR_GROUPS_LABEL,
    AMOUNT_VARIANT,
    QUANTITY_VARIANT,
    SUPERVISOR_GROUPS_LABEL,
    SUPERVISOR_TYPE
} from 'models/Confirmation';

// ==============================|| ApprovalDashboard DASHBOARD ||============================== //

const TabPanel = ({ children, value, index, ...other }) => (
    <div
        role="tabpanel"
        hidden={value !== index}
        id={`dashboard-tabpanel-${index}`}
        aria-labelledby={`dashboard-tab-${index}`}
        style={{ height: value === index ? '100%' : 'auto', display: value === index ? 'flex' : 'none', flexDirection: 'column' }}
        {...other}
    >
        {value === index && <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>{children}</Box>}
    </div>
);

const Section = ({ type = SUPERVISOR_TYPE, dateFilters, dateRangeValue }) => {
    const [filters, setFilters] = useState({});
    const [tabValue, setTabValue] = useState(0);
    const isSupervisor = type === SUPERVISOR_TYPE;
    const groupLabel = isSupervisor ? SUPERVISOR_GROUPS_LABEL : ADMINISTRATOR_GROUPS_LABEL;

    const handleTabChange = (_, newValue) => {
        setTabValue(newValue);
    };

    return (
        <Grid container spacing={gridSpacing} sx={{ alignItems: 'stretch' }}>
                <Grid item xs={12} md={9} lg={9} xl={9} sx={{ display: 'flex' }}>
                    <MainCard content={false} sx={{ width: '100%', display: 'flex', flexDirection: 'column' }}>
                        <Box sx={{ borderBottom: 1, borderColor: 'divider', px: 2, pt: 2 }}>
                            <Tabs
                                value={tabValue}
                                onChange={handleTabChange}
                                aria-label="dashboard tabs"
                                sx={{
                                    '& .MuiTab-root': {
                                        minHeight: '48px',
                                        fontSize: { xs: '0.8rem', sm: '0.9rem' },
                                        fontWeight: 500
                                    }
                                }}
                            >
                                <Tab
                                    icon={<BarChartIcon />}
                                    label="Gráficos"
                                    iconPosition="start"
                                    id="dashboard-tab-0"
                                    aria-controls="dashboard-tabpanel-0"
                                />
                                <Tab
                                    icon={<TableViewIcon />}
                                    label="Detalles"
                                    iconPosition="start"
                                    id="dashboard-tab-1"
                                    aria-controls="dashboard-tabpanel-1"
                                />
                            </Tabs>
                        </Box>
                        <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                            <TabPanel value={tabValue} index={0}>
                                <Box sx={{ p: 2 }}>
                                    <Grid container spacing={gridSpacing}>
                                        <Grid item xs={12} lg={6} sx={{ height: '500px' }}>
                                            <ApprovalChart
                                                title={`Cantidad de Aprobaciones en Grupos ${groupLabel}`}
                                                type={type}
                                                variant={QUANTITY_VARIANT}
                                                filters={{ dateRange: [...dateFilters[dateRangeValue]], selectedGroups: { ...filters } }}
                                            />
                                        </Grid>
                                        <Grid item xs={12} lg={6} sx={{ height: '500px' }}>
                                            <ApprovalChart
                                                title={`Monto de Aprobaciones en Grupos ${groupLabel}`}
                                                type={type}
                                                variant={AMOUNT_VARIANT}
                                                filters={{ dateRange: [...dateFilters[dateRangeValue]], selectedGroups: { ...filters } }}
                                            />
                                        </Grid>
                                    </Grid>
                                </Box>
                            </TabPanel>
                            <TabPanel value={tabValue} index={1} sx={{ height: '100%', p: 0 }}>
                                <ApprovalTableCard
                                    title=""
                                    filters={{ dateRange: [...dateFilters[dateRangeValue]], selectedGroups: { ...filters }, type }}
                                    setFilters={setFilters}
                                />
                            </TabPanel>
                        </Box>
                    </MainCard>
                </Grid>
                <Grid item xs={12} md={3} lg={3} xl={3} sx={{ display: 'flex' }}>
                    <Box sx={{ width: '100%' }}>
                        <GroupsTableCard
                            title={`Grupos ${groupLabel}`}
                            type={type}
                            filters={filters}
                            setFilters={setFilters}
                        />
                    </Box>
                </Grid>
            </Grid>
    );
};

export default function ApprovalDashboard() {
    const [dateRange, setDateRange] = useState({ [TODAY_VALUE]: [...getTodayDateRange()] });
    const dateRangeValue = getSingleKey(dateRange);
    const [type, setType] = useState(SUPERVISOR_TYPE);

    return (
        <Grid container spacing={gridSpacing}>
            <Grid item xs={12}>
                <Controls dateRange={dateRange} setDateRange={setDateRange} type={type} setType={setType} />
            </Grid>
            <Grid item xs={12}>
                <Section type={type} dateFilters={dateRange} dateRangeValue={dateRangeValue} />
            </Grid>
        </Grid>
    );
}
