import React, { useEffect, useState } from 'react';
import {
    DATE_RANGE_LABEL,
    DATE_RANGE_VALUE,
    formatFriendlyDate,
    isSameDate,
    MONTH_DATE_RANGE,
    MONTH_LABEL,
    MONTH_VALUE,
    TODAY_DATE_RANGE,
    TODAY_LABEL,
    TODAY_VALUE,
    WEEK_DATE_RANGE,
    WEEK_LABEL,
    WEEK_VALUE,
    YESTERDAY_DATE_RANGE,
    YESTERDAY_LABEL,
    YESTERDAY_VALUE
} from 'utils/dates';
import TodayIcon from '@mui/icons-material/Today';
import DateRangeIcon from '@mui/icons-material/DateRange';
import AdminPanelSettingsIcon from '@mui/icons-material/AdminPanelSettings';
import SupervisedUserCircleIcon from '@mui/icons-material/SupervisedUserCircle';
import DateRangeInput from 'views/commercial/salesDashboard/components/DateRangeInput';
import { getSingleKey } from 'views/commercial/salesDashboard/components/stores/functions';
import { Box, Paper, BottomNavigation, BottomNavigationAction, Divider } from '@mui/material';
import { ADMINISTRATOR_GROUPS_LABEL, ADMINISTRATOR_TYPE, SUPERVISOR_GROUPS_LABEL, SUPERVISOR_TYPE } from 'models/Confirmation';

const typeOptions = [
    {
        label: `Grupos ${SUPERVISOR_GROUPS_LABEL}`,
        value: SUPERVISOR_TYPE,
        icon: <SupervisedUserCircleIcon />
    },
    {
        label: `Grupos ${ADMINISTRATOR_GROUPS_LABEL}`,
        value: ADMINISTRATOR_TYPE,
        icon: <AdminPanelSettingsIcon />
    }
];

const valuesMap = {
    [TODAY_VALUE]: TODAY_DATE_RANGE,
    [YESTERDAY_VALUE]: YESTERDAY_DATE_RANGE,
    [WEEK_VALUE]: WEEK_DATE_RANGE,
    [MONTH_VALUE]: MONTH_DATE_RANGE
};

const options = [
    {
        label: TODAY_LABEL,
        value: TODAY_VALUE,
        icon: <TodayIcon />
    },
    {
        label: YESTERDAY_LABEL,
        value: YESTERDAY_VALUE,
        icon: <TodayIcon />
    },
    {
        label: WEEK_LABEL,
        value: WEEK_VALUE,
        icon: <DateRangeIcon />
    },
    {
        label: MONTH_LABEL,
        value: MONTH_VALUE,
        icon: <DateRangeIcon />
    },
    {
        label: DATE_RANGE_LABEL,
        value: DATE_RANGE_VALUE,
        icon: <DateRangeIcon />
    }
];

const paperStyles = {
    borderRadius: '24px',
    bgcolor: 'transparent',
    p: 1,
    boxShadow: '0 8px 20px rgba(0,0,0,0.2)',
    backgroundColor: 'transparent'
};

const DateControl = ({ sameDate, dateRange, dateRangeValue, setDateRange }) => {
    if (dateRangeValue === DATE_RANGE_VALUE) {
        return (
            <Box sx={{ py: 1 }}>
                <DateRangeInput
                    mainDateRange={MONTH_DATE_RANGE}
                    fontSize="0.8rem"
                    setMainDateRange={(newDateRange) => setDateRange({ [DATE_RANGE_VALUE]: newDateRange })}
                    hideLabel
                    disableSameMonthValidation
                    allowCurrentDate
                />
            </Box>
        );
    }

    if (sameDate) {
        return (
            <Box sx={{ py: 2.2 }}>
                <strong>{formatFriendlyDate(dateRange[0])}</strong>
            </Box>
        );
    }

    return (
        <Box sx={{ py: 2.2 }}>
            Desde &nbsp;
            <strong>{formatFriendlyDate(dateRange[0], { showYear: false })}</strong>&nbsp; hasta &nbsp;
            <strong>{formatFriendlyDate(dateRange[1], { showYear: false })}</strong>
        </Box>
    );
};

const Controls = ({ dateRange, setDateRange, type, setType }) => {
    const [sameDate, setSameDate] = useState(true);

    const dateRangeValue = getSingleKey(dateRange);

    useEffect(() => {
        const dt = dateRange[dateRangeValue];
        setSameDate(isSameDate(dt));
    }, [dateRangeValue]);

    const handleChangeDateRange = (value) => {
        if (value !== DATE_RANGE_VALUE) {
            setDateRange({ [value]: valuesMap[value] });
        } else {
            setDateRange({ [value]: WEEK_DATE_RANGE });
        }
    };

    return (
        <Box sx={{
            display: 'flex',
            width: '100%',
            justifyContent: 'center',
            gap: { xs: 1, sm: 2 },
            flexDirection: { xs: 'column', md: 'row' },
            alignItems: 'center'
        }}>
            <Paper elevation={8} sx={{
                ...paperStyles,
                width: { xs: '100%', sm: '80%', md: '35%', lg: '25%' },
                mb: { xs: 1, md: 0 }
            }}>
                <Box sx={{ borderRadius: '16px', bgcolor: 'white', height: '100%' }}>
                    <BottomNavigation
                        showLabels
                        sx={{
                            borderRadius: '16px',
                            height: '100%',
                            minHeight: '80px',
                            '& .MuiBottomNavigationAction-root': {
                                minWidth: '120px',
                                maxWidth: '200px',
                                padding: '8px 16px'
                            },
                            '& .MuiBottomNavigationAction-label': {
                                fontSize: '0.8rem',
                                fontWeight: 500,
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                            },
                            '& .MuiSvgIcon-root': {
                                fontSize: '1.5rem'
                            }
                        }}
                        value={type}
                        onChange={(_, value) => setType(value)}
                    >
                        {typeOptions.map(({ label, icon, value }) => (
                            <BottomNavigationAction
                                key={value}
                                label={label}
                                icon={icon}
                                value={value}
                            />
                        ))}
                    </BottomNavigation>
                </Box>
            </Paper>
            <Paper elevation={8} sx={{
                ...paperStyles,
                width: { xs: '100%', sm: '80%', md: '55%', lg: '45%' }
            }}>
                <Box sx={{ borderRadius: '16px', bgcolor: 'white' }}>
                    <BottomNavigation
                        showLabels
                        sx={{
                            borderRadius: '16px',
                            '& .MuiBottomNavigationAction-label': {
                                fontSize: { xs: '0.6rem', sm: '0.7rem', md: '0.75rem' }
                            }
                        }}
                        value={dateRangeValue}
                        onChange={(_, value) => handleChangeDateRange(value)}
                    >
                        {options.map(({ label, icon, value }) => (
                            <BottomNavigationAction
                                key={value}
                                label={label}
                                icon={icon}
                                value={value}
                                sx={{
                                    minWidth: { xs: 'auto', sm: 'auto' },
                                    px: { xs: 0.5, sm: 1 }
                                }}
                            />
                        ))}
                    </BottomNavigation>
                    <Divider />
                    <Box sx={{
                        textAlign: 'center',
                        display: 'flex',
                        justifyContent: 'center',
                        px: { xs: 1, sm: 2 }
                    }}>
                        <DateControl
                            sameDate={sameDate}
                            dateRange={dateRange[dateRangeValue]}
                            dateRangeValue={dateRangeValue}
                            setDateRange={setDateRange}
                        />
                    </Box>
                </Box>
            </Paper>
        </Box>
    );
};

export default Controls;
