<?php

namespace App\Http\Controllers\Api\V1\ClubMia;

use App\Http\Controllers\Controller;
use App\Http\Controllers\SianController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class ClubMiaController extends Controller
{
    /**
     * Register a new Club Mia member
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(Request $request)
    {
        try {
            // Validate the request according to SIAN API requirements
            $validator = Validator::make($request->all(), [
                'firstName' => 'required|string|max:255',
                'lastName' => 'required|string|max:255',
                'email' => 'required|string|email|max:255',
                'password' => 'required|string|min:6',
                'phone' => 'required|string|max:20',
                'address' => 'required|string|max:500',
                'birthDate' => 'required|date',
                'dni' => 'required|string|max:20',
                'ruc' => 'nullable|string|max:20',
                'addresses' => 'nullable|array',
                'phones' => 'nullable|array',
                'companyName' => 'nullable|string|max:255',
                'sian_domain' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Extract SIAN domain
            $sianDomain = $data['sian_domain'];

            // Remove SIAN domain from data to send
            unset($data['sian_domain']);

            Log::info('Club Mia Registration Request', [
                'email' => $data['email'],
                'firstName' => $data['firstName'],
                'lastName' => $data['lastName'],
                'dni' => $data['dni']
            ]);

            // Build the SIAN API URL for Club Mia registration
            $url = $sianDomain . '/apiSian/signin/registerClubMia';

            Log::info('SIAN Club Mia Registration URL', ['url' => $url]);

            // Send registration request to SIAN (no authentication required for Club Mia)
            $response = Http::withoutVerifying()->withHeaders([
                'Content-Type' => 'application/json',
            ])->post($url, $data);

            if ($response->successful()) {
                $responseData = $response->json();

                Log::info('SIAN Club Mia Registration Success', [
                    'status' => $response->status(),
                    'response' => $responseData
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Club Mia member registered successfully',
                    'data' => $responseData,
                    'status_code' => $response->status()
                ]);
            } else {
                $errorData = $response->json();

                Log::error('SIAN Club Mia Registration Error', [
                    'status' => $response->status(),
                    'error' => $errorData,
                    'body' => $response->body()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => 'Failed to register Club Mia member in SIAN',
                    'error' => $errorData,
                    'status_code' => $response->status()
                ], $response->status());
            }

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Club Mia Registration Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during Club Mia registration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Login a Club Mia member
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(Request $request)
    {
        try {
            // Validate the request
            $validator = Validator::make($request->all(), [
                'email' => 'required|string|email',
                'password' => 'required|string'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $email = $request->input('email');
            $password = $request->input('password');

            Log::info('Club Mia Login Attempt', ['email' => $email]);

            // TODO: Implement actual authentication logic
            // For now, we'll simulate a successful login
            
            // Here you would typically:
            // 1. Find the member by email
            // 2. Verify the password
            // 3. Generate a token
            // 4. Return member data with token

            /*
            $member = ClubMiaMember::where('email', $email)->first();
            
            if (!$member || !Hash::check($password, $member->password)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid credentials'
                ], 401);
            }

            if (!$member->status) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account is inactive'
                ], 403);
            }

            $token = $member->createToken('club-mia-token')->plainTextToken;
            */

            // Simulated successful response
            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'member_id' => uniqid('clubmia_'),
                    'name' => 'Test Member',
                    'email' => $email,
                    'phone' => '+**********',
                    'status' => 'active',
                    'token' => 'simulated_token_' . uniqid(),
                    'token_type' => 'Bearer',
                    'expires_in' => 3600
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Club Mia Login Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred during login',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get member profile information
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function profile(Request $request)
    {
        try {
            // TODO: Implement authentication middleware and get authenticated member
            // For now, return simulated profile data
            
            return response()->json([
                'success' => true,
                'message' => 'Profile retrieved successfully',
                'data' => [
                    'member_id' => uniqid('clubmia_'),
                    'name' => 'Test Member',
                    'email' => '<EMAIL>',
                    'phone' => '+**********',
                    'birth_date' => '1990-01-01',
                    'gender' => 'M',
                    'document_type' => 'DNI',
                    'document_number' => '12345678',
                    'address' => 'Test Address 123',
                    'status' => 'active',
                    'created_at' => now()->toISOString(),
                    'points' => 150,
                    'membership_level' => 'Bronze'
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Club Mia Profile Error', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'An error occurred retrieving profile',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
