import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Box, Button, TextField } from '@mui/material';
import DateRangeSelector from 'ui-component/extended/DateRangeSelector';
import SearchIcon from '@mui/icons-material/Search';
import { useDispatch, useSelector } from 'store';
import { clearSlice as clearSublineSlice, getSublineItems } from 'store/slices/subline/subline';
import { getLineItems } from 'store/slices/line/line';
import Autocomplete from 'ui-component/inputs/Autocomplete';
import ToggleButton from 'ui-component/inputs/ToggleButton';
import useFilters from 'hooks/useFilters';

export default function RecipeFilter({ setFilters, handleSearch, disabled = true, debounceDelay = 300 }) {
    const dispatch = useDispatch();
    const { data: lineData, loading: lineLoading } = useSelector((state) => state.line);
    const { data: sublineData, loading: sublineLoading } = useSelector((state) => state.subline);

    const [dataFilters, setValue] = useFilters(
        {
            productName: '',
            productId: '',
            dateRangeEmision: null,
            line: [],
            subline: [],
            type: ''
        },
        setFilters
    );

    const [productNameInput, setProductNameInput] = useState('');
    const [productIdInput, setProductIdInput] = useState('');
    const debounceTimeout = useRef(null);

    useEffect(() => {
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        debounceTimeout.current = setTimeout(() => {
            setValue('productName', productNameInput);
        }, debounceDelay);

        return () => {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
        };
    }, [productNameInput, setValue, debounceDelay]);

    useEffect(() => {
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        debounceTimeout.current = setTimeout(() => {
            setValue('productId', productIdInput);
        }, debounceDelay);

        return () => {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
        };
    }, [productIdInput, setValue, debounceDelay]);

    const handleSubmit = useCallback(
        (event) => {
            event.preventDefault();
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
            setValue('productName', productNameInput);
            setValue('productId', productIdInput);
            setTimeout(() => {
                handleSearch();
            }, 50);
        },
        [productNameInput, productIdInput, setValue, handleSearch]
    );

    const loadLine = useCallback(
        (keyword) => {
            const filters = {
                keyword,
                ...(dataFilters.line.length >= 1 && { line: [...dataFilters.line] })
            };

            dispatch(getLineItems(filters));
            dispatch(clearSublineSlice());
        },
        [dataFilters.line, dispatch]
    );

    const loadSubline = useCallback(
        (keyword) => {
            const filters = {
                keyword,
                ...(dataFilters.subline.length >= 1 && { subline: [...dataFilters.subline] }),
                ...(dataFilters.line.length >= 1 && { line: [...dataFilters.line] })
            };

            dispatch(getSublineItems(filters));
        },
        [dataFilters.subline, dataFilters.line, dispatch]
    );

    const toggleButtonItems = useMemo(
        () => [
            { label: 'Carta', value: 'C' },
            { label: 'Insumo', value: 'I' }
        ],
        []
    );

    const handleProductNameChange = useCallback(({ target: { value } }) => {
        setProductNameInput(value);
    }, []);

    const handleProductIdChange = useCallback(({ target: { value } }) => {
        setProductIdInput(value);
    }, []);

    return (
        <form onSubmit={handleSubmit}>
            <Box
                sx={{
                    paddingBottom: 4,
                    display: 'flex',
                    justifyContent: 'space-between',
                    flexDirection: 'row',
                    overflowX: 'auto',
                    width: '100%',
                    p: 1,
                    gap: 1
                }}
            >
                <Box sx={{ display: 'flex', gap: 2, flexDirection: 'column', width: '100%' }}>
                    <Box sx={{ display: 'flex', width: '100%', gap: 1 }}>
                        <TextField
                            label="ID PRODUCTO"
                            name="productId"
                            value={productIdInput}
                            onChange={handleProductIdChange}
                            placeholder="Ej: 123,456,789"
                            helperText="Separar múltiples IDs con comas"
                            fullWidth
                        />
                        <TextField
                            label="NOMBRE PRODUCTO"
                            name="productName"
                            value={productNameInput}
                            onChange={handleProductNameChange}
                            fullWidth
                        />

                        <DateRangeSelector
                            dateRange={dataFilters.dateRangeEmision}
                            label="FECHA DE CREACIÓN"
                            setDateRange={setValue}
                            name="dateRangeEmision"
                        />

                        <Autocomplete
                            label="LÍNEA"
                            name="line"
                            onChange={(value) => setValue('line', value)}
                            options={lineData}
                            loading={lineLoading}
                            onLoad={loadLine}
                        />
                        <Autocomplete
                            label="SUBLÍNEA"
                            name="subline"
                            onChange={(value) => setValue('subline', value)}
                            options={sublineData}
                            loading={sublineLoading}
                            onLoad={loadSubline}
                        />
                    </Box>
                </Box>
                <Button type="submit" variant="contained" color="success" disabled={disabled}>
                    <SearchIcon />
                </Button>
            </Box>
            <Box>
                <ToggleButton
                    label="TIPO"
                    value={dataFilters.type}
                    setValue={(value) => setValue('type', value)}
                    items={toggleButtonItems}
                    allItem
                />
            </Box>
        </form>
    );
}
