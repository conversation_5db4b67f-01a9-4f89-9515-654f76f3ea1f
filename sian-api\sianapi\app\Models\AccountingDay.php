<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

/**
 * Class AccountingDay
 * 
 * @property int $accounting_day_id
 * @property int $accounting_file_id
 * @property int $year
 * @property int $period
 * @property int $day
 * @property bool $opened
 * 
 * @property AccountingPeriod $accounting_period
 * @property Collection|AccountingMovement[] $accounting_movements
 *
 * @package App\Models
 */
class AccountingDay extends Model
{
	protected $table = 'accounting_day';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'accounting_day_id' => 'int',
		'accounting_file_id' => 'int',
		'year' => 'int',
		'period' => 'int',
		'day' => 'int',
		'opened' => 'bool'
	];

	protected $fillable = [
		'accounting_day_id',
		'opened'
	];

	public function accounting_period()
	{
		return $this->belongsTo(AccountingPeriod::class, 'accounting_file_id')
					->where('accounting_period.accounting_file_id', '=', 'accounting_day.accounting_file_id')
					->where('accounting_period.year', '=', 'accounting_day.year')
					->where('accounting_period.period', '=', 'accounting_day.period');
	}

	public function accounting_movements()
	{
		return $this->hasMany(AccountingMovement::class, 'accounting_file_id');
	}
}
