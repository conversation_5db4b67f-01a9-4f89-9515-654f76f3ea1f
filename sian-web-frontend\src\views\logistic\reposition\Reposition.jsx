import { <PERSON><PERSON>, <PERSON>, Button, IconButton, <PERSON>lt<PERSON>, Typo<PERSON>, MenuItem, TextField, Tabs, Tab } from '@mui/material';
import useModal from 'hooks/useModal';
import React, { useEffect, useState, useCallback } from 'react';
import MainCard from 'ui-component/cards/MainCard';
import { useDispatch, useSelector } from 'store';
import CardPagination from 'ui-component/pagination/CardPagination';
import { BlockLoader } from 'ui-component/loaders/loaders';
import RepositionFilter from 'ui-component/filters/RepositionFilter';
import {
    editToCart,
    getRepositionData,
    updateFormDataItem,
    setNewPage,
    setNewPageSize,
    getRotationData,
    clearCart,
    updateDataItem,
    updateFormDataItems,
    updateCartItems,
    closeRotationModal,
    updateFormSupplyDataItem,
    removeFormSupplyDataItem,
    setCart,
    setSupplyCart
} from 'store/slices/reposition/reposition';
import RotationRate from './others/RotationRate';
import ProductionQuantityLimitsIcon from '@mui/icons-material/ProductionQuantityLimits';
import ShoppingCartCheckoutIcon from '@mui/icons-material/ShoppingCartCheckout';
import NestedGrid from 'ui-component/grid/NestedGrid';
import RotationDetail from './others/RotationDetail';
import { ProyeccionFinalMateriaPrimaCell, CReponerMateriaPrimaCell } from './others/RawMaterialRotationDetail';
import RotationResume from './others/RotationResume';
import IAButton from 'ui-component/buttons/IAButton';
import { getStores } from 'store/slices/store/store';
import { FOOD_VALUE, MARKET_VALUE, PROYECTION, SUPPLY, RAW_MATERIAL } from 'models/Reposition';
import { stickyColumn } from 'ui-component/grid/Grid';
import RightAlignedNumber from 'ui-component/grid/RightAlignedNumber';
import Swal from 'sweetalert2';
import DisplayCurrency from 'ui-component/display/DisplayCurrency';
import ErrorIcon from '@mui/icons-material/Error';
import RefreshIcon from '@mui/icons-material/Refresh';
import Proyection from './others/Proyection';
import { UNIT_EQUIVALENCE } from 'models/Presentation';

export default function Reposition({ permissions }) {
    const dispatch = useDispatch();
    const {
        data,
        formData,
        formSupplyData,
        cart,
        loading,
        exportLoading,
        page,
        pageSize,
        totalRecords,
        totalPages,
        rotationLoading,
        rotationData,
        selected,
        isOpenRotation
    } = useSelector((state) => state.reposition);
    const { data: storeData } = useSelector((state) => state.store);

    const [isOpenResume, openResume, closeResume] = useModal();
    const [isOpenProyection, openProyection, closeProyection] = useModal();
    const [filters, setFilters] = useState({});
    const [isServerSideSort, setServerSideSort] = useState('global');
    const [gridMode, setGridMode] = useState(MARKET_VALUE);
    const [supplyTab, setSupplyTab] = useState(SUPPLY);
    const [foodMode, setFoodMode] = useState(SUPPLY);

    const allowForCart = gridMode === FOOD_VALUE ? Object.keys(formSupplyData).length === 0 : Object.keys(cart).length === 0;
    const renderRotationDetail = useCallback((props) => (
        <RotationDetail {...props} foodMode={foodMode} />
    ), [foodMode]);

    const reload = async (sort = null) => {
        if (permissions) {
            const newGridMode = filters.mode;
            const newFoodMode = filters.foodMode;
            const isGridModeChanging = gridMode !== newGridMode;

            const hasItemsInCurrentMode = gridMode === FOOD_VALUE ? Object.keys(formSupplyData).length > 0 : Object.keys(cart).length > 0;

            if (isGridModeChanging && hasItemsInCurrentMode) {
                const result = await Swal.fire({
                    title: '¿Limpiar carrito?',
                    text: `Tienes productos en el carrito del modo ${
                        gridMode === FOOD_VALUE ? 'FOOD' : 'MARKET'
                    }. ¿Deseas limpiarlos antes de cambiar al modo ${newGridMode === FOOD_VALUE ? 'FOOD' : 'MARKET'}?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Sí, limpiar',
                    cancelButtonText: 'No, mantener'
                });

                if (result.isConfirmed) {
                    if (gridMode === FOOD_VALUE) {
                        dispatch(setSupplyCart({}));
                    } else {
                        dispatch(clearCart());
                    }

                    Swal.fire({
                        title: '¡Limpiado!',
                        text: 'El carrito ha sido limpiado.',
                        icon: 'success',
                        timer: 1500,
                        showConfirmButton: false
                    });

                    setGridMode(newGridMode);
                    if (newFoodMode && newGridMode === FOOD_VALUE) {
                        setFoodMode(newFoodMode);
                    }
                    if (sort) {
                        dispatch(getRepositionData(pageSize, page, { ...filters, ...sort }));
                    } else {
                        dispatch(getRepositionData(pageSize, page, filters));
                    }
                }
            } else {
                setGridMode(newGridMode);
                if (newFoodMode && newGridMode === FOOD_VALUE) {
                    setFoodMode(newFoodMode);
                }
                if (sort) {
                    dispatch(getRepositionData(pageSize, page, { ...filters, ...sort }));
                } else {
                    dispatch(getRepositionData(pageSize, page, filters));
                }
            }
        }
    };

    const reloadProyection = () => {
        if (permissions && Object.keys(filters).length > 0) {
            setFilters({ ...filters, _reload: Date.now() });
        }
    };

    const handleEditCart = (pk, updatedData) => {
        dispatch(editToCart(pk, updatedData));
    };

    const handleSortChange = (sortOrder, sortDirection) => {
        reload({ sort: sortOrder, order: sortDirection });
    };

    const handleEditFormItem = (pk, updatedData) => {
        dispatch(updateFormDataItem(pk, updatedData));
    };

    const handleEditFormItems = (pks, updatedData) => {
        dispatch(updateFormDataItems(pks, updatedData));
    };

    const handleEditCartItems = (pks, updatedData) => {
        dispatch(updateCartItems(pks, updatedData));
    };

    useEffect(() => {
        reload();
    }, [pageSize, page]);

    useEffect(() => dispatch(getStores()), []);

    const closeModal = () => dispatch(closeRotationModal({ cart, formData, data, dispatch, handleEditFormItems, handleEditCartItems }));

    const loadRotationRate = () => dispatch(getRotationData(selected.product_id, selected.store));

    const NumberAlert = ({ condition = false, title = '' }) => {
        if (!condition) {
            return null;
        }
        return (
            <Tooltip title={title}>
                <IconButton color="error">
                    <ErrorIcon />
                </IconButton>
            </Tooltip>
        );
    };

    const SupplyQuantityInput = ({ tableMeta }) => {
        const pk = tableMeta.rowData[0];
        const foodCartData = formSupplyData[pk];
        const rowData = data.find((item) => item.pk === pk);
        const [quantityOc, setQuantityOc] = useState(foodCartData?.quantity_oc ?? 0);

        useEffect(() => setQuantityOc(parseFloat(foodCartData?.quantity_oc ?? 0).toFixed(2)), [foodCartData?.quantity_oc]);

        const handleBlur = () => {
            const newValue = foodCartData?.presentation?.allowDecimals === 1 ? quantityOc : parseFloat(quantityOc);
            const fixedValue = Math.floor(newValue) || 0;

            if (parseFloat(fixedValue) <= 0) {
                dispatch(removeFormSupplyDataItem(pk));
            } else if (foodCartData) {
                dispatch(updateFormSupplyDataItem(pk, { quantity_oc: fixedValue, hasTouch: true }));
            } else {
                dispatch(
                    updateFormSupplyDataItem(pk, {
                        product_id: rowData.product_id,
                        product_name: rowData.product_name,
                        quantity_oc: fixedValue,
                        hasTouch: true,
                        provider: rowData.provider ?? 'SIN PROVEEDOR',
                        provider_id: rowData.provider_id ?? '0000',
                        provider_number: rowData.provider_number ?? '**********',
                        equivalence: UNIT_EQUIVALENCE,
                        presentation: rowData.presentations[UNIT_EQUIVALENCE],
                        unit_price: rowData.unit_price
                    })
                );
            }
        };

        const handleChange = ({ target: { value } }) => setQuantityOc(parseFloat(value ?? 0) || 0);

        return (
            <TextField
                variant="outlined"
                sx={{ width: '8rem' }}
                size="small"
                type="number"
                value={quantityOc}
                onChange={handleChange}
                onBlur={handleBlur}
                InputProps={{
                    inputProps: {
                        style: { textAlign: 'right' },
                        step: foodCartData?.presentation?.allowDecimals === 1 ? 0.1 : 1,
                        min: 0,
                        inputMode: foodCartData?.presentation?.allowDecimals === 1 ? 'decimal' : 'numeric',
                        pattern: '[0-9]*'
                    }
                }}
            />
        );
    };

    const marketColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'globalIndex',
            label: 'N°',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => value + 1
            }
        },
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                ...stickyColumn,
                customBodyRender: (value) => (
                    <Typography>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'provider_number',
            label: 'RUC',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'provider',
            label: 'PROVEEDOR',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'stock',
            label: 'STOCK LOCAL',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value) => (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        {value} <NumberAlert condition={parseFloat(value) < 1} title="No hay stock en el Local" />
                    </Box>
                )
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: true
            }
        },
        {
            name: 'to_dispatch',
            label: 'CANT X DES',
            options: {
                filter: true,
                sort: true,
                display: true
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK DISP',
            options: {
                filter: true,
                sort: true,
                display: true
            }
        },
        {
            name: 'supplying_stock',
            label: 'STOCK ABAST',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    let quantityOta = 0;
                    Object.values(cart).forEach((item) => {
                        if (item.product_id === pk && item.quantity_ota) {
                            quantityOta += parseFloat(item.quantity_ota || 0);
                        }
                    });
                    return (
                        <Box
                            sx={{
                                gap: 2,
                                flexDirection: 'row',
                                display: 'inline-block',
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis'
                            }}
                        >
                            <RightAlignedNumber value={value} />
                            <NumberAlert
                                condition={parseFloat(value) < quantityOta}
                                title="La suma de la cantidad de transferencias es mayor a la disponible en el almacen de abastesimiento"
                            />
                        </Box>
                    );
                }
            }
        },
        {
            name: 'quantity',
            label: 'CANT PEDIDO',
            options: {
                filter: false,
                sort: false,
                customBodyRender: (_, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    let quantityOc = 0;
                    Object.values(cart).forEach((item) => {
                        if (item.product_id === pk && item.quantity_oc) {
                            quantityOc += parseFloat(item.quantity_oc || 0);
                        }
                    });

                    return <RightAlignedNumber value={quantityOc} />;
                }
            }
        },
        {
            name: 'quantity',
            label: 'CANT TRANSF',
            options: {
                filter: false,
                sort: false,
                display: false,
                customBodyRender: (_, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    let quantityOta = 0;
                    Object.values(cart).forEach((item) => {
                        if (item.product_id === pk && item.quantity_ota) {
                            quantityOta += parseFloat(item.quantity_ota || 0);
                        }
                    });

                    return <RightAlignedNumber value={quantityOta} />;
                }
            }
        },
        {
            name: 'presentations',
            label: 'PRES',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (presentations, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = data.find((item) => item.pk === pk);
                    const formRowData = Object.keys(formData).find((key) => formData[key].product_id === pk);
                    const cartRowData = Object.keys(cart).find((key) => cart[key].product_id === pk);

                    const handleChange = ({ target: { value } }) => {
                        const unit_price = rowData.default_unit_price * parseFloat(value);
                        const newData = { equivalence: value, presentation: presentations[value], product_id: pk, unit_price };
                        dispatch(updateDataItem(pk, newData));

                        if (formRowData) {
                            const formDataKeys = Object.keys(formData).filter((key) => formData[key].product_id === pk);
                            handleEditFormItems(formDataKeys, newData);
                        }

                        if (cartRowData) {
                            const cartKeys = Object.keys(cart).filter((key) => cart[key].product_id === pk);
                            handleEditCartItems(cartKeys, newData);
                        }
                    };

                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <TextField
                                variant="outlined"
                                value={cartRowData ? cart[cartRowData].equivalence : rowData.equivalence}
                                size="small"
                                onChange={handleChange}
                                disabled={cartRowData}
                                select
                                fullWidth
                            >
                                {Object.keys(presentations).map((key) => {
                                    const { equivalence, measure_name } = presentations[key];
                                    return (
                                        <MenuItem value={equivalence} key={`ITEM_${equivalence}_${measure_name}`}>
                                            {measure_name}
                                        </MenuItem>
                                    );
                                })}
                            </TextField>
                        </Box>
                    );
                }
            }
        },
        {
            name: 'unit_price',
            label: 'P.UNITARIO',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const { unit_price } = data.find((item) => item.pk === pk);
                    const cartRowData = Object.keys(cart).find((cartItem) => cartItem.product_id === pk);
                    const cartUnitPrice = cartRowData ? cartRowData.unit_price : unit_price;

                    return <DisplayCurrency value={cartUnitPrice} currency="pen" />;
                }
            }
        },
        {
            name: 'unit_price',
            label: 'P.TOTAL',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = data.find((item) => item.pk === pk);
                    const cartRowData = Object.keys(cart).find((cartItem) => cartItem.product_id === pk);
                    const quantity = Object.keys(cart).reduce(
                        (accumulator, { quantityOc, equivalence, unit_price, product_id }) =>
                            product_id === pk
                                ? parseFloat(quantityOc ?? 0) * parseFloat(equivalence) * parseFloat(unit_price) + accumulator
                                : accumulator,
                        0
                    );
                    const unit_price = cartRowData ? cartRowData.unit_price : value;
                    const equivalence = cartRowData ? cartRowData.equivalence : rowData.equivalence;
                    const total = parseFloat(unit_price) * parseFloat(equivalence) * parseFloat(quantity);

                    return <DisplayCurrency value={total} currency="pen" />;
                }
            }
        }
    ];

    const foodColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'globalIndex',
            label: 'N°',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => value + 1
            }
        },
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                ...stickyColumn,
                customBodyRender: (value) => (
                    <Typography>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'provider_number',
            label: 'RUC',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'provider',
            label: 'PROVEEDOR',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'to_enter',
            label: 'CANT X ING',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'to_dispatch',
            label: 'CANT X DES',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'waste_info',
            label: '% MERMA',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => {
                    const wasteInfo = value;
                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {
                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;
                    }
                    const percentage = parseFloat(wasteInfo.waste_percentage_total);
                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;
                }
            }
        },
        {
            name: 'unit_quantity_proyected',
            label: 'C.PROYECTADA',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK TIENDAS',
            options: {
                filter: true,
                sort: true,
                display: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'proyeccion_final',
            label: 'PROYECCIÓN FINAL',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const productData = data?.find((item) => item.product_id === pk);
                    const proyeccionFinal = productData?.proyeccion_final || 0;

                    return <RightAlignedNumber value={proyeccionFinal} />;
                }
            }
        },
        {
            name: 'quantity_oc',
            label: 'CANTIDAD A COMPRAR',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (_, tableMeta) => <SupplyQuantityInput tableMeta={tableMeta} />
            }
        },
        {
            name: 'presentations',
            label: 'PRES',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (presentations, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = data.find((item) => item.pk === pk);
                    const foodCartData = formSupplyData[pk];
                    const formRowData = Object.keys(formData).find((key) => formData[key].product_id === pk);
                    const cartRowData = Object.keys(cart).find((key) => cart[key].product_id === pk);

                    const handleChange = ({ target: { value } }) => {
                        const unit_price = rowData.default_unit_price * parseFloat(value);
                        const newData = { equivalence: value, presentation: presentations[value], product_id: pk, unit_price };
                        dispatch(updateDataItem(pk, newData));

                        if (formRowData) {
                            const formDataKeys = Object.keys(formData).filter((key) => formData[key].product_id === pk);
                            handleEditFormItems(formDataKeys, newData);
                        }

                        if (cartRowData) {
                            const cartKeys = Object.keys(cart).filter((key) => cart[key].product_id === pk);
                            handleEditCartItems(cartKeys, newData);
                        }

                        dispatch(updateFormSupplyDataItem(pk, newData));
                    };

                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <TextField
                                variant="outlined"
                                value={foodCartData ? foodCartData.equivalence : UNIT_EQUIVALENCE}
                                size="small"
                                onChange={handleChange}
                                disabled={!foodCartData}
                                select
                                fullWidth
                            >
                                {Object.keys(presentations).map((key) => {
                                    const { equivalence, measure_name } = presentations[key];
                                    return (
                                        <MenuItem value={equivalence} key={`ITEM_${equivalence}_${measure_name}`}>
                                            {measure_name}
                                        </MenuItem>
                                    );
                                })}
                            </TextField>
                        </Box>
                    );
                }
            }
        },
        {
            name: 'unit_price',
            label: 'P.UNITARIO',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (_, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const { unit_price } = data.find((item) => item.pk === pk);
                    const cartRowData = Object.keys(formSupplyData).find((cartItem) => cartItem.product_id === pk);
                    const cartUnitPrice = cartRowData ? cartRowData.unit_price : unit_price;

                    return <DisplayCurrency value={cartUnitPrice} currency="pen" maxDecimals={4} />;
                }
            }
        },
        {
            name: 'unit_price',
            label: 'P.TOTAL',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = data.find((item) => item.pk === pk);
                    const cartRowData = formSupplyData[pk];
                    const quantity = cartRowData ? cartRowData.quantity_oc : 0;
                    const unit_price = cartRowData?.unit_price || rowData?.unit_price || value || 0;
                    const equivalence = cartRowData?.equivalence || rowData?.equivalence || UNIT_EQUIVALENCE;
                    const total = parseFloat(unit_price) * parseFloat(equivalence) * parseFloat(quantity);
                    return <DisplayCurrency value={total} currency="pen" maxDecimals={4} />;
                }
            }
        }
    ];

    const rawMaterialColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '200px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '200px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => (
                    <Typography>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'provider_number',
            label: 'RUC',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'provider',
            label: 'PROVEEDOR',
            options: {
                filter: true,
                sort: true,
                display: false
            }
        },
        {
            name: 'c_reponer',
            label: 'C.REPONER',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (_, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[1];
                    return <CReponerMateriaPrimaCell tableMeta={tableMeta} productId={productId} />;
                }
            }
        },
        {
            name: 'waste_info',
            label: '% MERMA',
            options: {
                filter: true,
                sort: true,
                setCellHeaderProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                setCellProps: () => ({ style: { minWidth: '100px', whiteSpace: 'nowrap' } }),
                customBodyRender: (value) => {
                    const wasteInfo = value;
                    if (!wasteInfo || !wasteInfo.waste_percentage_total) {
                        return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>-</Typography>;
                    }
                    const percentage = parseFloat(wasteInfo.waste_percentage_total);
                    return <Typography sx={{ whiteSpace: 'nowrap', textAlign: 'center' }}>{percentage.toFixed(2)}%</Typography>;
                }
            }
        },
        {
            name: 'proyeccion_final',
            label: 'REPONER TIENDAS',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (value, tableMeta) => {
                    const rowData = tableMeta.rowData;
                    const productId = rowData[1]; // product_id está en la columna 1 en rawMaterialColumns
                    return <ProyeccionFinalMateriaPrimaCell value={value} tableMeta={tableMeta} productId={productId} />;
                }
            }
        },
        {
            name: 'purchase_stock',
            label: 'STOCK TIENDAS',
            options: {
                filter: true,
                sort: true,
                display: false,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'supplying_stock',
            label: 'STOCK A.PRINCIPAL',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => <RightAlignedNumber value={value} />
            }
        },
        {
            name: 'quantity_oc',
            label: 'CANTIDAD A COMPRAR',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (_, tableMeta) => <SupplyQuantityInput tableMeta={tableMeta} />
            }
        },
        {
            name: 'presentations',
            label: 'PRES',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (presentations, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = data.find((item) => item.pk === pk);
                    const foodCartData = formSupplyData[pk];
                    const formRowData = Object.keys(formData).find((key) => formData[key].product_id === pk);
                    const cartRowData = Object.keys(cart).find((key) => cart[key].product_id === pk);

                    const handleChange = ({ target: { value } }) => {
                        const unit_price = rowData.default_unit_price * parseFloat(value);
                        const newData = { equivalence: value, presentation: presentations[value], product_id: pk, unit_price };
                        dispatch(updateDataItem(pk, newData));

                        if (formRowData) {
                            const formDataKeys = Object.keys(formData).filter((key) => formData[key].product_id === pk);
                            handleEditFormItems(formDataKeys, newData);
                        }

                        if (cartRowData) {
                            const cartKeys = Object.keys(cart).filter((key) => cart[key].product_id === pk);
                            handleEditCartItems(cartKeys, newData);
                        }

                        dispatch(updateFormSupplyDataItem(pk, newData));
                    };

                    return (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                            <TextField
                                variant="outlined"
                                value={foodCartData ? foodCartData.equivalence : UNIT_EQUIVALENCE}
                                size="small"
                                onChange={handleChange}
                                disabled={!foodCartData}
                                select
                                fullWidth
                            >
                                {Object.keys(presentations).map((key) => {
                                    const { equivalence, measure_name } = presentations[key];
                                    return (
                                        <MenuItem value={equivalence} key={`ITEM_${equivalence}_${measure_name}`}>
                                            {measure_name}
                                        </MenuItem>
                                    );
                                })}
                            </TextField>
                        </Box>
                    );
                }
            }
        },
        {
            name: 'unit_price',
            label: 'P.UNITARIO',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (_, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const { unit_price } = data.find((item) => item.pk === pk);
                    const cartRowData = Object.keys(formSupplyData).find((cartItem) => cartItem.product_id === pk);
                    const cartUnitPrice = cartRowData ? cartRowData.unit_price : unit_price;

                    return <DisplayCurrency value={cartUnitPrice} currency="pen" maxDecimals={4} />;
                }
            }
        },
        {
            name: 'total_price',
            label: 'P.TOTAL',
            options: {
                filter: true,
                sort: false,
                customBodyRender: (value, tableMeta) => {
                    const pk = tableMeta.rowData[0];
                    const rowData = data.find((item) => item.pk === pk);
                    const cartRowData = formSupplyData[pk];
                    const quantity = cartRowData ? cartRowData.quantity_oc : 0;
                    const unit_price = cartRowData?.unit_price || rowData?.unit_price || value || 0;
                    const equivalence = cartRowData?.equivalence || rowData?.equivalence || UNIT_EQUIVALENCE;
                    const total = parseFloat(unit_price) * parseFloat(equivalence) * parseFloat(quantity);
                    return <DisplayCurrency value={total} currency="pen" maxDecimals={4} />;
                }
            }
        }
    ];

    const getColumns = (gridMode, foodMode) => {
        if (gridMode === FOOD_VALUE) {
            return foodMode === RAW_MATERIAL ? rawMaterialColumns : foodColumns;
        }
        return marketColumns;
    };

    const handleChangeTab = (_, newTabValue) => {
        setSupplyTab(newTabValue);
    };

    return (
        <>
            {isOpenRotation ? (
                <RotationRate
                    isOpen={isOpenRotation}
                    handleClose={closeModal}
                    title="INDICE DE ROTACIÓN"
                    maxWidth="xl"
                    loading={rotationLoading}
                    data={rotationData}
                    load={loadRotationRate}
                    product={selected}
                />
            ) : null}

            {isOpenResume ? (
                <RotationResume
                    isOpen={isOpenResume}
                    handleClose={closeResume}
                    cart={gridMode === FOOD_VALUE ? formSupplyData : cart}
                    setCart={gridMode === FOOD_VALUE ? setSupplyCart : setCart}
                    stores={storeData}
                    gridMode={gridMode}
                />
            ) : null}

            <MainCard sx={{ px: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', pb: '3rem' }}>
                    <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
                        <Typography variant="h1">Reposición</Typography>
                        <IAButton />
                    </Box>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                        <Badge badgeContent={cart.length} color="success">
                            <Tooltip title="Exportar datos del Carrito">
                                <span>
                                    <Button
                                        variant="contained"
                                        color="primary"
                                        disabled={allowForCart || exportLoading === true}
                                        onClick={openResume}
                                    >
                                        {exportLoading ? 'Cargando...' : <ShoppingCartCheckoutIcon />}
                                    </Button>
                                </span>
                            </Tooltip>
                        </Badge>
                        <Tooltip title="Limpiar Carrito">
                            <IconButton color="error" onClick={() => dispatch(clearCart())} disabled={cart.length === 0}>
                                <ProductionQuantityLimitsIcon />
                            </IconButton>
                        </Tooltip>
                    </Box>
                </Box>
                <RepositionFilter
                    setFilters={setFilters}
                    handleSearch={reload}
                    disabled={!permissions}
                    isServerSideSort={isServerSideSort}
                    setServerSideSort={setServerSideSort}
                />
                {gridMode === FOOD_VALUE ? (
                    <Tabs value={supplyTab} onChange={handleChangeTab}>
                        <Tab label="INSUMOS" value={SUPPLY} />
                        <Tab label="PROYECCIÓN" value={PROYECTION} />
                    </Tabs>
                ) : null}
                {loading && supplyTab !== PROYECTION ? <BlockLoader loading /> : null}
                <section style={{ display: loading || supplyTab === PROYECTION ? 'none' : 'block' }}>
                    <NestedGrid
                        columns={getColumns(gridMode, foodMode)}
                        data={data}
                        onSortChange={isServerSideSort === 'global' ? handleSortChange : null}
                        RenderNestedContent={renderRotationDetail}
                    />
                </section>
                <section style={{ display: supplyTab !== PROYECTION ? 'none' : 'block' }}>
                    <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={() => {
                                setFilters({ ...filters });
                            }}
                            disabled={!permissions}
                        >
                            Recargar Proyección
                        </Button>
                    </Box>
                    <Proyection filters={filters} />
                </section>
                {gridMode === FOOD_VALUE ? null : (
                    <CardPagination
                        dataLength={data.length}
                        page={page}
                        pageSize={pageSize}
                        totalPages={totalPages}
                        totalRecords={totalRecords}
                        setPage={(value) => dispatch(setNewPage(value))}
                        setPageSize={(value) => dispatch(setNewPageSize(value))}
                    />
                )}
            </MainCard>
        </>
    );
}
