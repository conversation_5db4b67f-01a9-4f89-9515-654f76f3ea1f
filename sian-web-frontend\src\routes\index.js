import { useRoutes, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';

// routes
import AuthenticationRoutes from './AuthenticationRoutes';
import LoginRoutes from './LoginRoutes';
import MainRoutes from './MainRoutes';
import { getPermisionsPromise } from 'services/permissionsService';

// ==============================|| ROUTING RENDER ||============================== //

function getPermissions(pathname) {
    const pathNameArray = pathname.split('/');
    const module = pathNameArray[1];

    if (pathNameArray[3]) {
        let controller = pathNameArray[3];
        if (controller === 'payment-schedule') {
            controller = 'paymentSchedule';
        }

        if (controller === 'production-cost') {
            controller = 'productionCost';
        }
        if (controller === 'food-sales') {
            controller = 'foodSales';
        }

        if (controller === 'food-sales') {
            controller = 'foodSales';
        }

        if (controller === 'sales-dashboard') {
            controller = 'salesDashboard';
        }

        if (controller === 'sales-dashboard-details') {
            controller = 'salesDashboardDetails';
        }

        if (controller === 'supply-transformation') {
            controller = 'supplyTransformation';
        }

        if (controller === 'link-products') {
            controller = 'linkProducts';
        }

        if (controller === 'supply-stocks') {
            controller = 'supplyStock';
        }

        if (controller === 'approvals') {
            controller = 'approvalsDashboard';
        }

        if (controller) {
            return getPermisionsPromise(module, controller);
        }
    }
    return null;
}

export default function ThemeRoutes() {
    const location = useLocation();
    const [permissions, setPermissions] = useState(null);

    useEffect(() => {
        setPermissions(getPermissions(location.pathname));
    }, [location.pathname]);

    return useRoutes([LoginRoutes, AuthenticationRoutes, MainRoutes(permissions)]);
}
