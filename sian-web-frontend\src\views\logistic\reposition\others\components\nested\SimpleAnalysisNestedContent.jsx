import React from 'react';
import { Box } from '@mui/material';
import MainCard from '../../../../../../ui-component/cards/MainCard';
import DerivedProductAnalysis from './DerivedProductAnalysis';
import { findMainProductWithDerivedProduct, findDerivedProductRecursively } from '../../utils/dataProcessing';

/**
 * Componente para mostrar contenido anidado simple de análisis
 */
const SimpleAnalysisNestedContent = ({ row, data, analisysDerivedProducts }) => {
    const productId = row[0];
    const mainProductData = findMainProductWithDerivedProduct(data, productId);

    return (
        <Box
            sx={{
                display: 'flex',
                flexDirection: 'row',
                gap: 1,
                pb: 1,
                px: 1,
                justifyContent: 'center',
                backgroundColor: '#f5f5f5',
                borderRadius: '0.5rem',
                my: 1
            }}
        >
            <Box sx={{ width: '80%', maxWidth: '1200px' }}>
                <MainCard>
                    <DerivedProductAnalysis row={row} columns={analisysDerivedProducts} />
                </MainCard>
            </Box>
        </Box>
    );
};

export default SimpleAnalysisNestedContent;
