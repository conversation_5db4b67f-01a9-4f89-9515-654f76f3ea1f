<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SpGroupedConfirmations {
    const MODE_SUM = 0;
    const MODE_UNIT = 1;

    public static function execute($startDate = '', $endDate = '', $user_id, $groups = '', $type = 'selected', $group = 'group') {
        $sql = "CALL sp_grouped_confirmations(?, ?, ?, ?, ?, ?)";
        $parameters = [
            $startDate,
            $endDate,
            $user_id,
            $groups,
            $type,
            $group
        ];
        return DB::select($sql, $parameters);
    }

}

