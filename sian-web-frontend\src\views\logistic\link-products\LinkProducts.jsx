import {
    Box,
    Divider,
    Grid,
    Table,
    TableCell,
    TableRow,
    Typography,
    TableBody,
    TableHead,
    IconButton,
    Tooltip,
    Button,
    FormLabel
} from '@mui/material';
import React, { useEffect, useMemo, useState } from 'react';
import MainCard from 'ui-component/cards/MainCard';
import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import {
    getChildrenByIDPromise,
    getChildrensItemsPromise,
    getListPromise,
    getParentsItemsPromise,
    saveLinksPromise
} from 'services/linkProductsService';
import useLoading from 'hooks/useLoading';
import { BlockLoader } from 'ui-component/loaders/loaders';
import AddIcon from '@mui/icons-material/Add';
import SaveIcon from '@mui/icons-material/Save';
import DeleteIcon from '@mui/icons-material/Delete';
import Autocomplete from 'ui-component/inputs/Autocomplete';
import CloudDoneIcon from '@mui/icons-material/CloudDone';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import fireSwal from 'utils/swal';
import CloseIcon from '@mui/icons-material/Close';

const sectionStyle = {
    overflowY: 'auto',
    maxHeight: '80vh',
    display: 'flex',
    flexDirection: 'column'
};

function DeleteButton({ removeProduct }) {
    const [state, setState] = useState(false);

    if (state === true) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', gap: 1 }}>
                <Tooltip title="Cancelar Eliminación">
                    <IconButton onClick={() => setState(false)}>
                        <CloseIcon fontSize="small" color="primary" />
                    </IconButton>
                </Tooltip>
                <Tooltip title="Proceder Eliminación">
                    <IconButton onClick={() => removeProduct()}>
                        <DeleteIcon fontSize="small" color="error" />
                    </IconButton>
                </Tooltip>
            </Box>
        );
    }

    return (
        <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
            <IconButton onClick={() => setState(true)}>
                <DeleteIcon fontSize="small" color="error" />
            </IconButton>
        </Box>
    );
}

function RowParentProduct({ product, setSelectProduct, setProduct, productIds, removeProduct }) {
    const [loading, startLoading, endLoading] = useLoading(false);
    const [options, setOptions] = useState([]);

    const onLoad = (keyword) => {
        let filters = {
            keyword
        };

        if (productIds.length > 0) {
            filters = {
                ...filters,
                product: [...productIds]
            };
        }
        startLoading();
        getParentsItemsPromise({ ...filters })
            .then((data) => setOptions(data))
            .finally(() => endLoading());
    };

    if (product.product_id) {
        return (
            <TableRow sx={{ cursor: 'pointer' }} onClick={() => setSelectProduct(product)} hover>
                <TableCell>{product.product_id}</TableCell>
                <TableCell>
                    <Typography>
                        <strong>{product.product_name}</strong>
                    </Typography>
                </TableCell>
                <TableCell>{product.unit_measure_name}</TableCell>
                <TableCell>
                    <IconButton>
                        <ArrowForwardIosIcon fontSize="small" />
                    </IconButton>
                </TableCell>
            </TableRow>
        );
    }

    return (
        <TableRow sx={{ cursor: 'pointer' }} hover>
            <TableCell colSpan={3}>
                <Autocomplete
                    label="Producto"
                    name="product"
                    loading={loading}
                    onLoad={onLoad}
                    options={options}
                    variant="standard"
                    onChange={(value) => {
                        if (value) {
                            setProduct(value);
                        }
                    }}
                    unique
                />
            </TableCell>
            <TableCell>
                <DeleteButton removeProduct={removeProduct} />
            </TableCell>
        </TableRow>
    );
}

function RowChildrenProduct({ link, setLink, selectedIds, deleteLink, parentId }) {
    const [loading, startLoading, endLoading] = useLoading(false);
    const [options, setOptions] = useState([]);

    const onLoad = (keyword) => {
        let filters = {
            keyword
        };

        if (selectedIds.length > 0) {
            filters = {
                ...filters,
                product: [...selectedIds, parentId]
            };
        }

        startLoading();
        getChildrensItemsPromise({ ...filters })
            .then((data) => setOptions(data))
            .finally(() => endLoading());
    };

    return (
        <TableRow sx={{ cursor: 'pointer' }} hover>
            {link.product_id ? (
                <>
                    <TableCell>
                        {link.product_link_id ? (
                            <Tooltip title="Guardado">
                                <CloudDoneIcon color="success" />
                            </Tooltip>
                        ) : (
                            <Tooltip title="Pendiente de Guardado">
                                <CloudUploadIcon color="primary" />
                            </Tooltip>
                        )}
                    </TableCell>
                    {/* <TableCell>{link.product_id}</TableCell> */}
                    <TableCell>
                        <Typography>
                            <strong>{link.product_name}</strong>
                        </Typography>
                    </TableCell>
                    <TableCell>
                        {parseInt(link.equivalence, 10)} {link.unit_measure_name}
                    </TableCell>
                </>
            ) : (
                <TableCell colSpan={3}>
                    <Autocomplete
                        label="Producto"
                        name="product"
                        loading={loading}
                        onLoad={onLoad}
                        options={options}
                        variant="standard"
                        onChange={(value) => {
                            if (value) {
                                setLink(value);
                                setOptions([]);
                            }
                        }}
                        unique
                    />
                </TableCell>
            )}
            <TableCell>
                <Box sx={{ display: 'flex', gap: 2 }}>
                    <Tooltip title="Eliminar">
                        <DeleteButton removeProduct={deleteLink} />
                    </Tooltip>
                </Box>
            </TableCell>
        </TableRow>
    );
}

function ProductHeader({ product }) {
    return (
        <Box sx={{ mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', mb: 4, mt: 2 }}>
                <Typography variant="h3">{product.product_name}</Typography>
            </Box>
        </Box>
    );
}

function LinkSection({ selectProduct }) {
    const [links, setLinks] = useState([]);
    const [loading, startLoading, endLoading] = useLoading();

    const addLink = () => setLinks((prev) => [...prev, {}]);

    const setLink = (index, product) =>
        setLinks((prev) => prev.map((item, i) => (i === index ? { ...product, product_link_id: null } : item)));

    const deleteLink = (index) => setLinks((prev) => prev.map((item, i) => (i === index ? { ...item, delete: true } : item)));

    const saveLinks = () => {
        const newLinks = links
            .filter((link) => Object.keys(link).length > 0 && link.product_link_id === null)
            .map((link) => ({ product_parent_id: link.product_id, product_id: selectProduct.product_id }));
        const deleteLinks = links
            .filter((link) => Object.keys(link).length > 0 && link.delete === true && link.product_link_id !== null)
            .map((link) => ({ product_parent_id: link.product_id, product_id: selectProduct.product_id }));

        if (newLinks.length === 0 && deleteLinks.length === 0) {
            fireSwal('No hay cambios', 'warning', 'center', false);
        } else {
            const submitData = { newLinks, deleteLinks, product_id: selectProduct.product_id };
            startLoading();
            saveLinksPromise(submitData)
                .then(({ data }) => setLinks(data))
                .finally(() => endLoading());
        }
    };

    useEffect(() => {
        if (selectProduct) {
            startLoading();
            getChildrenByIDPromise(selectProduct.product_id)
                .then((data) => setLinks(data))
                .finally(() => endLoading());
        }
    }, [selectProduct]);

    const productIds = useMemo(() => {
        if (!links || links.length === 0) {
            return [];
        }
        return links.filter((link) => Object.keys(link).length > 0 && link.product_id !== undefined).map((link) => link.product_id);
    }, [links]);

    if (!selectProduct) {
        return (
            <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center' }}>
                <Typography variant="h4">Selecciona un Producto</Typography>
            </Box>
        );
    }

    return (
        <Box>
            <ProductHeader product={selectProduct} />
            <Box sx={{ display: 'flex', justifyContent: 'end', mb: 2, px: 2 }}>
                <Button variant="contained" onClick={() => addLink()}>
                    <AddIcon />
                    Agregar Producto
                </Button>
            </Box>
            <BlockLoader loading={loading}>
                <Table size="small">
                    <TableHead>
                        <TableRow>
                            <TableCell>ESTADO</TableCell>
                            {/* <TableCell>ID</TableCell> */}
                            <TableCell>PRODUCTO</TableCell>
                            <TableCell>PRES</TableCell>
                            <TableCell>CTRL</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {[...links.map((link, index) => ({ ...link, index }))]
                            .filter((link) => link.delete !== true)
                            .map((link) => (
                                <RowChildrenProduct
                                    link={link}
                                    setLink={(product) => setLink(link.index, product)}
                                    selectedIds={productIds}
                                    deleteLink={() => deleteLink(link.index)}
                                    parentId={selectProduct.product_id}
                                />
                            ))}
                    </TableBody>
                </Table>
                <Box sx={{ display: 'flex', justifyContent: 'end', p: 2 }}>
                    <Button color="success" variant="contained" onClick={() => saveLinks()}>
                        <SaveIcon />
                        Guardar
                    </Button>
                </Box>
            </BlockLoader>
        </Box>
    );
}

export default function LinkProducts() {
    const [products, setProducts] = useState([]);
    const [selectProduct, setSelectProduct] = useState();
    const [loading, startLoading, endLoading] = useLoading();

    useEffect(() => {
        startLoading();
        getListPromise()
            .then((data) => setProducts(data))
            .finally(() => endLoading());
    }, []);

    const addProduct = () => setProducts((prev) => [...prev, {}]);
    const setProduct = (index, product) => setProducts((prev) => prev.map((item, i) => (i === index ? product : item)));
    const removeProduct = (index) => setProducts((prev) => prev.filter((_, i) => i !== index));

    const productIds = useMemo(() => {
        if (!products || products.length === 0) {
            return [];
        }
        return products.filter((product) => Object.keys(product).length > 0).map((product) => product.product_id);
    }, [products]);

    return (
        <MainCard title="Vincular Productos">
            <Grid maxHeight="80vh" container spacing={2}>
                <Grid item lg={5} sx={{ ...sectionStyle }}>
                    <Box sx={{ display: 'flex', justifyContent: 'end', mr: 2, mb: 2 }}>
                        <Button onClick={addProduct} color="success" variant="contained">
                            <AddIcon />
                            Agregar Producto
                        </Button>
                    </Box>
                    <BlockLoader loading={loading}>
                        <Table size="small" stickyHeader>
                            <TableHead>
                                <TableRow>
                                    <TableCell>ID</TableCell>
                                    <TableCell>PRODUCTO</TableCell>
                                    <TableCell>PRES</TableCell>
                                    <TableCell />
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {products.map((product, index) => (
                                    <RowParentProduct
                                        key={product.id}
                                        product={product}
                                        setSelectProduct={setSelectProduct}
                                        setProduct={(product) => setProduct(index, product)}
                                        productIds={productIds}
                                        removeProduct={() => removeProduct(index)}
                                    />
                                ))}
                            </TableBody>
                        </Table>
                    </BlockLoader>
                </Grid>
                <Divider />
                <Grid item lg={7} sx={sectionStyle}>
                    <LinkSection selectProduct={selectProduct} />
                </Grid>
            </Grid>
        </MainCard>
    );
}
