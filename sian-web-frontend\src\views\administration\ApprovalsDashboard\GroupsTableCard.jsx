import PropTypes from 'prop-types';
import { useEffect, useState } from 'react';

// material-ui
import { <PERSON>ton, CardActions, Checkbox, Divider, List, ListItemButton, Typography } from '@mui/material';
// third party
import PerfectScrollbar from 'react-perfect-scrollbar';

// project imports
import MainCard from 'ui-component/cards/MainCard';

// assets
import useLoading from 'hooks/useLoading';
import { BlockLoader } from 'ui-component/loaders/loaders';
import SkeletonList from 'views/ui-elements/skeletons/SkeletonList';
import { getGroupsByType } from 'services/administrationService';
import { SUPERVISOR_TYPE } from 'models/Confirmation';

// ===========================|| DASHBOARD ANALYTICS - TOTAL REVENUE CARD ||=========================== //
const Item = ({ group: { user_group_name }, checked, addGroup, removeGroup }) => {
    const toggle = () => (checked ? removeGroup() : addGroup());
    return (
        <>
            <ListItemButton
                onClick={() => toggle()}
                sx={{
                    py: { xs: 0.5, sm: 1 },
                    px: { xs: 1, sm: 2 }
                }}
            >
                <Checkbox
                    checked={checked}
                    onChange={() => toggle()}
                    size="small"
                />
                <Typography sx={{
                    fontSize: { xs: '0.8rem', sm: '0.875rem', md: '1rem' },
                    ml: 1
                }}>
                    {user_group_name}
                </Typography>
            </ListItemButton>
            <Divider />
        </>
    );
};

const GroupsTableCard = ({ title, type = SUPERVISOR_TYPE, filters, setFilters }) => {
    const [loading, startLoading, endLoading] = useLoading(true);
    const [groups, setGroups] = useState([]);
    const [allEntries, setAllEntries] = useState({});
    const clearFilters = () => setFilters({});

    useEffect(() => {
        startLoading();
        getGroupsByType({ type })
            .then((response) => {
                if (response.success) {
                    setGroups(response.data);
                    const allEntries = Object.fromEntries(response.data.map((item) => [item.user_group_id, item.user_group_name]));
                    setFilters(allEntries);
                    setAllEntries(allEntries);
                }
            })
            .catch((ex) => console.error(ex))
            .finally(() => endLoading());
    }, [type]);

    const removeKey = (keyToRemove) => {
        setFilters((prev) => {
            const { [keyToRemove]: _, ...rest } = prev;
            return rest;
        });
    };

    const addKey = (key, value) => {
        setFilters((prev) => ({
            ...prev,
            [key]: value
        }));
    };

    return (
        <MainCard title={title} content={false}>
            <PerfectScrollbar style={{
                height: '32.6rem',
                minHeight: '32.6rem'
            }}>
                <BlockLoader loading={loading} loaderComponent={<SkeletonList />}>
                    <List
                        component="nav"
                        aria-label="main mailbox folders"
                        sx={{
                            '& svg': {
                                width: { xs: 24, sm: 28, md: 32 },
                                my: -0.75,
                                ml: -0.75,
                                mr: 0.75
                            }
                        }}
                    >
                        {groups.map((group, index) => (
                            <Item
                                key={group.user_group_id || index}
                                group={group}
                                checked={filters[group.user_group_id]}
                                addGroup={() => addKey(group.user_group_id, group.user_group_name)}
                                removeGroup={() => removeKey(group.user_group_id)}
                            />
                        ))}
                    </List>
                </BlockLoader>
            </PerfectScrollbar>
            <Divider />
            <CardActions sx={{
                justifyContent: 'space-between',
                p: { xs: 0.5, sm: 1 },
                flexDirection: { xs: 'column', sm: 'row' },
                gap: { xs: 1, sm: 0 }
            }}>
                <Button
                    variant="text"
                    color="primary"
                    size="small"
                    onClick={() => setFilters(allEntries)}
                    sx={{
                        fontSize: { xs: '0.7rem', sm: '0.8rem' },
                        width: { xs: '100%', sm: 'auto' }
                    }}
                >
                    Seleccionar Todos
                </Button>
                <Button
                    variant="text"
                    color="error"
                    size="small"
                    onClick={clearFilters}
                    sx={{
                        fontSize: { xs: '0.7rem', sm: '0.8rem' },
                        width: { xs: '100%', sm: 'auto' }
                    }}
                >
                    Limpiar
                </Button>
            </CardActions>
        </MainCard>
    );
};

GroupsTableCard.propTypes = {
    title: PropTypes.string
};

export default GroupsTableCard;
