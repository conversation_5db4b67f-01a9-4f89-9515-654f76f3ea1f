import { Typography, DialogContent } from '@mui/material';
import React, { useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'store';
import { BlockLoader } from 'ui-component/loaders/loaders';
import { getRepositionData } from 'store/slices/reposition/reposition';
import NestedGrid from 'ui-component/grid/NestedGrid';
import { stickyColumn } from 'ui-component/grid/Grid';
import Modal from 'ui-component/modal/Modal';
import { MERCHANDISE_FOOD_VALUE } from 'models/Reposition';
import RotationDetail from './RotationDetail';

export default function Proyection({ filters = {}, ...props }) {
    const dispatch = useDispatch();
    const { loadingProyection: loading, merchandiseFoodData = [] } = useSelector((state) => state.reposition);

    const reload = (currentFilters = filters) => {
        dispatch(getRepositionData(1000, 1, { ...currentFilters, mode: MERCHANDISE_FOOD_VALUE }));
    };
    const renderRotationDetail = useCallback((props) => (
        <RotationDetail {...props} isFromProyection />
    ), []);

    useEffect(() => {
        if (Object.keys(filters).length > 0) {
            reload(filters);
        }
    }, [filters]);

    const foodMercColumns = [
        {
            name: 'pk',
            label: 'PK',
            options: {
                filter: false,
                sort: false,
                display: false
            }
        },
        {
            name: 'globalIndex',
            label: 'N°',
            options: {
                filter: true,
                sort: true,
                customBodyRender: (value) => value + 1
            }
        },
        {
            name: 'product_id',
            label: 'ID',
            options: {
                filter: true,
                sort: true
            }
        },
        {
            name: 'product_name',
            label: 'PRODUCTO',
            options: {
                filter: true,
                sort: true,
                ...stickyColumn,
                customBodyRender: (value) => (
                    <Typography>
                        <strong>{value}</strong>
                    </Typography>
                )
            }
        },
        {
            name: 'unit_quantity_order',
            label: 'PROYECTADO A VENDER',
            options: {
                filter: false,
                sort: false,
                display: true,
                customBodyRender: (value) => <Typography>{value.toFixed(2)}</Typography>
            }
        }
    ];

    return (
        <>
            {loading ? <BlockLoader loading /> : null}
            <section style={{ display: loading ? 'none' : 'block' }}>
                <NestedGrid
                    columns={foodMercColumns}
                    data={merchandiseFoodData}
                    RenderNestedContent={renderRotationDetail}
                />
            </section>
        </>
    );
}
