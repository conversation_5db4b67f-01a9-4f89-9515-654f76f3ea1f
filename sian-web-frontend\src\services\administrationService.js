import axios from 'utils/axios';
import { executePromise } from './service';
import { administrationGroupsEndpoint } from './apiEnpoints';

export const getGroupsByType = async (filters) => {
    const { user_id } = JSON.parse(localStorage.getItem('user'));
    const filtersUrl = new URLSearchParams({ ...filters, user_id });
    const url = `${administrationGroupsEndpoint}?${filtersUrl}`;
    return executePromise(axios.get(url));
};

export const getApprovalsByType = async (filters) => {
    const { user_id } = JSON.parse(localStorage.getItem('user'));
    const filtersUrl = new URLSearchParams({ ...filters, user_id });
    const url = `${administrationGroupsEndpoint}/approval?${filtersUrl}`;
    return executePromise(axios.get(url));
};

export const getChartDataByType = async (filters) => {
    const { user_id } = JSON.parse(localStorage.getItem('user'));
    const filtersUrl = new URLSearchParams({ ...filters, user_id });
    const url = `${administrationGroupsEndpoint}/chart?${filtersUrl}`;
    return executePromise(axios.get(url));
};
