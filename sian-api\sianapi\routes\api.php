<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

/*Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});*/

Route::prefix('V1')->group(function () {

    // ----------- ACCESS ------------------
    Route::post('menu', [App\Http\Controllers\Api\V1\AccessController::class, 'getMenus']);
    Route::post('module', [App\Http\Controllers\Api\V1\AccessController::class, 'getModules']);
    Route::post('parse-excel', [App\Http\Controllers\Api\ExcelController::class, 'processExcel']);
    Route::get('sian-domain', [App\Http\Controllers\Api\V1\Domain\DomainController::class, 'index']);
    Route::get('hour', [App\Http\Controllers\Api\V1\Hour\HourController::class, 'index']);
    Route::post('send-emails/{limit?}', [App\Http\Controllers\MailController::class, 'sendEmail']);

    // ----------- AUTH ------------------
    Route::prefix('auth')->group(function () {
        Route::post('hash', [App\Http\Controllers\Api\V1\AuthController::class, 'getHash']);
        Route::post('register', [App\Http\Controllers\Api\V1\AuthController::class, 'register']);
        Route::post('login', [App\Http\Controllers\Api\V1\AuthController::class, 'login']);
        Route::post('login-from-sian', [App\Http\Controllers\Api\V1\AuthController::class, 'loginFromSian']);
        Route::post('logout-from-sian', [App\Http\Controllers\Api\V1\AuthController::class, 'logoutFromSian']);
        Route::post('me', [App\Http\Controllers\Api\V1\AuthController::class, 'getUser']);
        Route::post('loginSian', [App\Http\Controllers\Api\V1\AuthController::class, 'loginSian']);
    });

    // ------------------------------------------------
    // -------------------- COMMON --------------------
    // ------------------------------------------------
    Route::prefix('common')->group(function () {
        // -------------- Multitable ------------------
        Route::get('multitable/items', [App\Http\Controllers\Api\V1\Common\MultitableController::class, 'getItems']);
    });

    // ------------------------------------------------
    // ----------------- ACCOUNTING -------------------
    // ------------------------------------------------
    Route::prefix('accounting')->group(function () {
        // ----------- Account ------------------
        Route::get('account/items', [App\Http\Controllers\Api\V1\Accounting\AccountController::class, 'getItems']);
        // ----------- AccountingFile ------------------
        Route::get('year/items', [App\Http\Controllers\Api\V1\Accounting\AccountingFileController::class, 'getYearItems']);
        Route::get('period/items', [App\Http\Controllers\Api\V1\Accounting\AccountingFileController::class, 'getPeriodItems']);
    });

    // ------------------------------------------------
    // ---------------- ADMINISTRATION ----------------
    // ------------------------------------------------
    Route::prefix('administration')->group(function () {

        // ----------- BusinessUnit ------------------
        Route::get('businessUnit/items', [App\Http\Controllers\Api\V1\Administration\BusinessUnitController::class, 'getItems']);

        // ---------------- Person -------------------
        Route::get('person/items', [App\Http\Controllers\Api\V1\Administration\PersonController::class, 'getItems']);

        // ---------------- Groups -------------------
        Route::prefix('group')->group(function () {
            Route::get('', [App\Http\Controllers\Api\V1\Administration\ApprovalController::class, 'getGroupsByType']);
            Route::get('approval', [App\Http\Controllers\Api\V1\Administration\ApprovalController::class, 'getAproovedByType']);
            Route::get('chart', [App\Http\Controllers\Api\V1\Administration\ApprovalController::class, 'getChartData']);
        });
    });

    Route::prefix('page')->group(function () {
        Route::get('permissions', [App\Http\Controllers\Api\V1\Page\PageController::class, 'show']);
    });

    Route::prefix('asset')->group(function () {
        Route::get('depreciation-group/items', [App\Http\Controllers\Api\V1\Asset\DepreciationGroupController::class, 'getItems']);
        Route::get('fixed-asset/items', [App\Http\Controllers\Api\V1\Asset\FixedAssetController::class, 'getItems']);
        Route::get('depreciation/header', [App\Http\Controllers\Api\V1\Asset\DepreciationController::class, 'getHeader']);
        Route::get('depreciation/pre-calculate', [App\Http\Controllers\Api\V1\Asset\DepreciationController::class, 'preCalculate']);
        Route::post('depreciation/calculate', [App\Http\Controllers\Api\V1\Asset\DepreciationController::class, 'calculate']);

        Route::apiResource('fixed-asset', App\Http\Controllers\Api\V1\Asset\FixedAssetController::class);
        Route::apiResource('depreciation-group', App\Http\Controllers\Api\V1\Asset\DepreciationGroupController::class);
        Route::apiResource('depreciation', App\Http\Controllers\Api\V1\Asset\DepreciationController::class);

        Route::get('depreciation-type', [App\Http\Controllers\Api\V1\Asset\FixedAssetController::class, 'getDepreciationTypeItems']);
        Route::get('depreciation-period', [App\Http\Controllers\Api\V1\Asset\FixedAssetController::class, 'getDepreciationPeriodItems']);

        Route::get('movementType/items', [App\Http\Controllers\Api\V1\Asset\FixedAssetMovementController::class, 'getManualMovementTypeItems']);
        Route::apiResource('fixed-asset-movement', App\Http\Controllers\Api\V1\Asset\FixedAssetMovementController::class);
    });

    Route::middleware('jwt.verify')->group(function () {
        // ------------------------------------------------
        // -------------------- ASSET ---------------------
        // ------------------------------------------------


        // ------------------------------------------------
        // ---------------- FINANCIAL ---------------------
        // ------------------------------------------------

        Route::prefix('financial')->group(function () {
            Route::prefix('payment-schedule')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Financial\PaymentScheduleController::class, 'index']);
                Route::post('', [App\Http\Controllers\Api\V1\Financial\PaymentScheduleController::class, 'store']);
                Route::put('', [App\Http\Controllers\Api\V1\Financial\PaymentScheduleController::class, 'update']);
                Route::get('balances', [App\Http\Controllers\Api\V1\Financial\PaymentScheduleController::class, 'getBalances']);
                Route::get('/{paymentScheduleId}', [App\Http\Controllers\Api\V1\Financial\PaymentScheduleController::class, 'show']);
            });

            Route::prefix('payment-schedule-setting')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Financial\ScheduleSettingController::class, 'show']);
                Route::post('', [App\Http\Controllers\Api\V1\Financial\ScheduleSettingController::class, 'store']);
                Route::put('', [App\Http\Controllers\Api\V1\Financial\ScheduleSettingController::class, 'update']);
            });

            Route::prefix('documents')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Financial\DocumentController::class, 'index']);
                Route::get('pay', [App\Http\Controllers\Api\V1\Financial\DocumentController::class, 'getDocumentsForPay']);
                Route::get('feePay', [App\Http\Controllers\Api\V1\Financial\DocumentController::class, 'getDetailFeePay']);

                Route::prefix('person')->group(function () {
                    Route::get('request-payment', [App\Http\Controllers\Api\V1\Financial\DocumentController::class, 'getRequestPaymentPersons']);
                    Route::get('business-partner', [App\Http\Controllers\Api\V1\Financial\DocumentController::class, 'getBussinessPartners']);
                });
            });

            Route::prefix('transaction')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'index']);
                Route::post('', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'store']);
                Route::post('files', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'files']);
                Route::get('box-account', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'getBoxAccountByUser']);
                Route::get('stores', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'getStoresByUser']);
                Route::get('business-unit', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'getBusinessUnitByUser']);
                /*
                    Route::get('projects', [App\Http\Controllers\Api\V1\Financial\TransactionController::class, 'getProjects']);
                    */
            });
            Route::prefix('cashbox')->group(function () {
                Route::get('user', [App\Http\Controllers\Api\V1\Financial\CashboxController::class, 'getBoxAccountByUser']);
            });
        });

        Route::prefix('commercial')->group(function () {
            Route::get('sales-dashboard', [App\Http\Controllers\Api\V1\Commercial\SalesDashboardController::class, 'getSales']);
            Route::get('progress-goals', [App\Http\Controllers\Api\V1\Commercial\SalesDashboardController::class, 'getProgressGoals']);
        });

        Route::prefix('logistic')->group(function () {

            Route::prefix('merchandise')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Merchandise\MerchandiseController::class, 'index']);
                Route::get('food', [App\Http\Controllers\Api\V1\Merchandise\MerchandiseController::class, 'getFoodItems']);
                Route::get('supply', [App\Http\Controllers\Api\V1\Merchandise\MerchandiseController::class, 'getSupplyItems']);
                Route::get('presentations', [App\Http\Controllers\Api\V1\Merchandise\MerchandiseController::class, 'getPresentations']);
            });

            Route::prefix('reposition')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'getReposition']);
                Route::get('product', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'getRepositionByProduct']);
                Route::post('supply', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'getSupplys']);
                Route::get('supply/analisys', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'getRepositionBySupply']);


                Route::prefix('oc')->group(function () {
                    Route::prefix('export')->group(function () {
                        Route::post('excel', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'exportOcExcel']);
                    });
                });
                Route::prefix('ota')->group(function () {
                    Route::prefix('export')->group(function () {
                        Route::post('excel', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'exportOtaExcel']);
                    });
                });
            });

            Route::get('rotation', [App\Http\Controllers\Api\V1\Logistic\IndicatorController::class, 'getRotation']);

            Route::prefix('supply-transformation')->group(function () {
                Route::prefix('documents')->group(function () {
                    Route::get('', [App\Http\Controllers\Api\V1\SupplyTransformation\SupplyTransformationController::class, 'getAvaliableDocuments']);
                    Route::get('/{movement_id}', [App\Http\Controllers\Api\V1\SupplyTransformation\SupplyTransformationController::class, 'getDocumentData']);
                });
                Route::post('', [App\Http\Controllers\Api\V1\SupplyTransformation\SupplyTransformationController::class, 'store']);
            });

            Route::prefix('link-products')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\LinkProducts\LinkProductsController::class, 'index']);
                Route::post('', [App\Http\Controllers\Api\V1\LinkProducts\LinkProductsController::class, 'store']);
                Route::prefix('items')->group(function () {
                    Route::get('parents', [App\Http\Controllers\Api\V1\LinkProducts\LinkProductsController::class, 'getParentItems']);
                    Route::get('childrens', [App\Http\Controllers\Api\V1\LinkProducts\LinkProductsController::class, 'getChildrenItems']);
                });
                Route::get('/{productID}', [App\Http\Controllers\Api\V1\LinkProducts\LinkProductsController::class, 'show']);
            });

            Route::prefix('reports')->group(function () {
                Route::get('supply-stocks', [App\Http\Controllers\Api\V1\SupplyStocks\SupplyStocksController::class, 'report']);
            });
        });

        Route::prefix('restaurant')->group(function () {
            Route::prefix('recipe')->group(function () {
                Route::get('', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'index']);
                Route::post('', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'store']);
                Route::get('/product/{productID}', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'getRecipeByProductID']);


                Route::get('/variables', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'getRecipeVariables']);
                Route::get('/analysis/{recipeID}', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'getCostAnalysis']);
                Route::get('/{recipeID}', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'show']);

                Route::put('/general/{recipeID}', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'updateRecipe']);
                Route::put('/{recipeID}', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'update']);
                Route::delete('/{recipeID}', [App\Http\Controllers\Api\V1\Recipe\RecipeController::class, 'destroy']);
            });

            Route::prefix('production-cost')->group(function () {
                Route::prefix('concept')->group(function () {
                    Route::post('', [App\Http\Controllers\Api\V1\ProductionCostConcept\ProductionCostConceptController::class, 'store']);
                    Route::get('/{id}', [App\Http\Controllers\Api\V1\ProductionCostConcept\ProductionCostConceptController::class, 'show']);
                    Route::put('/{id}', [App\Http\Controllers\Api\V1\ProductionCostConcept\ProductionCostConceptController::class, 'update']);
                    Route::delete('/{id}/{production_cost_id}', [App\Http\Controllers\Api\V1\ProductionCostConcept\ProductionCostConceptController::class, 'destroy']);
                });
                Route::get('', [App\Http\Controllers\Api\V1\ProductionCost\ProductionCostController::class, 'index']);
                Route::post('', [App\Http\Controllers\Api\V1\ProductionCost\ProductionCostController::class, 'store']);
                Route::put('/{id}', [App\Http\Controllers\Api\V1\ProductionCost\ProductionCostController::class, 'update']);
            });

            Route::prefix('food-sale')->group(function () {
                Route::get('report', [App\Http\Controllers\Api\V1\FoodSale\FoodSaleController::class, 'report']);
            });
        });

        Route::prefix('businessUnit')->group(function () {
            Route::get('', [App\Http\Controllers\Api\V1\BusinessUnit\BusinessUnitController::class, 'index']);
        });

        Route::prefix('project')->group(function () {
            Route::get('', [App\Http\Controllers\Api\V1\Project\ProjectController::class, 'index']);
        });

        Route::prefix('store')->group(function () {
            Route::get('', [App\Http\Controllers\Api\V1\Store\StoreController::class, 'index']);
            Route::get('withSales', [App\Http\Controllers\Api\V1\Store\StoreController::class, 'getStoresWithSales']);
            // Route::get('/{storeID}', [App\Http\Controllers\Api\V1\Store\StoreController::class, 'show']);
            // Route::post('', [App\Http\Controllers\Api\V1\Store\StoreController::class, 'store']);
            // Route::put('/{storeID}', [App\Http\Controllers\Api\V1\Store\StoreController::class, 'update']);
            // Route::delete('/{storeID}', [App\Http\Controllers\Api\V1\Store\StoreController::class, 'destroy']);
        });

        Route::prefix('files')->group(function () {
            Route::get('', [App\Http\Controllers\Api\FilesController::class, 'index']);
        });


        Route::prefix('category')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\ServiceCategory\ServiceCategoryController::class, 'getItems']);
        });

        Route::prefix('line')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Line\LineController::class, 'getItems']);
            Route::get('items-food', [App\Http\Controllers\Api\V1\Line\LineController::class, 'getFoodItems']);
        });

        Route::prefix('division')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Division\DivisionController::class, 'getItems']);
        });

        Route::prefix('subline')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Subline\SublineController::class, 'getItems']);
        });

        Route::prefix('mark')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Mark\MarkController::class, 'getItems']);
        });

        Route::prefix('mark')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Mark\MarkController::class, 'getItems']);
        });

        Route::prefix('provider')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Administration\PersonController::class, 'getProviderItems']);
        });

        Route::prefix('exchange-rate')->group(function () {
            Route::get('active', [App\Http\Controllers\Api\V1\ExchangeRate\ExchangeRateController::class, 'hasExchangeRate']);
        });

        Route::prefix('warehouse')->group(function () {
            Route::get('items', [App\Http\Controllers\Api\V1\Warehouse\WarehouseController::class, 'getItems']);
        });

        Route::prefix('movements')->group(function () {
            Route::get('references/{movementID}', [App\Http\Controllers\Api\V1\Movements\MovementsController::class, 'getReferences']);
        });
    });


    // ------------------------------------------------
    // -------------------- HUMAN ---------------------
    // ------------------------------------------------
    Route::prefix('human')->group(function () {
        // ----------------- Area  --------------------
        Route::get('area/items', [App\Http\Controllers\Api\V1\Human\AreaController::class, 'getItems']);
    });

    // ------------------------------------------------
    // ------------------- LOGISTIC -------------------
    // ------------------------------------------------
    Route::middleware('jwt.verify')->prefix('logistic')->group(function () {
        // ----------------- Mark  --------------------
        Route::get('mark/items', [App\Http\Controllers\Api\V1\Logistic\MarkController::class, 'getItems']);
    });
});



Route::prefix('asset')->group(function () {
    // ------------------ FixedAsset ------------------
    Route::prefix('fixedAsset')->group(function () {
        Route::get('export-to-pdf', [App\Http\Controllers\Api\V1\Asset\FixedAssetController::class, 'report']);
        Route::get('export-to-excel', [App\Http\Controllers\Api\V1\Asset\FixedAssetController::class, 'exportToExcel']);
    });
});

// ------------------------------------------------
// ------------------- procedures -------------------
// ------------------------------------------------
Route::prefix('procedures')->group(function () {
    Route::apiResource('type-documentary-procedure', App\Http\Controllers\Api\Procedures\TypeDocumentaryProcedureController::class);
    Route::apiResource('documentary-procedures', App\Http\Controllers\Api\Procedures\DocumentaryProcedureController::class);
    Route::apiResource('documentary-procedure-detail', App\Http\Controllers\Api\Procedures\DocumentaryProcedureDetailController::class);
});
