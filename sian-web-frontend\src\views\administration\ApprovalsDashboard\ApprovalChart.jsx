import React, { useEffect, useState } from 'react';

// material-ui
import { FormControl, Grid, MenuItem, Typography, Skeleton, Box, Select, InputLabel } from '@mui/material';

// third-party
import Chart from 'react-apexcharts';

// project imports
import MainCard from 'ui-component/cards/MainCard';
import { gridSpacing } from 'store/constant';

// chart data
import useLoading from 'hooks/useLoading';
import { getChartDataByType } from 'services/administrationService';
import { parseDateToApi } from 'utils/dates';
import { BlockLoader } from 'ui-component/loaders/loaders';
import DisplayCurrency, { PEN_SYMBOL } from 'ui-component/display/DisplayCurrency';
import { compressString, getRandomColor } from 'views/commercial/salesDashboard/components/stores/functions';
import { AMOUNT_VARIANT, GROUP_VALUE, groupOptions, variantMap } from 'models/Confirmation';

const ValueSkeleton = () => (
    <Grid item>
        <Skeleton variant="text" width={100} height={36} />
    </Grid>
);

const BarChartSkeleton = ({ bars = 6, height = 272, barWidth = 60 }) => {
    const maxYTicks = 5;

    const yTicks = Array.from({ length: maxYTicks + 1 }).map((_, i) => (
        <Typography
            key={i}
            variant="caption"
            sx={{
                position: 'absolute',
                bottom: `${(i / maxYTicks) * 100}%`,
                left: 0,
                transform: 'translateY(50%)',
                whiteSpace: 'nowrap'
            }}
        >
            {Math.round(((maxYTicks - i) * 100) / maxYTicks)}
        </Typography>
    ));

    return (
        <Box display="flex" flexDirection="row" width="100%" pl={4} mt={2}>
            <Box position="relative" height={height} width={40}>
                {yTicks}
            </Box>
            <Box display="flex" flexDirection="column" width="100%">
                <Box
                    display="flex"
                    alignItems="flex-end"
                    justifyContent="space-around"
                    height={height}
                    borderLeft="1px solid #ccc"
                    borderBottom="1px solid #ccc"
                    px={2}
                >
                    {Array.from({ length: bars }).map((_, i) => {
                        const barHeight = Math.floor(Math.random() * (height - 50)) + 50;
                        return (
                            <Box key={i} display="flex" flexDirection="column" alignItems="center" sx={{ mb: 1 }}>
                                <Skeleton variant="rectangular" width={barWidth} height={barHeight} sx={{ borderRadius: 1 }} />
                            </Box>
                        );
                    })}
                </Box>
                <Box display="flex" justifyContent="space-around" mt={1} px={2}>
                    {Array.from({ length: bars }).map((_, i) => (
                        <Typography key={i} variant="caption" textAlign="center" noWrap sx={{ width: barWidth }}>
                            <Skeleton variant="rectangular" width={70} height={20} sx={{ borderRadius: 1 }} />
                        </Typography>
                    ))}
                </Box>
            </Box>
        </Box>
    );
};

const SelectGroup = ({ groupValue, setGroupValue }) => (
    <FormControl
        sx={{
            minWidth: 160,
            '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                '&:hover': {
                    backgroundColor: 'rgba(255, 255, 255, 1)',
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)'
                },
                '&.Mui-focused': {
                    backgroundColor: 'rgba(255, 255, 255, 1)',
                    boxShadow: '0 2px 12px rgba(102, 126, 234, 0.2)'
                }
            },
            '& .MuiInputLabel-root': {
                color: '#64748b',
                fontWeight: 500,
                fontSize: '0.8rem',
                '&.Mui-focused': {
                    color: '#667eea'
                }
            }
        }}
        size="small"
    >
        <InputLabel>Agrupar por</InputLabel>
        <Select
            value={groupValue}
            onChange={({ target: { value } }) => setGroupValue(value)}
            label="Agrupar por"
            sx={{
                '& .MuiSelect-select': {
                    fontSize: '0.8rem',
                    fontWeight: 500,
                    color: '#374151',
                    py: 1
                }
            }}
        >
            {groupOptions.map(({ label, value }) => (
                <MenuItem
                    key={value}
                    value={value}
                    sx={{
                        fontSize: '0.8rem',
                        fontWeight: 500,
                        py: 0.5,
                        '&:hover': {
                            backgroundColor: 'rgba(102, 126, 234, 0.1)'
                        },
                        '&.Mui-selected': {
                            backgroundColor: 'rgba(102, 126, 234, 0.15)',
                            '&:hover': {
                                backgroundColor: 'rgba(102, 126, 234, 0.2)'
                            }
                        }
                    }}
                >
                    {label}
                </MenuItem>
            ))}
        </Select>
    </FormControl>
);

export default function ApprovalChart({ title, type, variant, filters }) {
    const [groupValue, setGroupValue] = useState(GROUP_VALUE);
    const [isLoading, startLoading, endLoading] = useLoading(true);
    const [chartData, setChartData] = useState({
        options: {
            chart: {},
            xaxis: {},
            yaxis: {}
        },
        series: []
    });
    const [totalData, setTotalData] = useState({
        amount: 0,
        quantity: 0
    });

    const getChartData = () => {
        const { dateRange, selectedGroups, ...otherFilters } = { ...filters };
        const dataToApi = {
            start_date: parseDateToApi(dateRange[0]),
            end_date: parseDateToApi(dateRange[1]),
            group: groupValue,
            groups: selectedGroups ? Object.keys(selectedGroups).map((key) => key) : [],
            type,
            ...otherFilters
        };
        return getChartDataByType(dataToApi);
    };

    useEffect(() => {
        startLoading();
        getChartData()
            .then((resp) => {
                const data = resp.data;
                const columns = data.columns;
                const rows = data.rows;

                setTotalData({
                    amount: data.total,
                    quantity: data.quantity
                });

                const fullLabels = columns;
                const shortLabels = columns;

                setChartData({
                    options: {
                        chart: {
                            id: 'basic-bar',
                            animations: {
                                enabled: true,
                                speed: 800,
                                animateGradually: {
                                    enabled: true,
                                    delay: 150
                                },
                                dynamicAnimation: {
                                    enabled: true,
                                    speed: 350
                                }
                            }
                        },
                        plotOptions: {
                            bar: {
                                distributed: true
                            }
                        },
                        colors: columns.map(() => getRandomColor()),
                        xaxis: {
                            categories: shortLabels
                        },
                        tooltip: {
                            x: {
                                formatter: (val, { dataPointIndex }) => fullLabels[dataPointIndex] || val
                            },
                            y: {
                                formatter: (val) => (variant === AMOUNT_VARIANT ? `${PEN_SYMBOL} ${val.toFixed(2)}` : val)
                            },
                            style: {
                                fontSize: '12px',
                                fontFamily: 'inherit'
                            },
                            custom({ series, seriesIndex, dataPointIndex, w }) {
                                const fullLabel =
                                    columns[dataPointIndex] || w.globals.categoryLabels[dataPointIndex] || w.globals.labels[dataPointIndex];
                                const value = series[seriesIndex][dataPointIndex];
                                const formattedValue = variant === AMOUNT_VARIANT ? `S/ ${value}` : value;

                                return `
                                    <div style="padding: 10px 14px; background: white; border-radius: 8px; box-shadow: 0 4px 16px rgba(0,0,0,0.15); border: 1px solid rgba(0,0,0,0.1);">
                                        <div style="font-weight: 600; color: #374151; margin-bottom: 6px; max-width: 250px; word-wrap: break-word; line-height: 1.3;">
                                            ${fullLabel || 'N/A'}
                                        </div>
                                        <div style="color: #6b7280; font-size: 12px;">
                                            ${
                                                variantMap[variant]
                                            }: <span style="font-weight: 600; color: #111827; font-size: 13px;">${formattedValue}</span>
                                        </div>
                                    </div>
                                `;
                            }
                        },
                        dataLabels: {
                            enabled: true,
                            formatter: (val) => (variant === AMOUNT_VARIANT ? `${PEN_SYMBOL} ${val.toFixed(2)}` : val)
                        }
                    },
                    series: [
                        {
                            name: variantMap[variant],
                            data: columns.map((key) => rows[key][variant])
                        }
                    ]
                });
            })
            .catch((err) => console.error(err))
            .finally(() => endLoading());
    }, [filters, groupValue]);

    return (
        <MainCard
            sx={{
                height: '500px',
                background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                borderRadius: '12px',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.06)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                overflow: 'hidden',
                display: 'flex'
            }}
        >
            <Grid container spacing={0} sx={{ height: '100%', display: 'flex', flexDirection: 'row' }}>
                <Grid item xs={12}>
                    <Box
                        sx={{
                            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                            color: 'white',
                            p: 2,
                            m: -2,
                            mb: 2,
                            borderRadius: '12px 12px 0 0'
                        }}
                    >
                        <Grid container alignItems="center" justifyContent="space-between" spacing={1}>
                            <Grid item xs={12}>
                                <Typography
                                    variant="h6"
                                    sx={{
                                        fontSize: { xs: '0.9rem', sm: '1rem', md: '1.1rem' },
                                        fontWeight: 600,
                                        mb: 0.5,
                                        color: 'white'
                                    }}
                                >
                                    {title}
                                </Typography>
                                <BlockLoader loading={isLoading} loaderComponent={<ValueSkeleton />}>
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            fontSize: { xs: '1.3rem', sm: '1.5rem', md: '1.7rem' },
                                            fontWeight: 700,
                                            color: 'white',
                                            textShadow: '0 2px 4px rgba(0,0,0,0.2)'
                                        }}
                                    >
                                        {variant === AMOUNT_VARIANT ? <DisplayCurrency value={totalData[variant]} /> : totalData[variant]}
                                    </Typography>
                                </BlockLoader>
                            </Grid>
                        </Grid>
                    </Box>

                    <SelectGroup groupValue={groupValue} setGroupValue={setGroupValue} />
                </Grid>
                <Grid item xs={12} sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
                    <BlockLoader loading={isLoading} loaderComponent={<BarChartSkeleton />}>
                        {chartData.options && chartData.series ? (
                            <Chart
                                options={{
                                    ...chartData.options,
                                    chart: {
                                        ...chartData.options.chart,
                                        toolbar: {
                                            show: false
                                        },
                                        background: 'transparent',
                                        height: '100%'
                                    },
                                    plotOptions: {
                                        bar: {
                                            borderRadius: 6,
                                            columnWidth: '65%',
                                            dataLabels: {
                                                position: 'top'
                                            }
                                        }
                                    },
                                    dataLabels: {
                                        enabled: true,
                                        formatter(val) {
                                            return variant === AMOUNT_VARIANT ? `S/ ${val}` : val;
                                        },
                                        offsetY: -15,
                                        style: {
                                            fontSize: '10px',
                                            fontWeight: 600,
                                            colors: ['#304758']
                                        }
                                    },
                                    colors: [variant === AMOUNT_VARIANT ? '#2ecc71' : '#3498db'],
                                    grid: {
                                        borderColor: '#f1f3f4',
                                        strokeDashArray: 2,
                                        xaxis: {
                                            lines: {
                                                show: false
                                            }
                                        },
                                        yaxis: {
                                            lines: {
                                                show: true
                                            }
                                        },
                                        padding: {
                                            top: 0,
                                            right: 10,
                                            bottom: 60,
                                            left: 10
                                        }
                                    },
                                    xaxis: {
                                        ...chartData.options.xaxis,
                                        labels: {
                                            style: {
                                                fontSize: '10px',
                                                fontWeight: 500,
                                                colors: '#64748b'
                                            },
                                            rotate: -45,
                                            rotateAlways: true,
                                            maxHeight: 140,
                                            trim: false,
                                            hideOverlappingLabels: false,
                                            offsetY: 10
                                        },
                                        axisBorder: {
                                            show: false
                                        },
                                        axisTicks: {
                                            show: false
                                        },
                                        tickPlacement: 'on'
                                    },
                                    yaxis: {
                                        ...chartData.options.yaxis,
                                        labels: {
                                            style: {
                                                fontSize: '10px',
                                                fontWeight: 500,
                                                colors: '#64748b'
                                            }
                                        }
                                    },
                                    tooltip: {
                                        ...chartData.options.tooltip
                                    }
                                }}
                                series={chartData.series}
                                type="bar"
                                height={350}
                            />
                        ) : (
                            <Box
                                sx={{
                                    height: '350px',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
                                    borderRadius: '8px',
                                    border: '2px dashed #dee2e6'
                                }}
                            >
                                <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{
                                        fontWeight: 500,
                                        textAlign: 'center'
                                    }}
                                >
                                    No hay datos disponibles
                                </Typography>
                                <Typography
                                    variant="caption"
                                    color="text.secondary"
                                    sx={{
                                        opacity: 0.7,
                                        textAlign: 'center',
                                        mt: 0.5
                                    }}
                                >
                                    Selecciona filtros para ver gráficos
                                </Typography>
                            </Box>
                        )}
                    </BlockLoader>
                </Grid>
            </Grid>
        </MainCard>
    );
}
