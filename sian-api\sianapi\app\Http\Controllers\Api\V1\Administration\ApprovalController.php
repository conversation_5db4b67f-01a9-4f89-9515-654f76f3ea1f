<?php
namespace App\Http\Controllers\Api\V1\Administration;
use App\Models\DataProviders\DpBusinessPartner;
use App\Models\Owner;
use App\Models\Procedures\SpGetApprovedByType;
use App\Models\Procedures\SpGroupedConfirmations;
use Illuminate\Http\Request;
use App\Models\Person;
use App\Http\Controllers\Controller;
use App\Http\Resources\Administration\PersonResource;
use App\Http\Resources\Administration\SimplePersonResource;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use function GuzzleHttp\json_encode;
class ApprovalController extends Controller {

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function getGroupsByType(Request $request) {
        try {
            $validate = Validator::make($request->all(), [
                "user_id" => "required|integer",
                "type" => "string",
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $type = $request->input('type', 'selected');
            $userId = $request->input('user_id');

            $opColumn = $type === 'default' ? 'OP.default' : 'OP.selected';

            $query = DB::table('user_group as UG')
                ->join('owner_pair as UGOP', function ($join) {
                    $join->on('UG.user_group_id', '=', 'UGOP.owner_id')
                        ->where('UGOP.owner', '=', 'UserGroup');
                })
                ->join('owner_pair as OP', function ($join) {
                    $join->on('UGOP.pair_id', '=', 'OP.parent_id')
                        ->where('OP.owner', '=', 'User');
                })
                ->join('user as SU', 'SU.user_id', '=', 'OP.owner_id')
                ->where(function ($query) use ($userId, $opColumn) {
                    $query->where(DB::raw("(SELECT `level` FROM `user` WHERE user_id = $userId)"), '=', 4)
                        ->orWhere(function ($sub) use ($userId, $opColumn) {
                            $sub->where('SU.user_id', '=', $userId)
                                ->whereRaw("$opColumn = 1");
                        });
                })
                ->groupBy(
                    'UG.user_group_id',
                    'UG.user_group_name',
                    'UG.users_count',
                    'UG.admin_count',
                    'UG.status'
                )
                ->select('UG.*');


            return response()->json([
                'success' => true,
                'data' => $query->get()
            ]);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getAproovedByType(Request $request) {
        try {
            $validate = Validator::make($request->all(), [
                "user_id" => "required|integer",
                "start_date" => "required|date",
                "end_date" => "date|after_or_equal:start_date|required_with:start_date",
                "type" => "in:selected,default",
            ]);
            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_user_id = $request->input('user_id');
            $s_start_date = $request->input('start_date');
            $s_end_date = $request->input('end_date');
            $type = $request->input('type', 'selected');
            $groups = $request->input('groups', '');

            $data = SpGetApprovedByType::execute($s_start_date, $s_end_date, $s_user_id, $groups, $type);

            return response()->json([
                'success' => true,
                'data' => $data
            ]);
        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function getChartData(Request $request) {
        try {
            $validate = Validator::make($request->all(), [
                "user_id" => "required|integer",
                "start_date" => "required|date",
                "end_date" => "date|after_or_equal:start_date|required_with:start_date",
                "type" => "in:selected,default",
            ]);

            if ($validate->fails()) {
                return response()->json([
                    'success' => false,
                    'error' => $validate->errors()
                ], 400);
            }

            $s_user_id = $request->input('user_id');
            $s_type = $request->input('type', 'selected');
            $s_start_date = $request->input('start_date');
            $s_end_date = $request->input('end_date');
            $s_group = $request->input('group');
            $groups = $request->input('groups', '');

            $f_total = 0;
            $i_quantity = 0;
            $s_name_key = '';

            switch ($s_group) {
                case 'group':
                    $s_name_key = 'user_group_name';
                    break;
                case 'approver':
                    $s_name_key = 'user_name';
                    break;
                case 'request':
                    $s_name_key = 'user_request';
                    break;
                case 'project':
                    $s_name_key = 'project_name';
                    break;
                default:
                    $s_name_key = '';
                    break;
            }

            $groupsString = '';
            if (!empty($groups) && is_array($groups)) {
                $groupsString = implode(',', $groups);
            }

            $groupedData = SpGroupedConfirmations::execute($s_start_date, $s_end_date, $s_user_id, $groupsString, $s_type, $s_group);

            $column_names = [];
            $rows = [];
            foreach ($groupedData as $row) {
                $totalPen = floatval($row->total_pen ?? 0);
                $quantity = floatval($row->quantity ?? 0);

                $f_total += $totalPen;
                $i_quantity += $quantity;

                $keyValue = $row->{$s_name_key} ?? 'NO PROYECTO';

                if (!isset($rows[$keyValue])) {
                    $rows[$keyValue] = [
                        'amount' => 0,
                        'quantity' => 0
                    ];
                }

                $rows[$keyValue]['amount'] += $totalPen;
                $rows[$keyValue]['quantity'] += $quantity;

                if (!in_array($keyValue, $column_names)) {
                    $column_names[] = $keyValue;
                }
            }

            $response_data = [
                'rows' => $rows,
                'columns' => $column_names,
                'total' => $f_total,
                'quantity' => $i_quantity,
                'key' => $s_name_key,
            ];

            return response()->json([
                'success' => true,
                'data' => $response_data
            ]);

        } catch (\Exception $ex) {
            return response()->json([
                'success' => false,
                'error' => 'query error: ' . $ex->getMessage(),
            ], 500);
        }
    }

}