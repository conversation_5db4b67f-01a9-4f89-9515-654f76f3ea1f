<?php

namespace App\Models\Procedures;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SpGetApprovedByType {

    public static function execute($startDate = '', $endDate = '', $user_id, $group, $type = 'selected') {
        $sql = "CALL sp_get_approved_by_type(?, ?, ?, ?, ?)";
        $parameters = [
            $startDate,
            $endDate,
            $user_id,
            $group,
            $type
        ];
        return DB::select($sql, $parameters);
    }

}

