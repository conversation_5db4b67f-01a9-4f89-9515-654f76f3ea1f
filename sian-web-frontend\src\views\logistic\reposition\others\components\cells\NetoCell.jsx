import React from 'react';
import { Tooltip, Typography } from '@mui/material';

/**
 * Componente para mostrar valores NETO sin formateo de unidades
 * Muestra el valor tal como viene de la API sin transformación
 */
const NetoCell = ({ value, tableMeta }) => {
    // Mostrar el valor tal como viene, sin validaciones complejas
    const displayValue = value !== null && value !== undefined ? parseFloat(value).toFixed(2) : '0.00';

    return (
        <Typography sx={{ fontSize: '0.875rem', color: value > 0 ? 'green' : 'red' }}>
            {displayValue}
        </Typography>
    );
};

export default NetoCell;
