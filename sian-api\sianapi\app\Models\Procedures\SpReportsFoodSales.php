<?php

namespace App\Models\Procedures;

use App\Models\Division;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;


class SpReportsFoodSales
{
    const MODE_SUM = 0;
    const MODE_UNIT = 1;

    public static function execute(
        $startDate = '',
        $endDate = '',
        $limit = 0,
        $product_type = Division::DIVISION_FOOD_NAME,
        $s_divisionIds = "",
        $s_lineIds = "",
        $s_sublineIds = "",
        $s_categoryIds = "",
        $s_productIds = "",
        $s_storeIds = "",
        $grouping = "",
        $order_by,
        $only_terrace = 0
    ) {
        $sql = "CALL sp_reports_sales_by_type(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $parameters = [
            $startDate,
            $endDate,
            $product_type,
            $s_divisionIds,
            $s_lineIds,
            $s_sublineIds,
            $s_categoryIds,
            $s_productIds,
            $s_storeIds,
            $order_by,
            $limit,
            $grouping,
            $only_terrace
        ];
        return DB::select($sql, $parameters);
    }
}
