import React from 'react';
import { Typography } from '@mui/material';

/**
 * Componente para mostrar la presentación formateada de un producto
 */
const PresentationCell = ({ value, tableMeta }) => {
    // Obtener datos necesarios de las columnas ocultas
    const rowData = tableMeta.rowData;
    const presentations = rowData[tableMeta.columnIndex - tableMeta.columnIndex + 2]; // presentations está en índice 2
    const equivalenceDefault = rowData[tableMeta.columnIndex - tableMeta.columnIndex + 3]; // equivalence_default está en índice 3

    // Validar que tenemos los datos necesarios
    const parsedEquivalenceDefault = parseFloat(equivalenceDefault);
    
    if (presentations && parsedEquivalenceDefault) {
        // Buscar la presentación correspondiente a la equivalencia por defecto
        const equivalenceKey = parsedEquivalenceDefault.toFixed(2);
        let presentationData = presentations[equivalenceKey];
        
        // Si no se encuentra, buscar por valor numérico
        if (!presentationData) {
            presentationData = Object.values(presentations).find(p => 
                parseFloat(p.equivalence) === parsedEquivalenceDefault
            );
        }
        
        // Si aún no se encuentra, usar equivalencia 1 como fallback
        if (!presentationData) {
            presentationData = presentations["1.00"] || Object.values(presentations)[0];
        }
        
        if (presentationData?.measure_name) {
            // Formatear la presentación
            if (parsedEquivalenceDefault === 1) {
                return <Typography>{presentationData.measure_name}</Typography>;
            }
            // Extraer solo la parte de la unidad (antes del asterisco si existe)
            const unitPart = presentationData.measure_name.split('*')[0].trim();
            return <Typography>{`${unitPart} de ${parsedEquivalenceDefault.toFixed(0)} ${unitPart}`}</Typography>;
        }
    }

    // Fallback: mostrar valor original
    return <Typography>{value || 'N/A'}</Typography>;
};

export default PresentationCell;
