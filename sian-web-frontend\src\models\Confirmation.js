export const STATE_PENDIENT = 'Pendiente';
export const STATE_APPROVAL = 'Aceptado';
export const STATE_REJECTED = 'Rechazado';
export const STATE_CANCELED = 'Cancelado';
export const TYPE_EDIT = 'edit';
export const TYPE_PAY = 'pay';
export const TYPE_APPLY = 'apply';
export const TYPE_DISPATCH = 'dispatch';
export const TYPE_UPGRADE = 'upgrade';
export const TYPE_NULL = 'null';
export const TYPE_REMOVE = 'remove';
export const SEPARATOR = '|';

export const DIRECTLY_FIELD = 'directly';
export const REQUEST_FIELD = 'request';
export const VIEW_NORMAL = 'normal';
export const VIEW_CONFIRM = 'confirmation';

export const listTypeRequestData = {
    [TYPE_EDIT]: 'Edición',
    [TYPE_PAY]: 'Pago/Cobro',
    [TYPE_APPLY]: 'Aplicación',
    [TYPE_UPGRADE]: 'Orden/Factura',
    [TYPE_DISPATCH]: 'Despacho/Ingreso',
    [TYPE_NULL]: 'Anulación',
    [TYPE_REMOVE]: 'Eliminación'
};

export const listStateData = {
    [STATE_PENDIENT]: STATE_PENDIENT,
    [STATE_APPROVAL]: STATE_APPROVAL,
    [STATE_REJECTED]: STATE_REJECTED,
    [STATE_CANCELED]: STATE_CANCELED
};

export const colorStateData = {
    [TYPE_EDIT]: 'primary',
    [TYPE_NULL]: 'danger',
    [TYPE_REMOVE]: 'danger',
    [TYPE_DISPATCH]: 'warning',
    [TYPE_PAY]: 'info',
    [TYPE_APPLY]: 'info',
    [TYPE_UPGRADE]: 'default'
};

export const confirmationMap = {
    [TYPE_EDIT]: {
        [DIRECTLY_FIELD]: 'edit_directly',
        [REQUEST_FIELD]: 'request_edit'
    },

    [TYPE_DISPATCH]: {
        [DIRECTLY_FIELD]: 'dispatch_directly',
        [REQUEST_FIELD]: 'request_dispatch'
    },

    [TYPE_PAY]: {
        [DIRECTLY_FIELD]: 'pay_directly',
        [REQUEST_FIELD]: 'request_pay'
    },
    [TYPE_APPLY]: {
        [DIRECTLY_FIELD]: 'apply_directly',
        [REQUEST_FIELD]: 'request_apply'
    },
    [TYPE_UPGRADE]: {
        [DIRECTLY_FIELD]: 'upgrade_directly',
        [REQUEST_FIELD]: 'request_upgrade'
    },
    [TYPE_NULL]: {
        [DIRECTLY_FIELD]: 'null_directly',
        [REQUEST_FIELD]: 'request_null'
    },
    [TYPE_REMOVE]: {
        [DIRECTLY_FIELD]: 'remove_directly',
        [REQUEST_FIELD]: 'request_remove'
    }
};

export const SUPERVISOR_GROUPS_LABEL = 'Supervisados';
export const ADMINISTRATOR_GROUPS_LABEL = 'Administrados';

export const SUPERVISOR_LABEL = 'Supervisor';
export const ADMINISTRATOR_LABEL = 'Administrador';

export const AMOUNT_VARIANT = 'amount';
export const QUANTITY_VARIANT = 'quantity';

export const AMOUNT_LABEL = 'Monto';
export const APPROVALS_LABEL = 'Aprobaciones';

export const variantMap = {
    [AMOUNT_VARIANT]: AMOUNT_LABEL,
    [QUANTITY_VARIANT]: APPROVALS_LABEL
};

export const ADMINISTRATOR_TYPE = 'default';
export const SUPERVISOR_TYPE = 'selected';

export const GROUP_LABEL = 'Grupo';
export const REQUEST_LABEL = 'Solicitante';
export const APPROVER_LABEL = 'Responsable';
export const PROJECT_LABEL = 'Proyecto';

export const GROUP_VALUE = 'group';
export const REQUEST_VALUE = 'request';
export const APPROVER_VALUE = 'approver';
export const PROJECT_VALUE = 'project';

export const groupOptions = [
    { label: GROUP_LABEL, value: GROUP_VALUE },
    { label: REQUEST_LABEL, value: REQUEST_VALUE },
    { label: APPROVER_LABEL, value: APPROVER_VALUE },
    { label: PROJECT_LABEL, value: PROJECT_VALUE }
];
