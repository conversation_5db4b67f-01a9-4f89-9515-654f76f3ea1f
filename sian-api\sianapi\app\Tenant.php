<?php

namespace App;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Eloquent\Model;

class Tenant extends Model
{
    /**
     * The connection name for the model.
     *
     * @var string|null
     */
    protected $connection = 'sian';

    protected $guarded = [];

    /**
     *
     */
    public function configure()
    {
        config([
            'database.connections.tenant.database' => $this->database,
        ]);

        DB::purge('tenant');

        DB::reconnect('tenant');

        Schema::connection('tenant')->getConnection()->reconnect();

        return $this;
    }

    /**
     *
     */
    public function use()
    {
        app()->forgetInstance('tenant');

        app()->instance('tenant', $this);

        return $this;
    }

    public static function getDomain($request) {
        $host = '';
        $originHeader = $request->headers->get('Origin');

        if ($originHeader) {
            $parsedUrl = parse_url($originHeader);
            $host = $parsedUrl['host'] ?? '';
        }

        $tenant = Tenant::whereDomain($host)->first();

        if (!$tenant) {
            throw new \Exception("Tenant no encontrado para el dominio: $host");
        }

        return 'https://' . $tenant['sian_domain'];
    }

}