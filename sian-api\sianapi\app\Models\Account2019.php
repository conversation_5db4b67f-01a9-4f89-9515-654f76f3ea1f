<?php

/**
 * Created by Reliese Model.
 */

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Class Account2019
 * 
 * @property int $account_id
 * @property string $account_code
 * @property string $account_name
 * @property bool $system
 * @property bool $level
 * @property bool $element_code
 * @property string|null $account_parent
 * @property string|null $counterpart
 * @property string|null $reverse_account
 * @property string|null $owner
 * @property int|null $owner_id
 * @property bool $status
 * @property bool $is_wildcard
 * @property bool $is_item_wildcard
 * @property bool $is_usable
 * @property bool $has_destinies
 * @property string|null $destiny_type
 * @property bool $has_combination
 * @property bool $has_children
 *
 * @package App\Models
 */
class Account2019 extends Model
{
	protected $table = 'account_2019';
	public $incrementing = false;
	public $timestamps = false;

	protected $casts = [
		'account_id' => 'int',
		'system' => 'bool',
		'level' => 'bool',
		'element_code' => 'bool',
		'owner_id' => 'int',
		'status' => 'bool',
		'is_wildcard' => 'bool',
		'is_item_wildcard' => 'bool',
		'is_usable' => 'bool',
		'has_destinies' => 'bool',
		'has_combination' => 'bool',
		'has_children' => 'bool'
	];

	protected $fillable = [
		'account_id',
		'account_code',
		'account_name',
		'system',
		'level',
		'element_code',
		'account_parent',
		'counterpart',
		'reverse_account',
		'owner',
		'owner_id',
		'status',
		'is_wildcard',
		'is_item_wildcard',
		'is_usable',
		'has_destinies',
		'destiny_type',
		'has_combination',
		'has_children'
	];
}
