import Routes from 'routes';
import NavigationScroll from 'layout/NavigationScroll';
import ThemeCustomization from 'themes';
import './styles.css';


import { JWTProvider as AuthProvider } from 'contexts/JWTContext';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { es } from 'date-fns/locale';

// ==============================|| APP ||============================== //
const App = () => (
    <ThemeCustomization>
        <NavigationScroll>
            <AuthProvider>
                <LocalizationProvider dateAdapter={AdapterDateFns} adapterLocale={es}>
                    <Routes />
                </LocalizationProvider>
            </AuthProvider>
        </NavigationScroll>
    </ThemeCustomization>
);

export default App;
