import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Box, Button, MenuItem, TextField } from '@mui/material';
import DateRangeSelector from 'ui-component/extended/DateRangeSelector';
import SearchIcon from '@mui/icons-material/Search';
import { parseDataToSearchString } from 'utils/dates';
import { documentOptionsWithAcc, documentOptionsWithoutAcc } from 'data/documentTypes';
import Autocomplete from 'ui-component/inputs/Autocomplete';
import { useDispatch, useSelector } from 'store';
import { clearSlice as clearRequestPaymentPerson, getRequestPaymentPersons } from 'store/slices/person/requestPaymentPerson';
import { clearSlice as clearBussinessPartner, getBussinessPartners } from 'store/slices/person/bussinessPartner';
import { clearSlice as clearBusinessUnit, getBusinessUnits } from 'store/slices/businessUnit/businessUnit';
import { clearSlice as clearProject, getProjects } from 'store/slices/project/project';
import ToggleButton from 'ui-component/inputs/ToggleButton';
import useLocalStorage from 'hooks/useLocalStorage';
import { getServerHour } from 'services/serverService';

const optionsWithAcc = documentOptionsWithAcc.map((item) => (
    <MenuItem sx={{ textTransform: 'uppercase' }} value={item.value} key={`DOCUMENT_TYPE_${item.value}`}>
        {item.value} - {item.label}
    </MenuItem>
));

const optionsWithoutAcc = documentOptionsWithoutAcc.map((item) => (
    <MenuItem sx={{ textTransform: 'uppercase' }} value={item.value} key={`DOCUMENT_TYPE_${item.value}`}>
        {item.value} - {item.label}
    </MenuItem>
));

export default function DocumentFilter({ setFilters, handleSearch, mainFilters }) {
    const dispatch = useDispatch();
    const { data: requestPersonData, loading: requestPersonLoading } = useSelector((state) => state.requestPerson);
    const { data: bussinessPartnerData, loading: bussinessPartnerLoading } = useSelector((state) => state.bussinessPartner);
    const { data: businessUnitData, loading: businessUnitLoading } = useSelector((state) => state.businessUnit);
    const { data: projectData, loading: projectLoading } = useSelector((state) => state.project);
    const [, setStartRegisterDate] = useLocalStorage('start_register_date');
    const [firstSearch, setFirstSearch] = useState(false);

    const submitSearch = useCallback(() => {
        if (!firstSearch) {
            getServerHour().then(({ success, ...restData }) => success && setStartRegisterDate(restData.data.date));
        }
        setFirstSearch(true);
        handleSearch();
    }, [firstSearch, setStartRegisterDate, handleSearch]);

    const [dataFilters, setDataFilters] = useState({
        dateRangeEmision: null,
        dateRangeExpiration: null,
        ruc: '',
        documentOrigin: 'EG',
        documentType: [],
        documentNumber: '',
        searchType: false,
        showDetraction: '',
        businessPartner: [],
        requestPaymentPersons: [],
        businessUnit: [],
        project: []
    });

    const [documentNumberInput, setDocumentNumberInput] = useState('');
    const debounceTimeout = useRef(null);

    const setValue = useCallback((name, value) => {
        if (name === 'documentOrigin') {
            setDataFilters((currentState) => ({ ...currentState, documentType: [] }));
            if (value === 'M') {
                setDataFilters((currentState) => ({ ...currentState, showDetraction: '' }));
            }
        }
        setDataFilters((currentState) => ({ ...currentState, [name]: value }));
    }, []);

    useEffect(() => {
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        debounceTimeout.current = setTimeout(() => {
            setValue('documentNumber', documentNumberInput);
        }, 300);

        return () => {
            if (debounceTimeout.current) {
                clearTimeout(debounceTimeout.current);
            }
        };
    }, [documentNumberInput, setValue]);

    const loadRequestPerson = useCallback((keyword) => {
        dispatch(clearRequestPaymentPerson());
        const filters = {
            ...(keyword && keyword !== '' && { keyword }),
            ...(dataFilters.requestPaymentPersons.length >= 1 && {
                requestPaymentPersons: [...dataFilters.requestPaymentPersons]
            })
        };
        dispatch(getRequestPaymentPersons(filters));
    }, [dispatch, dataFilters.requestPaymentPersons]);

    const loadBusinessPartner = useCallback((keyword) => {
        dispatch(clearBussinessPartner());
        const filters = {
            ...(keyword && keyword !== '' && { keyword: keyword.toString() }),
            ...(dataFilters.businessPartner.length >= 1 && {
                businessPartner: [...dataFilters.businessPartner]
            }),
            limit: -1
        };
        dispatch(getBussinessPartners(filters));
    }, [dispatch, dataFilters.businessPartner]);

    const loadBusinessUnit = useCallback((keyword) => {
        dispatch(clearBusinessUnit());
        const filters = {
            ...(keyword && keyword !== '' && { keyword }),
            ...(dataFilters.businessUnit.length >= 1 && {
                businessUnit: [...dataFilters.businessUnit]
            })
        };
        dispatch(getBusinessUnits(filters));
    }, [dispatch, dataFilters.businessUnit]);

    const loadProject = useCallback((keyword) => {
        dispatch(clearProject());
        const filters = {
            ...(keyword && keyword !== '' && { keyword }),
            ...(dataFilters.project.length >= 1 && {
                project: [...dataFilters.project]
            })
        };
        dispatch(getProjects(filters));
    }, [dispatch, dataFilters.project]);

    useEffect(() => {
        submitSearch();
    }, [mainFilters.documentOrigin]);

    useEffect(() => {
        // valida y modifica los valores de datafilters
        let filters = {};

        if (dataFilters.dateRangeEmision) {
            filters = {
                ...filters,
                startDateEmision: parseDataToSearchString(dataFilters.dateRangeEmision.startDate),
                endDateEmision: parseDataToSearchString(dataFilters.dateRangeEmision.endDate)
            };
        }

        if (dataFilters.dateRangeExpiration) {
            filters = {
                ...filters,
                startDateExpiration: parseDataToSearchString(dataFilters.dateRangeExpiration.startDate),
                endDateExpiration: parseDataToSearchString(dataFilters.dateRangeExpiration.endDate)
            };
        }

        if (dataFilters.ruc && dataFilters.ruc.trim() !== '') {
            filters = {
                ...filters,
                ruc: Number(dataFilters.ruc)
            };
        }

        if (dataFilters.documentType.length >= 1) {
            filters = {
                ...filters,
                documentType: dataFilters.documentType.filter((elemento) => elemento !== '')
            };
        }

        if (dataFilters.documentNumber && dataFilters.documentNumber.trim() !== '') {
            filters = {
                ...filters,
                documentNumber: dataFilters.documentNumber
            };
        }

        if (dataFilters.documentOrigin && dataFilters.documentOrigin.trim() !== '') {
            filters = {
                ...filters,
                documentOrigin: dataFilters.documentOrigin
            };
        } else {
            filters = {
                ...filters,
                documentOrigin: 'EG'
            };
        }

        if (dataFilters.businessPartner.length >= 1) {
            filters = {
                ...filters,
                businessPartner: dataFilters.businessPartner.filter((elemento) => elemento !== '')
            };
        }

        if (dataFilters.requestPaymentPersons.length >= 1) {
            filters = {
                ...filters,
                requestPaymentPersons: dataFilters.requestPaymentPersons.filter((elemento) => elemento !== '')
            };
        }

        if (dataFilters.businessUnit.length >= 1) {
            // validaciones
            filters = {
                ...filters,
                businessUnit: dataFilters.businessUnit.filter((elemento) => elemento !== '')
            };
        }

        if (dataFilters.project.length >= 1) {
            // validaciones
            filters = {
                ...filters,
                project: dataFilters.project.filter((elemento) => elemento !== '')
            };
        }

        if (dataFilters.showDetraction !== '') {
            filters = {
                ...filters,
                showDetraction: dataFilters.showDetraction
            };
        }

        setFilters(filters);
    }, [dataFilters]);

    const handleSubmit = useCallback((event) => {
        event.preventDefault();
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        setValue('documentNumber', documentNumberInput);
        setTimeout(() => {
            submitSearch();
        }, 50);
    }, [documentNumberInput, setValue, submitSearch]);

    const handleDocumentNumberChange = useCallback(({ target: { value } }) => {
        setDocumentNumberInput(value);
    }, []);

    return (
        <form onSubmit={handleSubmit}>
        <Box sx={{ width: '100%' }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', flexDirection: { xs: 'column', sm: 'column', md: 'row' }, width: '100%', gap: 1 }}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5, width: '100%' }}>
                    <Box sx={{ display: 'flex', gap: 0.5, flexDirection: { xs: 'column', lg: 'row' }, width: '100%', alignItems: 'end' }}>
                        <Box sx={{ display: 'flex', gap: 0.5, flexDirection: 'row', width: { xs: '100%', lg: '20%' }, alignItems: 'end' }}>
                            <ToggleButton
                                label="TIPO DE BÚSQUEDA"
                                value={dataFilters.documentOrigin}
                                setValue={(value) => { setValue('documentOrigin', value) }}
                                items={[
                                    { label: 'COMPROBANTE', value: 'EG' },
                                    { label: 'ORDEN', value: 'M' }
                                ]}
                                fullWidth
                            />
                        </Box>
                        <Box sx={{ display: 'flex', gap: 0.5, flexDirection: { xs: 'column', sm: 'row' }, width: { xs: '100%', lg: '40%' }, alignItems: 'end' }}>
                            <Autocomplete
                                label="UNIDAD DE NEGOCIO"
                                name="businessUnit"
                                onChange={(value) => setValue('businessUnit', value)}
                                options={businessUnitData}
                                loading={businessUnitLoading}
                                onLoad={loadBusinessUnit}
                                option_id="business_unit_id"
                                optionLabel="business_unit_name"
                            />
                            <Autocomplete
                                label="PROYECTO"
                                name="project"
                                onChange={(value) => setValue('project', value)}
                                options={projectData}
                                loading={projectLoading}
                                onLoad={loadProject}
                                option_id="project_id"
                                optionLabel="short_name"
                            />
                        </Box>
                        <Box sx={{ display: 'flex', gap: 0.5, flexDirection: { xs: 'column', sm: 'row' }, width: { xs: '100%', lg: '40%' }, alignItems: 'end' }}>
                            <TextField
                                className="select-field"
                                label="TIPO DE DOCUMENTO"
                                name="documentType"
                                onChange={({ target: { name, value } }) => setValue(name, value)}
                                value={dataFilters.documentType}
                                SelectProps={{
                                    multiple: true,
                                    renderValue: (selected) =>
                                        selected
                                            .map((value) =>
                                                dataFilters.documentOrigin === 'EG'
                                                    ? documentOptionsWithAcc.find((item) => item.value === value)?.value || value
                                                    : documentOptionsWithoutAcc.find((item) => item.value === value)?.value || value
                                            )
                                            .join(', ')
                                }}
                                select
                                fullWidth
                            >
                                {dataFilters.documentOrigin === 'EG' ? optionsWithAcc.map((jsx) => jsx) : optionsWithoutAcc.map((jsx) => jsx)}
                            </TextField>
                            <TextField
                                label="N° DE DOCUMENTO"
                                type="text"
                                name="documentNumber"
                                value={documentNumberInput}
                                onChange={handleDocumentNumberChange}
                                fullWidth
                            />
                        </Box>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 0.5, flexDirection: { xs: 'column', lg: 'row' }, width: '100%', alignItems: 'end' }}>
                        <Box sx={{ display: 'flex', gap: 0.5, flexDirection: 'row', width: { xs: '100%', lg: '20%' } }}>
                            <ToggleButton
                                label="DETRACCIONES"
                                value={dataFilters.showDetraction}
                                setValue={(value) => setValue('showDetraction', value)}
                                items={[
                                    { label: 'SI', value: true },
                                    { label: 'NO', value: false }
                                ]}
                                disabled={dataFilters.documentOrigin !== 'EG'}
                                fullWidth
                                allItem
                            />
                        </Box>
                        <Box sx={{ display: 'flex', gap: 0.5, flexDirection: { xs: 'column', sm: 'row' }, width: { xs: '100%', lg: '40%' }, alignItems: 'end' }}>
                            <Autocomplete
                                label="SOCIO DE NEGOCIO"
                                name="businessPartner"
                                onChange={(value) => setValue('businessPartner', value)}
                                options={bussinessPartnerData}
                                loading={bussinessPartnerLoading}
                                onLoad={loadBusinessPartner}
                                option_id="person_id"
                                optionLabel="full_name"
                            />
                            <Autocomplete
                                label="SOLICITANTE"
                                name="requestPaymentPersons"
                                onChange={(value) => setValue('requestPaymentPersons', value)}
                                options={requestPersonData}
                                loading={requestPersonLoading}
                                onLoad={loadRequestPerson}
                                option_id="person_id"
                                optionLabel="person_name"
                            />
                        </Box>
                        <Box sx={{ display: 'flex', gap: 0.5, flexDirection: { xs: 'column', sm: 'row' }, width: { xs: '100%', lg: '40%' }, alignItems: 'end' }}>
                            <Box sx={{ width: { xs: '100%', lg: '50%' }, gap: 0.5 }}>
                                <DateRangeSelector
                                    dateRange={dataFilters.dateRangeEmision}
                                    label="FECHA DE EMISIÓN"
                                    setDateRange={setValue}
                                    name="dateRangeEmision"
                                />
                            </Box>
                            <Box sx={{ width: { xs: '100%', lg: '50%' }, gap: 0.5 }}>
                                <DateRangeSelector
                                    dateRange={dataFilters.dateRangeExpiration}
                                    label="FECHA DE VENCIMIENTO"
                                    setDateRange={setValue}
                                    name="dateRangeExpiration"
                                />
                            </Box>
                        </Box>
                    </Box>
                </Box>
                <Button type="submit" variant="contained" color="success">
                    <SearchIcon />
                </Button>
            </Box >
        </Box >
        </form>
    );
}
