import React from 'react';
import { Typography } from '@mui/material';
import AnalysisFormattedStockCell from '../components/cells/AnalysisFormattedStockCell';
import PresentationCell from '../components/cells/PresentationCell';
import NetoCell from '../components/cells/NetoCell';
import { DisplayUnitValueCell } from '../RawMaterialRotationDetail';

/**
 * Definiciones de columnas para análisis de productos derivados
 */
export const analisysDerivedProducts = [
    {
        name: 'pk',
        label: 'PK',
        options: {
            filter: false,
            sort: false,
            display: false
        }
    },
    {
        name: 'product_id',
        label: 'PRODUCT_ID',
        options: {
            filter: false,
            sort: false,
            display: false
        }
    },
    {
        name: 'presentations',
        label: 'PRESENTATIONS',
        options: {
            filter: false,
            sort: false,
            display: false
        }
    },
    {
        name: 'equivalence_default',
        label: 'EQUIVALENCE_DEFAULT',
        options: {
            filter: false,
            sort: false,
            display: false
        }
    },
    {
        name: 'store_name',
        label: 'TIENDA',
        options: {
            filter: true,
            sort: true,
            customBodyRender: (value) => {
                const cleanName = value
                    ? value
                          .replace(/^Tienda\s*/i, '')
                          .replace(/\s*-\s*/g, ' ')
                          .trim()
                    : value;
                return <Typography sx={{ fontWeight: 'bold' }}>{cleanName}</Typography>;
            }
        }
    },
    {
        name: 'purchase_stock',
        label: 'STOCK',
        options: {
            filter: true,
            sort: true,
            display: true,

            customBodyRender: (value, tableMeta) => <AnalysisFormattedStockCell value={value} tableMeta={tableMeta} />
        }
    },
    {
        name: 'purchase_stock',
        label: 'STOCK UNITARIO',
        options: {
            filter: true,
            sort: true,
            display: true,
            customBodyRender: (value, tableMeta) => {
                const rowData = tableMeta.rowData;
                const productId = rowData[1];
                return <DisplayUnitValueCell value={value} tableMeta={tableMeta} productId={productId} />;
            }
        }
    }
];

/**
 * Columnas simplificadas para productos derivados
 */
export const simplifiedDerivedProductColumns = [
    {
        name: 'product_id',
        label: 'ID',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
            setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
        }
    },
    {
        name: 'product_name',
        label: 'PRODUCTO',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '200px', maxWidth: '200px' } }),
            setCellProps: () => ({ style: { width: '200px', maxWidth: '200px' } })
        }
    },
    {
        name: 'unit_quantity_proyected',
        label: 'C.PROYECTADA',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '120px', maxWidth: '120px' } }),
            setCellProps: () => ({ style: { width: '120px', maxWidth: '120px' } })
        }
    },
    {
        name: 'purchase_stock',
        label: 'STOCK EN TIENDAS',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '140px', maxWidth: '140px' } }),
            setCellProps: () => ({ style: { width: '140px', maxWidth: '140px' } })
        }
    },
    {
        name: 'suma_netos_analisis',
        label: 'SUMA REPONER',
        options: {
            filter: true,
            sort: true,
            display: false,
            customBodyRender: (value, tableMeta) => <AnalysisFormattedStockCell value={value} tableMeta={tableMeta} />
        }
    },
    {
        name: 'stock',
        label: 'STOCK A PRINCIPAL',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '140px', maxWidth: '140px' } }),
            setCellProps: () => ({ style: { width: '140px', maxWidth: '140px' } })
        }
    },
    {
        name: 'to_transform',
        label: 'TRANSFORMAR',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '160px', maxWidth: '160px' } }),
            setCellProps: () => ({ style: { width: '160px', maxWidth: '160px' } })
        }
    },
    {
        name: 'presentation',
        label: 'PRESENTACIÓN',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '140px', maxWidth: '140px' } }),
            setCellProps: () => ({ style: { width: '140px', maxWidth: '140px' } }),
            customBodyRender: (value, tableMeta) => <PresentationCell value={value} tableMeta={tableMeta} />
        }
    },
    {
        name: 'neto',
        label: 'REPONER',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '100px', maxWidth: '100px' } }),
            setCellProps: () => ({ style: { width: '100px', maxWidth: '100px' } })
        }
    }
];

/**
 * Columnas simplificadas para productos no derivados
 */
export const simplifiedNonDerivedProductColumns = [
    {
        name: 'product_id',
        label: 'ID',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '80px', maxWidth: '80px' } }),
            setCellProps: () => ({ style: { width: '80px', maxWidth: '80px' } })
        }
    },
    {
        name: 'product_name',
        label: 'PRODUCTO',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '200px', maxWidth: '200px' } }),
            setCellProps: () => ({ style: { width: '200px', maxWidth: '200px' } })
        }
    },
    {
        name: 'unit_quantity_proyected',
        label: 'C.PROYECTADA',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '120px', maxWidth: '120px' } }),
            setCellProps: () => ({ style: { width: '120px', maxWidth: '120px' } })
        }
    },
    {
        name: 'purchase_stock',
        label: 'STOCK',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '100px', maxWidth: '100px' } }),
            setCellProps: () => ({ style: { width: '100px', maxWidth: '100px' } })
        }
    },
    {
        name: 'to_transform',
        label: 'TRANSFORMAR',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '120px', maxWidth: '120px' } }),
            setCellProps: () => ({ style: { width: '120px', maxWidth: '120px' } })
        }
    },
    {
        name: 'presentation',
        label: 'PRESENTACIÓN',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '140px', maxWidth: '140px' } }),
            setCellProps: () => ({ style: { width: '140px', maxWidth: '140px' } }),
            customBodyRender: (value, tableMeta) => <PresentationCell value={value} tableMeta={tableMeta} />
        }
    },
    {
        name: 'neto',
        label: 'REPONER',
        options: {
            filter: true,
            sort: true,
            setCellHeaderProps: () => ({ style: { width: '100px', maxWidth: '100px' } }),
            setCellProps: () => ({ style: { width: '100px', maxWidth: '100px' } }),
            customBodyRender: (value, tableMeta) => <NetoCell value={value} tableMeta={tableMeta} />
        }
    }
];
