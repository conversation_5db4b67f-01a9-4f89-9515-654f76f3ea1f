import { Box, TextField, Button, Tooltip } from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers';
import React, { useEffect, useState } from 'react';
import { getTodayDate, getYesterdayDate, isSameMonth } from 'utils/dates';
import { styled } from '@mui/material/styles';

const SubmitButton = styled(Button)({
    backgroundColor: '#9e187e',
    color: 'white',
    borderRadius: '12px',
    padding: '6px 20px',
    textTransform: 'none',
    fontWeight: 'bold',
    boxShadow: '0 2px 4px rgba(179, 37, 110, 0.3)',
    minWidth: '140px',
    height: '36px',
    '&:hover': {
        backgroundColor: '#9e187e'
    }
});

export default function DateRangeInput({
    mainDateRange,
    setMainDateRange,
    desactivateComparison,
    fontSize = '0.85rem',
    hideLabel = null,
    disableSameMonthValidation = null,
    allowCurrentDate = false
}) {
    const [dateRange, setDateRange] = useState([...mainDateRange]);
    const [disabled, setDisabled] = useState(false);
    const [message, setMessage] = useState('');

    const handleSearch = () => {
        setMainDateRange(dateRange);
        if (desactivateComparison) {
            desactivateComparison();
        }
    };

    useEffect(() => {
        if (isSameMonth(dateRange[0], dateRange[1])) {
            setDisabled(false);
            setMessage('');
        } else if (!disableSameMonthValidation) {
            setDisabled(true);
            setMessage('Las fechas deben pertenecer al mismo mes.');
        }
    }, [dateRange]);

    return (
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <DatePicker
                label={hideLabel ? null : 'Fecha inicio'}
                value={dateRange[0]}
                inputFormat="dd-MM-yyyy"
                mask="##-##-####"
                onChange={(newValue) => {
                    if (newValue && !Number.isNaN(new Date(newValue).getTime())) {
                        setDateRange([newValue, dateRange[1]]);
                    }
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        size="small"
                        sx={{
                            width: '170px',
                            '& .MuiOutlinedInput-root': {
                                borderRadius: '50px',
                                fontSize
                            }
                        }}
                    />
                )}
                maxDate={dateRange[1] || getTodayDate}
            />
            <DatePicker
                label={hideLabel ? null : 'Fecha fin'}
                value={dateRange[1]}
                inputFormat="dd-MM-yyyy"
                mask="##-##-####"
                onChange={(newValue) => {
                    if (newValue && !Number.isNaN(new Date(newValue).getTime())) {
                        setDateRange([dateRange[0], newValue]);
                    }
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        size="small"
                        sx={{
                            width: '170px',
                            '& .MuiOutlinedInput-root': {
                                borderRadius: '50px',
                                fontSize
                            }
                        }}
                    />
                )}
                minDate={dateRange[0]}
                maxDate={allowCurrentDate ? getTodayDate() : getYesterdayDate()}
            />
            <Box>
                <Tooltip title={message} disableInteractive={!disabled} followCursor>
                    <span>
                        <SubmitButton
                            variant="contained"
                            color="primary"
                            onClick={handleSearch}
                            size="small"
                            disabled={disabled}
                            sx={{ fontSize }}
                        >
                            Buscar
                        </SubmitButton>
                    </span>
                </Tooltip>
            </Box>
        </Box>
    );
}
