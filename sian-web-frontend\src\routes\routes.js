import { ROUTE_CONST } from 'data/global-constants';
// MODULES ROUTES
// Financial Route
export const financialRoute = '/financial';

// Accouns Route
export const accountsRoute = '/accounts';

// Restaurant Route
export const restaurantRoute = '/restaurant';

// Logistic Route
export const logisticRoute = '/logistic';

// Warehouse Route
export const warehouseRoute = '/warehouse';

// Commercial Route
export const commercialRoute = '/commercial';

// Dashboard Route
export const dashboardRoute = '/dashboard';

// Analitycs Route
export const analyticsRoute = '/analytics';

// General Route
export const generalRoute = '/general';

// Movements Route
export const movementsRoute = '/movements';

// Clasification Route
export const clasificationRoute = '/clasification';

// Orders Route
export const ordersRoute = '/orders';

// Reports Route
export const reportsRoute = '/reports';

// Administration Route
export const AdministrationRoute = '/administration';

// ACTIONS ROUTES
// Payment Schedule Routes
export const paymentScheduleRoute = `${financialRoute}${accountsRoute}/payment-schedule`;
export const paymentScheduleRouteCreate = `${paymentScheduleRoute}/${ROUTE_CONST.ACTIONS.CREATE}`;
export const paymentScheduleRouteView = (id) => `${paymentScheduleRoute}/${id}?action=${ROUTE_CONST.ACTIONS.VIEW}`;
export const paymentScheduleRouteEdit = (id) => `${paymentScheduleRoute}/${id}?action=${ROUTE_CONST.ACTIONS.UPDATE}`;

// Transacctions Routes
export const transactionRoute = `${financialRoute}${movementsRoute}/transaction`;
export const transactionRouteCreate = `${transactionRoute}/${ROUTE_CONST.ACTIONS.CREATE}`;

// Recipe Routes
export const recipeRoute = `${restaurantRoute}${generalRoute}/recipe`;
export const recipeRouteCreate = `${recipeRoute}/${ROUTE_CONST.ACTIONS.CREATE}`;
export const recipeRouteView = (id) => `${recipeRoute}/${id}?action=${ROUTE_CONST.ACTIONS.VIEW}`;
export const recipeRouteEdit = (id) => `${recipeRoute}/${id}?action=${ROUTE_CONST.ACTIONS.UPDATE}`;

// Production Costs Routes
export const productionCostRoute = `${restaurantRoute}${generalRoute}/production-cost`;
export const productionCostRouteCreate = `${productionCostRoute}/${ROUTE_CONST.ACTIONS.CREATE}`;
export const productionCostRouteView = (id) => `${productionCostRoute}/${id}?action=${ROUTE_CONST.ACTIONS.VIEW}`;
export const productionCostRouteEdit = (id) => `${productionCostRoute}/${id}?action=${ROUTE_CONST.ACTIONS.UPDATE}`;

export const foodSalesRoute = `${commercialRoute}${generalRoute}/food-sales`;

// Reposition Routes
export const repositionRoute = `${logisticRoute}${movementsRoute}/reposition`;
export const repositionRouteCreate = `${repositionRoute}/${ROUTE_CONST.ACTIONS.CREATE}`;

// Reposition Routes
export const supplyTransformationRoute = `${logisticRoute}${movementsRoute}/supply-transformation`;
export const supplyTransformationRouteCreate = `${supplyTransformationRoute}/${ROUTE_CONST.ACTIONS.CREATE}`;
export const getSupplyTransformationRouteCreate = (id) => `${supplyTransformationRoute}/${ROUTE_CONST.ACTIONS.CREATE}/${id}`;

// Dashboard Route
export const salesDashboardRoute = `${commercialRoute}${generalRoute}/sales-dashboard`;
export const salesDetailsRoute = `${commercialRoute}${generalRoute}/sales-dashboard-details`;

export const linkProductsRoute = `${logisticRoute}${clasificationRoute}/link-products`;
export const supplyStocksReportRoute = `${logisticRoute}${reportsRoute}/supply-stocks`;

export const administrationDashboardRoute = `${AdministrationRoute}${dashboardRoute}`;
export const approvalsDashboardRoute = `${administrationDashboardRoute}/approvals`;
